#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGeneration/NodeFactory.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNodeTypeRegistry, Log, All);

/**
 * Enumeration for node compatibility levels
 */
UENUM(BlueprintType)
enum class ENodeCompatibility : uint8
{
    Universal       UMETA(DisplayName = "Universal (All Graphs)"),
    EventGraph      UMETA(DisplayName = "Event Graph Only"),
    FunctionGraph   UMETA(DisplayName = "Function Graph Only"),
    MacroGraph      UMETA(DisplayName = "Macro Graph Only"),
    AnimGraph       UMETA(DisplayName = "Animation Graph Only"),
    WidgetGraph     UMETA(DisplayName = "Widget Graph Only"),
    Restricted      UMETA(DisplayName = "Restricted Use")
};

/**
 * Enumeration for node complexity levels
 */
UENUM(BlueprintType)
enum class ENodeComplexity : uint8
{
    Simple          UMETA(DisplayName = "Simple"),
    Moderate        UMETA(DisplayName = "Moderate"),
    Complex         UMETA(DisplayName = "Complex"),
    Advanced        UMETA(DisplayName = "Advanced"),
    Expert          UMETA(DisplayName = "Expert")
};

/**
 * Structure containing detailed node type information
 */
USTRUCT(BlueprintType)
struct FNODETYPEREGISTRYENTRY
{
    GENERATED_BODY()

    /** Unique identifier for the node type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString NodeTypeID;

    /** Display name for the node type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString DisplayName;

    /** Node class for instantiation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    UClass* NodeClass;

    /** Category classification */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    ENodeCategory Category;

    /** Compatibility level with different graph types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    ENodeCompatibility Compatibility;

    /** Complexity level of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    ENodeComplexity Complexity;

    /** Detailed description of the node's functionality */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString Description;

    /** Keywords for searching and filtering */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    TArray<FString> Keywords;

    /** Alternative names for the node type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    TArray<FString> Aliases;

    /** Required UE5 modules for this node type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    TArray<FString> RequiredModules;

    /** Supported graph types (empty means all) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    TArray<FString> SupportedGraphTypes;

    /** Default properties for node creation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    TMap<FString, FString> DefaultProperties;

    /** Function name for function call nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString FunctionName;

    /** Class name for function call nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString ClassName;

    /** Whether this node type requires special configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    bool bRequiresSpecialConfiguration;

    /** Whether this node type is deprecated */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    bool bIsDeprecated;

    /** Whether this node type is experimental */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    bool bIsExperimental;

    /** Usage frequency for optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    int32 UsageCount;

    /** Last used timestamp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FDateTime LastUsed;

    FNODETYPEREGISTRYENTRY()
        : NodeClass(nullptr)
        , Category(ENodeCategory::Custom)
        , Compatibility(ENodeCompatibility::Universal)
        , Complexity(ENodeComplexity::Simple)
        , bRequiresSpecialConfiguration(false)
        , bIsDeprecated(false)
        , bIsExperimental(false)
        , UsageCount(0)
        , LastUsed(FDateTime::MinValue())
    {
    }
};

/**
 * Structure for node search criteria
 */
USTRUCT(BlueprintType)
struct FNODESEARCHCRITERIA
{
    GENERATED_BODY()

    /** Search text for name, description, or keywords */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    FString SearchText;

    /** Filter by category */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    TArray<ENodeCategory> Categories;

    /** Filter by compatibility */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    TArray<ENodeCompatibility> CompatibilityLevels;

    /** Filter by complexity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    TArray<ENodeComplexity> ComplexityLevels;

    /** Filter by graph type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    FString GraphType;

    /** Include deprecated nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    bool bIncludeDeprecated;

    /** Include experimental nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    bool bIncludeExperimental;

    /** Maximum number of results */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search")
    int32 MaxResults;

    FNODESEARCHCRITERIA()
        : bIncludeDeprecated(false)
        , bIncludeExperimental(true)
        , MaxResults(50)
    {
    }
};

/**
 * Structure for node search results
 */
USTRUCT(BlueprintType)
struct FNODESEARCHRESULT
{
    GENERATED_BODY()

    /** Found node type entries */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search Result")
    TArray<FNODETYPEREGISTRYENTRY> FoundNodes;

    /** Total number of matches (may be more than returned) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search Result")
    int32 TotalMatches;

    /** Search execution time in milliseconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search Result")
    float SearchTimeMs;

    /** Whether the search was successful */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search Result")
    bool bSearchSuccessful;

    /** Error message if search failed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Search Result")
    FString ErrorMessage;

    FNODESEARCHRESULT()
        : TotalMatches(0)
        , SearchTimeMs(0.0f)
        , bSearchSuccessful(true)
    {
    }
};

/**
 * Class responsible for managing the comprehensive node type database
 */
class UE5BLUEPRINTGENERATOR_API FNodeTypeRegistry
{
public:
    FNodeTypeRegistry();
    ~FNodeTypeRegistry();

    // Initialization and management

    /**
     * Initializes the node type registry with built-in node types
     * @return True if initialization was successful
     */
    bool Initialize();

    /**
     * Shuts down the registry and cleans up resources
     */
    void Shutdown();

    /**
     * Reloads the registry from configuration files
     * @return True if reload was successful
     */
    bool Reload();

    // Node type registration

    /**
     * Registers a new node type in the registry
     * @param NodeEntry - The node type entry to register
     * @return True if registration was successful
     */
    bool RegisterNodeType(const FNODETYPEREGISTRYENTRY& NodeEntry);

    /**
     * Unregisters a node type from the registry
     * @param NodeTypeID - The ID of the node type to unregister
     * @return True if unregistration was successful
     */
    bool UnregisterNodeType(const FString& NodeTypeID);

    /**
     * Updates an existing node type entry
     * @param NodeTypeID - The ID of the node type to update
     * @param NodeEntry - The updated node type entry
     * @return True if update was successful
     */
    bool UpdateNodeType(const FString& NodeTypeID, const FNODETYPEREGISTRYENTRY& NodeEntry);

    // Node type retrieval

    /**
     * Gets a node type entry by ID
     * @param NodeTypeID - The ID of the node type to retrieve
     * @return The node type entry, or empty entry if not found
     */
    FNODETYPEREGISTRYENTRY GetNodeType(const FString& NodeTypeID);

    /**
     * Gets all registered node types
     * @return Array of all node type entries
     */
    TArray<FNODETYPEREGISTRYENTRY> GetAllNodeTypes();

    /**
     * Gets node types by category
     * @param Category - The category to filter by
     * @return Array of node type entries in the specified category
     */
    TArray<FNODETYPEREGISTRYENTRY> GetNodeTypesByCategory(ENodeCategory Category);

    /**
     * Gets node types by compatibility level
     * @param Compatibility - The compatibility level to filter by
     * @return Array of node type entries with the specified compatibility
     */
    TArray<FNODETYPEREGISTRYENTRY> GetNodeTypesByCompatibility(ENodeCompatibility Compatibility);

    /**
     * Gets node types by complexity level
     * @param Complexity - The complexity level to filter by
     * @return Array of node type entries with the specified complexity
     */
    TArray<FNODETYPEREGISTRYENTRY> GetNodeTypesByComplexity(ENodeComplexity Complexity);

    // Search and filtering

    /**
     * Searches for node types based on criteria
     * @param SearchCriteria - The search criteria to use
     * @return Search results containing matching node types
     */
    FNODESEARCHRESULT SearchNodeTypes(const FNODESEARCHCRITERIA& SearchCriteria);

    /**
     * Searches for node types by text
     * @param SearchText - Text to search for in names, descriptions, and keywords
     * @param MaxResults - Maximum number of results to return
     * @return Array of matching node type entries
     */
    TArray<FNODETYPEREGISTRYENTRY> SearchByText(const FString& SearchText, int32 MaxResults = 20);

    /**
     * Gets node type suggestions based on partial input
     * @param PartialInput - Partial node type name or keyword
     * @param MaxSuggestions - Maximum number of suggestions to return
     * @return Array of suggested node type entries
     */
    TArray<FNODETYPEREGISTRYENTRY> GetSuggestions(const FString& PartialInput, int32 MaxSuggestions = 10);

    // Compatibility checking

    /**
     * Checks if a node type is compatible with a graph type
     * @param NodeTypeID - The node type to check
     * @param GraphType - The graph type to check compatibility with
     * @return True if the node type is compatible with the graph type
     */
    bool IsNodeTypeCompatible(const FString& NodeTypeID, const FString& GraphType);

    /**
     * Gets compatible node types for a specific graph type
     * @param GraphType - The graph type to get compatible nodes for
     * @return Array of compatible node type entries
     */
    TArray<FNODETYPEREGISTRYENTRY> GetCompatibleNodeTypes(const FString& GraphType);

    /**
     * Validates node type requirements
     * @param NodeTypeID - The node type to validate
     * @return True if all requirements are met
     */
    bool ValidateNodeTypeRequirements(const FString& NodeTypeID);

    // Usage tracking and analytics

    /**
     * Records usage of a node type
     * @param NodeTypeID - The node type that was used
     */
    void RecordNodeTypeUsage(const FString& NodeTypeID);

    /**
     * Gets the most frequently used node types
     * @param Count - Number of top node types to return
     * @return Array of most used node type entries
     */
    TArray<FNODETYPEREGISTRYENTRY> GetMostUsedNodeTypes(int32 Count = 10);

    /**
     * Gets recently used node types
     * @param Count - Number of recent node types to return
     * @return Array of recently used node type entries
     */
    TArray<FNODETYPEREGISTRYENTRY> GetRecentlyUsedNodeTypes(int32 Count = 10);

    /**
     * Gets usage statistics for the registry
     * @return Map of statistics (total nodes, usage counts, etc.)
     */
    TMap<FString, int32> GetUsageStatistics();

    // Configuration and customization

    /**
     * Loads custom node types from configuration files
     * @param ConfigFilePath - Path to the configuration file
     * @return True if loading was successful
     */
    bool LoadCustomNodeTypes(const FString& ConfigFilePath);

    /**
     * Saves custom node types to configuration files
     * @param ConfigFilePath - Path to save the configuration file
     * @return True if saving was successful
     */
    bool SaveCustomNodeTypes(const FString& ConfigFilePath);

    /**
     * Imports node types from another registry
     * @param OtherRegistry - The registry to import from
     * @param bOverwriteExisting - Whether to overwrite existing entries
     * @return Number of node types imported
     */
    int32 ImportNodeTypes(const FNodeTypeRegistry& OtherRegistry, bool bOverwriteExisting = false);

    /**
     * Exports node types to another registry
     * @param OtherRegistry - The registry to export to
     * @param NodeTypeIDs - Specific node types to export (empty means all)
     * @return Number of node types exported
     */
    int32 ExportNodeTypes(FNodeTypeRegistry& OtherRegistry, const TArray<FString>& NodeTypeIDs = TArray<FString>());

    // Utility methods

    /**
     * Checks if a node type exists in the registry
     * @param NodeTypeID - The node type ID to check
     * @return True if the node type exists
     */
    bool HasNodeType(const FString& NodeTypeID);

    /**
     * Gets the total number of registered node types
     * @return Total count of registered node types
     */
    int32 GetNodeTypeCount();

    /**
     * Gets all available categories
     * @return Array of all categories that have registered node types
     */
    TArray<ENodeCategory> GetAvailableCategories();

    /**
     * Gets all available compatibility levels
     * @return Array of all compatibility levels that have registered node types
     */
    TArray<ENodeCompatibility> GetAvailableCompatibilityLevels();

    /**
     * Gets all available complexity levels
     * @return Array of all complexity levels that have registered node types
     */
    TArray<ENodeComplexity> GetAvailableComplexityLevels();

    /**
     * Converts node compatibility enum to string
     * @param Compatibility - The compatibility level to convert
     * @return String representation of the compatibility level
     */
    FString CompatibilityToString(ENodeCompatibility Compatibility);

    /**
     * Converts string to node compatibility enum
     * @param CompatibilityString - The string to convert
     * @return Corresponding compatibility level enum
     */
    ENodeCompatibility StringToCompatibility(const FString& CompatibilityString);

    /**
     * Converts node complexity enum to string
     * @param Complexity - The complexity level to convert
     * @return String representation of the complexity level
     */
    FString ComplexityToString(ENodeComplexity Complexity);

    /**
     * Converts string to node complexity enum
     * @param ComplexityString - The string to convert
     * @return Corresponding complexity level enum
     */
    ENodeComplexity StringToComplexity(const FString& ComplexityString);

    /**
     * Gets registry statistics for debugging
     * @return Map of registry statistics
     */
    TMap<FString, FString> GetRegistryStatistics();

private:
    // Core registry data

    /** Map of node type ID to registry entry */
    TMap<FString, FNODETYPEREGISTRYENTRY> NodeTypeRegistry;

    /** Map of aliases to node type IDs */
    TMap<FString, FString> AliasMap;

    /** Map of keywords to node type IDs */
    TMap<FString, TArray<FString>> KeywordMap;

    /** Map of categories to node type IDs */
    TMap<ENodeCategory, TArray<FString>> CategoryMap;

    /** Map of compatibility levels to node type IDs */
    TMap<ENodeCompatibility, TArray<FString>> CompatibilityMap;

    /** Map of complexity levels to node type IDs */
    TMap<ENodeComplexity, TArray<FString>> ComplexityMap;

    // Initialization methods

    /**
     * Initializes built-in flow control nodes
     */
    void InitializeFlowControlNodes();

    /**
     * Initializes built-in function nodes
     */
    void InitializeFunctionNodes();

    /**
     * Initializes built-in variable nodes
     */
    void InitializeVariableNodes();

    /**
     * Initializes built-in event nodes
     */
    void InitializeEventNodes();

    /**
     * Initializes built-in timer nodes
     */
    void InitializeTimerNodes();

    /**
     * Initializes built-in math nodes
     */
    void InitializeMathNodes();

    /**
     * Initializes built-in casting nodes
     */
    void InitializeCastingNodes();

    /**
     * Initializes built-in array nodes
     */
    void InitializeArrayNodes();

    /**
     * Initializes built-in string nodes
     */
    void InitializeStringNodes();

    /**
     * Initializes built-in debug nodes
     */
    void InitializeDebugNodes();

    /**
     * Initializes built-in input nodes
     */
    void InitializeInputNodes();

    /**
     * Initializes built-in utility nodes
     */
    void InitializeUtilityNodes();

    // Helper methods

    /**
     * Adds a node type to internal maps for fast lookup
     * @param NodeEntry - The node type entry to index
     */
    void IndexNodeType(const FNODETYPEREGISTRYENTRY& NodeEntry);

    /**
     * Removes a node type from internal maps
     * @param NodeTypeID - The node type ID to remove from indices
     */
    void RemoveFromIndices(const FString& NodeTypeID);

    /**
     * Rebuilds all internal indices
     */
    void RebuildIndices();

    /**
     * Validates a node type entry
     * @param NodeEntry - The entry to validate
     * @param OutErrorMessage - Error message if validation fails
     * @return True if the entry is valid
     */
    bool ValidateNodeEntry(const FNODETYPEREGISTRYENTRY& NodeEntry, FString& OutErrorMessage);

    /**
     * Calculates search relevance score
     * @param NodeEntry - The node entry to score
     * @param SearchCriteria - The search criteria
     * @return Relevance score (0.0-1.0)
     */
    float CalculateSearchRelevance(const FNODETYPEREGISTRYENTRY& NodeEntry, const FNODESEARCHCRITERIA& SearchCriteria);

    /**
     * Performs text matching for search
     * @param Text - The text to search in
     * @param SearchText - The text to search for
     * @return Match score (0.0-1.0)
     */
    float PerformTextMatch(const FString& Text, const FString& SearchText);

    // Logging and statistics

    /**
     * Logs registry activity
     * @param Message - The message to log
     * @param bIsError - Whether this is an error message
     */
    void LogRegistryActivity(const FString& Message, bool bIsError = false);

    /**
     * Updates registry statistics
     * @param Operation - The operation that was performed
     * @param NodeTypeID - The node type involved (optional)
     */
    void UpdateRegistryStatistics(const FString& Operation, const FString& NodeTypeID = TEXT(""));

    // Member variables

    /** Whether the registry has been initialized */
    bool bIsInitialized;

    /** Whether to enable verbose logging */
    bool bVerboseLogging;

    /** Whether to track usage statistics */
    bool bTrackUsageStatistics;

    /** Registry statistics for debugging and optimization */
    TMap<FString, int32> RegistryStatistics;

    /** Last search time for performance monitoring */
    float LastSearchTimeMs;

    /** Total number of searches performed */
    int32 TotalSearchCount;
}; 