#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "Templates/SharedPointer.h"
#include "Containers/Map.h"
#include "Containers/Array.h"
#include "HAL/CriticalSection.h"
#include "Misc/DateTime.h"
#include "CodeGeneration/CodeGenerationFramework.h"

DECLARE_LOG_CATEGORY_EXTERN(LogTemplateEngine, Log, All);

/**
 * Template type enumeration
 */
UENUM(BlueprintType)
enum class ETemplateType : uint8
{
    None,
    CppHeader,          // C++ header file template
    CppSource,          // C++ source file template
    CppClass,           // C++ class template
    CppFunction,        // C++ function template
    CppVariable,        // C++ variable template
    Documentation,      // Documentation template
    Project,            // Project file template
    CMake,              // CMake file template
    Custom              // Custom user-defined template
};

/**
 * Template format enumeration
 */
UENUM(BlueprintType)
enum class ETemplateFormat : uint8
{
    PlainText,          // Plain text with variable substitution
    Mustache,           // Mustache-style templates {{variable}}
    Jinja,              // Jinja-style templates {{ variable }}
    Custom              // Custom template format
};

/**
 * Variable data type enumeration for template processing
 */
UENUM(BlueprintType)
enum class ETemplateVariableType : uint8
{
    String,             // String variable
    Integer,            // Integer variable
    Float,              // Float variable
    Boolean,            // Boolean variable
    Array,              // Array of variables
    Object,             // Object/Map of variables
    Function            // Function/Lambda variable
};

/**
 * Template variable structure
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FTemplateVariable
{
    GENERATED_BODY()

    // Variable name
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    FString Name;

    // Variable type
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    ETemplateVariableType Type = ETemplateVariableType::String;

    // String value
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    FString StringValue;

    // Integer value
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    int32 IntegerValue = 0;

    // Float value
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    float FloatValue = 0.0f;

    // Boolean value
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    bool BooleanValue = false;

    // Array values (for array type)
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    TArray<FString> ArrayValues;

    // Object values (for object type)
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    TMap<FString, FString> ObjectValues;

    // Is variable required for template processing
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    bool bIsRequired = false;

    // Default value for optional variables
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    FString DefaultValue;

    // Variable description
    UPROPERTY(BlueprintReadWrite, Category = "Variable")
    FString Description;

    FTemplateVariable()
    {
        Type = ETemplateVariableType::String;
        IntegerValue = 0;
        FloatValue = 0.0f;
        BooleanValue = false;
        bIsRequired = false;
    }

    // Get value as string
    FString GetValueAsString() const;

    // Set value from string
    void SetValueFromString(const FString& Value);

    // Validate variable value
    bool IsValid() const;
};

/**
 * Template structure
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FTemplate
{
    GENERATED_BODY()

    // Template unique identifier
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FString Id;

    // Template name
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FString Name;

    // Template description
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FString Description;

    // Template type
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    ETemplateType Type = ETemplateType::Custom;

    // Template format
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    ETemplateFormat Format = ETemplateFormat::Mustache;

    // Template content
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FString Content;

    // Required variables for this template
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    TArray<FTemplateVariable> RequiredVariables;

    // Optional variables for this template
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    TArray<FTemplateVariable> OptionalVariables;

    // Template version
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FString Version;

    // Template author
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FString Author;

    // Template creation date
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FDateTime CreationDate;

    // Template modification date
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FDateTime ModificationDate;

    // Template tags for categorization
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    TArray<FString> Tags;

    // Template file path (if loaded from file)
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    FString FilePath;

    // Is template built-in or custom
    UPROPERTY(BlueprintReadWrite, Category = "Template")
    bool bIsBuiltIn = false;

    FTemplate()
    {
        Type = ETemplateType::Custom;
        Format = ETemplateFormat::Mustache;
        CreationDate = FDateTime::Now();
        ModificationDate = FDateTime::Now();
        bIsBuiltIn = false;
    }

    // Get all variables (required + optional)
    TArray<FTemplateVariable> GetAllVariables() const;

    // Find variable by name
    const FTemplateVariable* FindVariable(const FString& VariableName) const;

    // Validate template structure
    bool IsValid() const;

    // Get template hash for comparison
    FString GetTemplateHash() const;
};

/**
 * Template processing request
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FTemplateProcessingRequest
{
    GENERATED_BODY()

    // Template to process
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    FTemplate Template;

    // Variables for template processing
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    TArray<FTemplateVariable> Variables;

    // Processing options
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bStrictMode = true;

    // Enable conditional processing
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bEnableConditionals = true;

    // Enable loop processing
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bEnableLoops = true;

    // Enable function processing
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bEnableFunctions = true;

    // Maximum processing time in seconds
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    float MaxProcessingTime = 60.0f;

    // Request identifier
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    FString RequestId;

    FTemplateProcessingRequest()
    {
        bStrictMode = true;
        bEnableConditionals = true;
        bEnableLoops = true;
        bEnableFunctions = true;
        MaxProcessingTime = 60.0f;
        RequestId = FGuid::NewGuid().ToString();
    }

    // Get variable by name
    const FTemplateVariable* FindVariable(const FString& VariableName) const;

    // Add or update variable
    void SetVariable(const FTemplateVariable& Variable);

    // Validate request
    bool IsValid() const;
};

/**
 * Template processing result
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FTemplateProcessingResult
{
    GENERATED_BODY()

    // Whether processing was successful
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // Processed content
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ProcessedContent;

    // Processing errors
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Errors;

    // Processing warnings
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Warnings;

    // Variables that were used during processing
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> UsedVariables;

    // Variables that were missing during processing
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> MissingVariables;

    // Processing time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ProcessingTime = 0.0f;

    // Number of substitutions made
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 SubstitutionCount = 0;

    // Quality score of processing (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float QualityScore = 0.0f;

    FTemplateProcessingResult()
    {
        bSuccess = false;
        ProcessingTime = 0.0f;
        SubstitutionCount = 0;
        QualityScore = 0.0f;
    }
};

/**
 * Template engine configuration
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FTemplateEngineConfig
{
    GENERATED_BODY()

    // Default template format
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    ETemplateFormat DefaultFormat = ETemplateFormat::Mustache;

    // Enable template caching
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    bool bEnableCaching = true;

    // Maximum cache size (number of templates)
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    int32 MaxCacheSize = 100;

    // Cache expiration time in seconds
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    float CacheExpirationTime = 3600.0f;

    // Enable strict variable checking
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    bool bStrictVariableChecking = true;

    // Enable automatic type conversion
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    bool bEnableAutoTypeConversion = true;

    // Maximum recursion depth for nested templates
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    int32 MaxRecursionDepth = 10;

    // Template directory paths
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    TArray<FString> TemplateDirectories;

    // Custom variable delimiter start
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    FString VariableDelimiterStart = TEXT("{{");

    // Custom variable delimiter end
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    FString VariableDelimiterEnd = TEXT("}}");

    FTemplateEngineConfig()
    {
        DefaultFormat = ETemplateFormat::Mustache;
        bEnableCaching = true;
        MaxCacheSize = 100;
        CacheExpirationTime = 3600.0f;
        bStrictVariableChecking = true;
        bEnableAutoTypeConversion = true;
        MaxRecursionDepth = 10;
        VariableDelimiterStart = TEXT("{{");
        VariableDelimiterEnd = TEXT("}}");
    }
};

/**
 * Template engine statistics
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FTemplateEngineStatistics
{
    GENERATED_BODY()

    // Total number of templates processed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalTemplatesProcessed = 0;

    // Number of successful processing operations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulProcessing = 0;

    // Number of failed processing operations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedProcessing = 0;

    // Average processing time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageProcessingTime = 0.0f;

    // Fastest processing time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float FastestProcessingTime = 0.0f;

    // Slowest processing time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float SlowestProcessingTime = 0.0f;

    // Total number of registered templates
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalRegisteredTemplates = 0;

    // Number of cached templates
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 CachedTemplates = 0;

    // Cache hit rate (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float CacheHitRate = 0.0f;

    // Most used template type
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    ETemplateType MostUsedTemplateType = ETemplateType::Custom;

    // Most used template format
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    ETemplateFormat MostUsedTemplateFormat = ETemplateFormat::Mustache;

    FTemplateEngineStatistics()
    {
        TotalTemplatesProcessed = 0;
        SuccessfulProcessing = 0;
        FailedProcessing = 0;
        AverageProcessingTime = 0.0f;
        FastestProcessingTime = 0.0f;
        SlowestProcessingTime = 0.0f;
        TotalRegisteredTemplates = 0;
        CachedTemplates = 0;
        CacheHitRate = 0.0f;
        MostUsedTemplateType = ETemplateType::Custom;
        MostUsedTemplateFormat = ETemplateFormat::Mustache;
    }
};

/**
 * Delegate declarations for template engine events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTemplateProcessingStarted, const FTemplateProcessingRequest&, Request);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTemplateProcessingProgress, float, Progress, const FString&, CurrentTask);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTemplateProcessingCompleted, const FTemplateProcessingResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTemplateProcessingError, const FString&, ErrorMessage, const FTemplateProcessingRequest&, Request);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTemplateRegistered, const FTemplate&, Template);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTemplateUnregistered, const FString&, TemplateId);

/**
 * Template Engine - Comprehensive template processing system for code generation
 * 
 * This class provides a flexible and extensible template engine for generating code
 * from templates with variable substitution, conditional logic, and loops. It supports
 * multiple template formats and provides caching for improved performance.
 */
class UE5BLUEPRINTGENERATOR_API FTemplateEngine
{
public:
    FTemplateEngine();
    virtual ~FTemplateEngine();

    // Singleton access
    static FTemplateEngine& Get();

    // Core template operations
    bool ProcessTemplate(const FTemplateProcessingRequest& Request, FTemplateProcessingResult& OutResult);
    bool IsProcessingInProgress() const;
    void CancelProcessing();

    // Template management
    bool RegisterTemplate(const FTemplate& Template);
    bool UnregisterTemplate(const FString& TemplateId);
    bool UpdateTemplate(const FTemplate& Template);
    TArray<FTemplate> GetRegisteredTemplates() const;
    const FTemplate* FindTemplate(const FString& TemplateId) const;
    TArray<FTemplate> FindTemplatesByType(ETemplateType Type) const;
    TArray<FTemplate> FindTemplatesByTags(const TArray<FString>& Tags) const;

    // Template file operations
    bool LoadTemplateFromFile(const FString& FilePath, FTemplate& OutTemplate);
    bool SaveTemplateToFile(const FTemplate& Template, const FString& FilePath);
    bool LoadTemplatesFromDirectory(const FString& DirectoryPath);
    bool SaveTemplateToDirectory(const FTemplate& Template, const FString& DirectoryPath);

    // Built-in templates
    void RegisterBuiltInTemplates();
    TArray<FTemplate> GetBuiltInTemplates() const;

    // Configuration
    void SetEngineConfig(const FTemplateEngineConfig& Config);
    FTemplateEngineConfig GetEngineConfig() const;

    // Statistics
    FTemplateEngineStatistics GetStatistics() const;
    void ResetStatistics();

    // Validation
    bool ValidateTemplate(const FTemplate& Template, TArray<FString>& OutErrors) const;
    bool ValidateProcessingRequest(const FTemplateProcessingRequest& Request, TArray<FString>& OutErrors) const;

    // Cache management
    void ClearCache();
    int32 GetCacheSize() const;
    void SetCacheSize(int32 NewSize);

    // Event delegates
    FOnTemplateProcessingStarted OnTemplateProcessingStarted;
    FOnTemplateProcessingProgress OnTemplateProcessingProgress;
    FOnTemplateProcessingCompleted OnTemplateProcessingCompleted;
    FOnTemplateProcessingError OnTemplateProcessingError;
    FOnTemplateRegistered OnTemplateRegistered;
    FOnTemplateUnregistered OnTemplateUnregistered;

private:
    // Singleton instance
    static TSharedPtr<FTemplateEngine> Instance;

    // Configuration
    FTemplateEngineConfig Config;

    // Statistics
    FTemplateEngineStatistics Statistics;

    // Processing state
    bool bIsProcessingInProgress;
    FDateTime ProcessingStartTime;
    FTemplateProcessingRequest CurrentRequest;

    // Template registry
    TMap<FString, FTemplate> RegisteredTemplates;

    // Template cache
    struct FCachedTemplate
    {
        FTemplate Template;
        FDateTime CacheTime;
        int32 AccessCount;

        FCachedTemplate()
        {
            CacheTime = FDateTime::Now();
            AccessCount = 0;
        }
    };
    TMap<FString, FCachedTemplate> TemplateCache;

    // Thread safety
    mutable FCriticalSection CriticalSection;

    // Core processing methods
    bool InitializeProcessing(const FTemplateProcessingRequest& Request);
    bool ExecuteProcessing(const FTemplateProcessingRequest& Request, FTemplateProcessingResult& OutResult);
    bool FinalizeProcessing(const FTemplateProcessingResult& Result);

    // Template processing engines
    bool ProcessMustacheTemplate(const FTemplate& Template, const TArray<FTemplateVariable>& Variables, FString& OutContent);
    bool ProcessJinjaTemplate(const FTemplate& Template, const TArray<FTemplateVariable>& Variables, FString& OutContent);
    bool ProcessPlainTextTemplate(const FTemplate& Template, const TArray<FTemplateVariable>& Variables, FString& OutContent);

    // Variable processing
    FString ProcessVariableSubstitution(const FString& Content, const TArray<FTemplateVariable>& Variables, ETemplateFormat Format);
    FString ProcessConditionals(const FString& Content, const TArray<FTemplateVariable>& Variables);
    FString ProcessLoops(const FString& Content, const TArray<FTemplateVariable>& Variables);
    FString ProcessFunctions(const FString& Content, const TArray<FTemplateVariable>& Variables);

    // Variable helpers
    const FTemplateVariable* FindVariable(const FString& VariableName, const TArray<FTemplateVariable>& Variables) const;
    FString ConvertVariableToString(const FTemplateVariable& Variable) const;
    bool EvaluateCondition(const FString& Condition, const TArray<FTemplateVariable>& Variables) const;

    // Cache management
    void AddToCache(const FString& TemplateId, const FTemplate& Template);
    const FTemplate* GetFromCache(const FString& TemplateId);
    void RemoveFromCache(const FString& TemplateId);
    void CleanupCache();

    // Built-in template creation
    FTemplate CreateCppHeaderTemplate() const;
    FTemplate CreateCppSourceTemplate() const;
    FTemplate CreateCppClassTemplate() const;
    FTemplate CreateCppFunctionTemplate() const;
    FTemplate CreateCppVariableTemplate() const;
    FTemplate CreateDocumentationTemplate() const;
    FTemplate CreateProjectTemplate() const;
    FTemplate CreateCMakeTemplate() const;

    // Statistics helpers
    void UpdateStatistics(const FTemplateProcessingResult& Result);
    void UpdateTemplateUsageStatistics(ETemplateType Type, ETemplateFormat Format);

    // Event broadcasting
    void BroadcastProcessingStarted(const FTemplateProcessingRequest& Request);
    void BroadcastProcessingProgress(float Progress, const FString& CurrentTask);
    void BroadcastProcessingCompleted(const FTemplateProcessingResult& Result);
    void BroadcastProcessingError(const FString& ErrorMessage, const FTemplateProcessingRequest& Request);
    void BroadcastTemplateRegistered(const FTemplate& Template);
    void BroadcastTemplateUnregistered(const FString& TemplateId);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();
};

/**
 * Interface for custom template processors
 */
class UE5BLUEPRINTGENERATOR_API ITemplateProcessor
{
public:
    virtual ~ITemplateProcessor() = default;

    // Core processing interface
    virtual bool ProcessTemplate(const FTemplate& Template, const TArray<FTemplateVariable>& Variables, FString& OutContent) = 0;
    virtual bool CanProcessFormat(ETemplateFormat Format) const = 0;
    virtual ETemplateFormat GetSupportedFormat() const = 0;

    // Processor information
    virtual FString GetProcessorName() const = 0;
    virtual FString GetProcessorVersion() const = 0;
    virtual FString GetProcessorDescription() const = 0;

    // Validation
    virtual bool ValidateTemplate(const FTemplate& Template, TArray<FString>& OutErrors) const = 0;
    virtual bool ValidateVariables(const TArray<FTemplateVariable>& Variables, TArray<FString>& OutErrors) const = 0;
}; 