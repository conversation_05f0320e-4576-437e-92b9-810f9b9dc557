#include "CodeGeneration/BlueprintToCppConverter.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_FunctionEntry.h"
#include "BlueprintGraph/Classes/K2Node_FunctionResult.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_ComponentBoundEvent.h"
#include "EdGraphSchema_K2.h"
#include "KismetCompiler.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "Async/Async.h"

DEFINE_LOG_CATEGORY(LogBlueprintToCppConverter);

// Singleton instance
UBlueprintToCppConverter* UBlueprintToCppConverter::Instance = nullptr;

UBlueprintToCppConverter::UBlueprintToCppConverter()
{
    UE_LOG(LogBlueprintToCppConverter, Log, TEXT("Blueprint to C++ Converter initialized"));
}

UBlueprintToCppConverter* UBlueprintToCppConverter::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UBlueprintToCppConverter>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

FCppConversionResult UBlueprintToCppConverter::ConvertBlueprint(const FCppConversionRequest& Request)
{
    FScopeLock Lock(&CriticalSection);
    
    FCppConversionResult Result;
    Result.RequestId = Request.RequestId;
    
    double StartTime = FPlatformTime::Seconds();
    
    // Validate request
    FString ErrorMessage;
    if (!ValidateRequest(Request, ErrorMessage))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = ErrorMessage;
        UE_LOG(LogBlueprintToCppConverter, Error, TEXT("Request validation failed: %s"), *ErrorMessage);
        OnConversionError.Broadcast(Request.RequestId, ErrorMessage);
        return Result;
    }
    
    // Broadcast conversion started
    OnConversionStarted.Broadcast(Request.RequestId);
    
    try
    {
        UBlueprint* Blueprint = Request.Blueprint.Get();
        if (!Blueprint)
        {
            Result.bSuccess = false;
            Result.ErrorMessage = TEXT("Blueprint is null or has been garbage collected");
            OnConversionError.Broadcast(Request.RequestId, Result.ErrorMessage);
            return Result;
        }
        
        // Progress: 10%
        OnConversionProgress.Broadcast(Request.RequestId, 0.1f);
        
        // Analyze blueprint structure
        FCppClass CppClass = AnalyzeBlueprintStructure(Blueprint);
        
        // Progress: 40%
        OnConversionProgress.Broadcast(Request.RequestId, 0.4f);
        
        // Find best generator
        TSharedPtr<ICppCodeGenerator> Generator = FindBestGenerator(Request);
        if (Generator.IsValid())
        {
            // Use registered generator
            Result = Generator->GenerateCode(Request);
        }
        else
        {
            // Use built-in generation
            Result.GeneratedClass = CppClass;
            
            // Generate header file if needed
            if (Request.Config.ConversionMode == ECppConversionMode::HeaderOnly || 
                Request.Config.ConversionMode == ECppConversionMode::Complete)
            {
                Result.HeaderCode = GenerateHeaderFile(CppClass, Request.Config);
            }
            
            // Progress: 70%
            OnConversionProgress.Broadcast(Request.RequestId, 0.7f);
            
            // Generate implementation file if needed
            if (Request.Config.ConversionMode == ECppConversionMode::ImplementationOnly || 
                Request.Config.ConversionMode == ECppConversionMode::Complete)
            {
                Result.ImplementationCode = GenerateImplementationFile(CppClass, Request.Config);
            }
            
            // Progress: 90%
            OnConversionProgress.Broadcast(Request.RequestId, 0.9f);
            
            // Save files if output directory is specified
            if (!Request.Config.OutputDirectory.IsEmpty())
            {
                TArray<FString> GeneratedFiles;
                
                if (!Result.HeaderCode.IsEmpty())
                {
                    FString HeaderPath = FPaths::Combine(Request.Config.OutputDirectory, 
                        CppClass.Name + Request.Config.HeaderFileExtension);
                    if (FFileHelper::SaveStringToFile(Result.HeaderCode, *HeaderPath))
                    {
                        GeneratedFiles.Add(HeaderPath);
                    }
                }
                
                if (!Result.ImplementationCode.IsEmpty())
                {
                    FString ImplPath = FPaths::Combine(Request.Config.OutputDirectory, 
                        CppClass.Name + Request.Config.ImplementationFileExtension);
                    if (FFileHelper::SaveStringToFile(Result.ImplementationCode, *ImplPath))
                    {
                        GeneratedFiles.Add(ImplPath);
                    }
                }
                
                Result.GeneratedFiles = GeneratedFiles;
            }
            
            Result.bSuccess = true;
        }
        
        // Calculate metrics
        Result.ConversionTime = FPlatformTime::Seconds() - StartTime;
        Result.LinesOfCode = Result.HeaderCode.ParseIntoArray(TArray<FString>(), TEXT("\n")).Num() + 
                           Result.ImplementationCode.ParseIntoArray(TArray<FString>(), TEXT("\n")).Num();
        Result.QualityScore = CalculateQualityScore(CppClass, Request.Config);
        
        // Progress: 100%
        OnConversionProgress.Broadcast(Request.RequestId, 1.0f);
        
        // Update statistics
        UpdateStatistics(Result);
        
        // Broadcast completion
        OnConversionCompleted.Broadcast(Request.RequestId, Result);
        
        UE_LOG(LogBlueprintToCppConverter, Log, TEXT("Blueprint conversion completed successfully. Time: %.3fs, Lines: %d, Quality: %.2f"), 
            Result.ConversionTime, Result.LinesOfCode, Result.QualityScore);
    }
    catch (const std::exception& e)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Exception during conversion: %s"), UTF8_TO_TCHAR(e.what()));
        Result.ConversionTime = FPlatformTime::Seconds() - StartTime;
        
        UpdateStatistics(Result);
        OnConversionError.Broadcast(Request.RequestId, Result.ErrorMessage);
        
        UE_LOG(LogBlueprintToCppConverter, Error, TEXT("Exception during blueprint conversion: %s"), *Result.ErrorMessage);
    }
    
    return Result;
}

FString UBlueprintToCppConverter::ConvertBlueprintAsync(const FCppConversionRequest& Request)
{
    FString RequestId = Request.RequestId.IsEmpty() ? FGuid::NewGuid().ToString() : Request.RequestId;
    
    // Store request
    {
        FScopeLock Lock(&CriticalSection);
        FCppConversionRequest StoredRequest = Request;
        StoredRequest.RequestId = RequestId;
        StoredRequest.Timestamp = FPlatformTime::Seconds();
        ActiveRequests.Add(RequestId, StoredRequest);
    }
    
    // Execute asynchronously
    Async(EAsyncExecution::ThreadPool, [this, RequestId]()
    {
        FCppConversionRequest StoredRequest;
        {
            FScopeLock Lock(&CriticalSection);
            if (ActiveRequests.Contains(RequestId))
            {
                StoredRequest = ActiveRequests[RequestId];
            }
            else
            {
                return; // Request was cancelled
            }
        }
        
        FCppConversionResult Result = ConvertBlueprint(StoredRequest);
        
        // Remove from active requests
        {
            FScopeLock Lock(&CriticalSection);
            ActiveRequests.Remove(RequestId);
        }
    });
    
    return RequestId;
}

bool UBlueprintToCppConverter::RegisterGenerator(TSharedPtr<ICppCodeGenerator> Generator)
{
    if (!Generator.IsValid())
    {
        UE_LOG(LogBlueprintToCppConverter, Warning, TEXT("Attempted to register null generator"));
        return false;
    }
    
    FScopeLock Lock(&CriticalSection);
    
    // Check if generator with same name already exists
    for (const auto& ExistingGenerator : RegisteredGenerators)
    {
        if (ExistingGenerator.IsValid() && ExistingGenerator->GetGeneratorName() == Generator->GetGeneratorName())
        {
            UE_LOG(LogBlueprintToCppConverter, Warning, TEXT("Generator with name '%s' already registered"), 
                *Generator->GetGeneratorName());
            return false;
        }
    }
    
    RegisteredGenerators.Add(Generator);
    UE_LOG(LogBlueprintToCppConverter, Log, TEXT("Registered C++ generator: %s"), *Generator->GetGeneratorName());
    return true;
}

bool UBlueprintToCppConverter::UnregisterGenerator(const FString& GeneratorName)
{
    FScopeLock Lock(&CriticalSection);
    
    for (int32 i = RegisteredGenerators.Num() - 1; i >= 0; --i)
    {
        if (RegisteredGenerators[i].IsValid() && RegisteredGenerators[i]->GetGeneratorName() == GeneratorName)
        {
            RegisteredGenerators.RemoveAt(i);
            UE_LOG(LogBlueprintToCppConverter, Log, TEXT("Unregistered C++ generator: %s"), *GeneratorName);
            return true;
        }
    }
    
    UE_LOG(LogBlueprintToCppConverter, Warning, TEXT("Generator '%s' not found for unregistration"), *GeneratorName);
    return false;
}

TArray<FString> UBlueprintToCppConverter::GetAvailableGenerators() const
{
    FScopeLock Lock(&CriticalSection);
    
    TArray<FString> GeneratorNames;
    for (const auto& Generator : RegisteredGenerators)
    {
        if (Generator.IsValid())
        {
            GeneratorNames.Add(Generator->GetGeneratorName());
        }
    }
    
    return GeneratorNames;
}

bool UBlueprintToCppConverter::ValidateRequest(const FCppConversionRequest& Request, FString& ErrorMessage) const
{
    // Validate blueprint
    if (!Request.Blueprint.IsValid())
    {
        ErrorMessage = TEXT("Blueprint is null or invalid");
        return false;
    }
    
    UBlueprint* Blueprint = Request.Blueprint.Get();
    if (!Blueprint)
    {
        ErrorMessage = TEXT("Blueprint has been garbage collected");
        return false;
    }
    
    // Validate output directory if specified
    if (!Request.Config.OutputDirectory.IsEmpty())
    {
        if (!FPaths::DirectoryExists(Request.Config.OutputDirectory))
        {
            // Try to create directory
            if (!IFileManager::Get().MakeDirectory(*Request.Config.OutputDirectory, true))
            {
                ErrorMessage = FString::Printf(TEXT("Cannot create output directory: %s"), *Request.Config.OutputDirectory);
                return false;
            }
        }
    }
    
    // Validate file extensions
    if (Request.Config.HeaderFileExtension.IsEmpty())
    {
        ErrorMessage = TEXT("Header file extension cannot be empty");
        return false;
    }
    
    if (Request.Config.ImplementationFileExtension.IsEmpty())
    {
        ErrorMessage = TEXT("Implementation file extension cannot be empty");
        return false;
    }
    
    return true;
}

FCppConversionStatistics UBlueprintToCppConverter::GetStatistics() const
{
    FScopeLock Lock(&CriticalSection);
    return Statistics;
}

void UBlueprintToCppConverter::ResetStatistics()
{
    FScopeLock Lock(&CriticalSection);
    Statistics = FCppConversionStatistics();
    UE_LOG(LogBlueprintToCppConverter, Log, TEXT("Conversion statistics reset"));
}

bool UBlueprintToCppConverter::CancelConversion(const FString& RequestId)
{
    FScopeLock Lock(&CriticalSection);
    
    if (ActiveRequests.Contains(RequestId))
    {
        ActiveRequests.Remove(RequestId);
        UE_LOG(LogBlueprintToCppConverter, Log, TEXT("Cancelled conversion request: %s"), *RequestId);
        return true;
    }
    
    return false;
}

TArray<FString> UBlueprintToCppConverter::GetActiveRequests() const
{
    FScopeLock Lock(&CriticalSection);
    
    TArray<FString> RequestIds;
    ActiveRequests.GetKeys(RequestIds);
    return RequestIds;
}

FCppClass UBlueprintToCppConverter::AnalyzeBlueprintStructure(UBlueprint* Blueprint) const
{
    FCppClass CppClass;
    
    if (!Blueprint)
    {
        return CppClass;
    }
    
    // Set basic class information
    CppClass.Name = Blueprint->GetName();
    
    // Determine base class
    if (Blueprint->ParentClass)
    {
        CppClass.BaseClass = Blueprint->ParentClass->GetName();
    }
    else
    {
        CppClass.BaseClass = TEXT("UObject");
    }
    
    // Add standard includes
    CppClass.Includes.Add(TEXT("CoreMinimal.h"));
    CppClass.Includes.Add(TEXT("UObject/NoExportTypes.h"));
    
    if (CppClass.BaseClass != TEXT("UObject"))
    {
        CppClass.Includes.Add(FString::Printf(TEXT("%s.h"), *CppClass.BaseClass));
    }
    
    // Extract variables
    CppClass.Variables = ExtractVariables(Blueprint);
    
    // Extract functions
    CppClass.Functions = ExtractFunctions(Blueprint);
    
    // Add UCLASS specifier
    CppClass.Specifiers.Add(TEXT("BlueprintType"));
    CppClass.Specifiers.Add(TEXT("Blueprintable"));
    
    // Set comment
    CppClass.Comment = FString::Printf(TEXT("Generated from Blueprint: %s"), *Blueprint->GetName());
    
    return CppClass;
}

TArray<FCppVariable> UBlueprintToCppConverter::ExtractVariables(UBlueprint* Blueprint) const
{
    TArray<FCppVariable> Variables;
    
    if (!Blueprint || !Blueprint->GeneratedClass)
    {
        return Variables;
    }
    
    // Extract blueprint variables
    for (FBPVariableDescription& Variable : Blueprint->NewVariables)
    {
        FCppVariable CppVar;
        CppVar.Name = Variable.VarName.ToString();
        
        // Convert type
        if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_Boolean)
        {
            CppVar.Type = TEXT("bool");
        }
        else if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_Int)
        {
            CppVar.Type = TEXT("int32");
        }
        else if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_Float)
        {
            CppVar.Type = TEXT("float");
        }
        else if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_String)
        {
            CppVar.Type = TEXT("FString");
        }
        else if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_Name)
        {
            CppVar.Type = TEXT("FName");
        }
        else if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_Text)
        {
            CppVar.Type = TEXT("FText");
        }
        else if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_Object)
        {
            if (Variable.VarType.PinSubCategoryObject.IsValid())
            {
                CppVar.Type = FString::Printf(TEXT("%s*"), *Variable.VarType.PinSubCategoryObject->GetName());
                CppVar.bIsPointer = true;
            }
            else
            {
                CppVar.Type = TEXT("UObject*");
                CppVar.bIsPointer = true;
            }
        }
        else if (Variable.VarType.PinCategory == UEdGraphSchema_K2::PC_Struct)
        {
            if (Variable.VarType.PinSubCategoryObject.IsValid())
            {
                CppVar.Type = FString::Printf(TEXT("F%s"), *Variable.VarType.PinSubCategoryObject->GetName());
            }
            else
            {
                CppVar.Type = TEXT("FStruct");
            }
        }
        else
        {
            CppVar.Type = TEXT("auto"); // Fallback
        }
        
        // Handle arrays
        if (Variable.VarType.ContainerType == EPinContainerType::Array)
        {
            CppVar.Type = FString::Printf(TEXT("TArray<%s>"), *CppVar.Type);
            CppVar.bIsArray = true;
        }
        
        // Set access pattern based on blueprint exposure
        if (Variable.PropertyFlags & CPF_BlueprintReadWrite)
        {
            CppVar.AccessPattern = ECppAccessPattern::BlueprintReadWrite;
            CppVar.Specifiers.Add(TEXT("BlueprintReadWrite"));
        }
        else if (Variable.PropertyFlags & CPF_BlueprintReadOnly)
        {
            CppVar.AccessPattern = ECppAccessPattern::BlueprintReadOnly;
            CppVar.Specifiers.Add(TEXT("BlueprintReadOnly"));
        }
        else
        {
            CppVar.AccessPattern = ECppAccessPattern::Private;
        }
        
        // Add UPROPERTY specifiers
        if (Variable.PropertyFlags & CPF_Edit)
        {
            if (Variable.PropertyFlags & CPF_DisableEditOnInstance)
            {
                CppVar.Specifiers.Add(TEXT("EditDefaultsOnly"));
            }
            else
            {
                CppVar.Specifiers.Add(TEXT("EditAnywhere"));
            }
        }
        
        // Set category
        if (!Variable.Category.IsEmpty())
        {
            CppVar.Specifiers.Add(FString::Printf(TEXT("Category = \"%s\""), *Variable.Category.ToString()));
        }
        
        // Set comment
        if (!Variable.FriendlyName.IsEmpty())
        {
            CppVar.Comment = Variable.FriendlyName;
        }
        
        Variables.Add(CppVar);
    }
    
    return Variables;
}

TArray<FCppFunction> UBlueprintToCppConverter::ExtractFunctions(UBlueprint* Blueprint) const
{
    TArray<FCppFunction> Functions;
    
    if (!Blueprint)
    {
        return Functions;
    }
    
    // Extract blueprint functions
    TArray<UEdGraph*> FunctionGraphs;
    Blueprint->GetAllGraphs(FunctionGraphs);
    
    for (UEdGraph* Graph : FunctionGraphs)
    {
        if (Graph->GetSchema()->GetGraphType(Graph) == GT_Function)
        {
            FCppFunction CppFunc;
            CppFunc.Name = Graph->GetFName().ToString();
            
            // Find function entry node
            UK2Node_FunctionEntry* EntryNode = nullptr;
            for (UEdGraphNode* Node : Graph->Nodes)
            {
                if (UK2Node_FunctionEntry* Entry = Cast<UK2Node_FunctionEntry>(Node))
                {
                    EntryNode = Entry;
                    break;
                }
            }
            
            if (EntryNode)
            {
                // Extract parameters
                for (UEdGraphPin* Pin : EntryNode->Pins)
                {
                    if (Pin->Direction == EGPD_Output && Pin->PinType.PinCategory != UEdGraphSchema_K2::PC_Exec)
                    {
                        FCppVariable Param;
                        Param.Name = Pin->PinName.ToString();
                        
                        // Convert pin type to C++ type
                        if (Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Boolean)
                        {
                            Param.Type = TEXT("bool");
                        }
                        else if (Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Int)
                        {
                            Param.Type = TEXT("int32");
                        }
                        else if (Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Float)
                        {
                            Param.Type = TEXT("float");
                        }
                        else if (Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_String)
                        {
                            Param.Type = TEXT("const FString&");
                            Param.bIsReference = true;
                            Param.bIsConst = true;
                        }
                        else
                        {
                            Param.Type = TEXT("auto");
                        }
                        
                        CppFunc.Parameters.Add(Param);
                    }
                }
                
                // Check if function is pure
                CppFunc.bIsPure = EntryNode->GetFunctionFlags() & FUNC_BlueprintPure;
                
                // Set access pattern
                if (EntryNode->GetFunctionFlags() & FUNC_BlueprintCallable)
                {
                    CppFunc.AccessPattern = ECppAccessPattern::BlueprintReadWrite;
                    CppFunc.Specifiers.Add(TEXT("BlueprintCallable"));
                }
                
                if (CppFunc.bIsPure)
                {
                    CppFunc.Specifiers.Add(TEXT("BlueprintPure"));
                }
            }
            
            // Generate basic implementation
            CppFunc.Implementation = FString::Printf(TEXT("{\n\t// TODO: Implement %s\n}"), *CppFunc.Name);
            
            Functions.Add(CppFunc);
        }
    }
    
    return Functions;
}

FString UBlueprintToCppConverter::ConvertNodeToCpp(UK2Node* Node, const FCppConversionConfig& Config) const
{
    if (!Node)
    {
        return TEXT("");
    }
    
    FString CppCode;
    
    // Handle different node types
    if (UK2Node_VariableGet* VarGet = Cast<UK2Node_VariableGet>(Node))
    {
        CppCode = VarGet->GetVarName().ToString();
    }
    else if (UK2Node_VariableSet* VarSet = Cast<UK2Node_VariableSet>(Node))
    {
        CppCode = FString::Printf(TEXT("%s = /* value */"), *VarSet->GetVarName().ToString());
    }
    else if (UK2Node_CallFunction* FuncCall = Cast<UK2Node_CallFunction>(Node))
    {
        if (FuncCall->GetTargetFunction())
        {
            CppCode = FString::Printf(TEXT("%s(/* parameters */)"), *FuncCall->GetTargetFunction()->GetName());
        }
    }
    else if (UK2Node_Event* Event = Cast<UK2Node_Event>(Node))
    {
        CppCode = FString::Printf(TEXT("// Event: %s"), *Event->GetNodeTitle(ENodeTitleType::FullTitle).ToString());
    }
    else
    {
        CppCode = FString::Printf(TEXT("// Node: %s"), *Node->GetClass()->GetName());
    }
    
    return CppCode;
}

FString UBlueprintToCppConverter::GenerateHeaderFile(const FCppClass& CppClass, const FCppConversionConfig& Config) const
{
    FString HeaderCode;
    
    // Header guard
    FString HeaderGuard = FString::Printf(TEXT("%s_H"), *CppClass.Name.ToUpper());
    HeaderCode += FString::Printf(TEXT("#pragma once\n\n"));
    
    // Includes
    for (const FString& Include : CppClass.Includes)
    {
        HeaderCode += FString::Printf(TEXT("#include \"%s\"\n"), *Include);
    }
    HeaderCode += TEXT("\n");
    
    // Forward declarations
    for (const FString& ForwardDecl : CppClass.ForwardDeclarations)
    {
        HeaderCode += FString::Printf(TEXT("class %s;\n"), *ForwardDecl);
    }
    if (CppClass.ForwardDeclarations.Num() > 0)
    {
        HeaderCode += TEXT("\n");
    }
    
    // Class declaration
    if (Config.bGenerateComments && !CppClass.Comment.IsEmpty())
    {
        HeaderCode += FString::Printf(TEXT("/**\n * %s\n */\n"), *CppClass.Comment);
    }
    
    // UCLASS macro
    if (CppClass.Specifiers.Num() > 0)
    {
        HeaderCode += TEXT("UCLASS(");
        for (int32 i = 0; i < CppClass.Specifiers.Num(); ++i)
        {
            HeaderCode += CppClass.Specifiers[i];
            if (i < CppClass.Specifiers.Num() - 1)
            {
                HeaderCode += TEXT(", ");
            }
        }
        HeaderCode += TEXT(")\n");
    }
    
    // Class definition
    HeaderCode += FString::Printf(TEXT("class BLUEPRINTGENERATOR_API %s : public %s\n{\n"), 
        *CppClass.Name, *CppClass.BaseClass);
    HeaderCode += TEXT("\tGENERATED_BODY()\n\n");
    
    // Constructor
    HeaderCode += TEXT("public:\n");
    HeaderCode += FString::Printf(TEXT("\t%s();\n\n"), *CppClass.Name);
    
    // Variables
    if (CppClass.Variables.Num() > 0)
    {
        ECppAccessPattern CurrentAccess = ECppAccessPattern::Public;
        
        for (const FCppVariable& Variable : CppClass.Variables)
        {
            // Change access level if needed
            if (Variable.AccessPattern != CurrentAccess)
            {
                CurrentAccess = Variable.AccessPattern;
                switch (CurrentAccess)
                {
                case ECppAccessPattern::Public:
                case ECppAccessPattern::BlueprintReadOnly:
                case ECppAccessPattern::BlueprintReadWrite:
                    HeaderCode += TEXT("public:\n");
                    break;
                case ECppAccessPattern::Protected:
                    HeaderCode += TEXT("protected:\n");
                    break;
                case ECppAccessPattern::Private:
                    HeaderCode += TEXT("private:\n");
                    break;
                }
            }
            
            // Variable comment
            if (Config.bGenerateComments && !Variable.Comment.IsEmpty())
            {
                HeaderCode += FString::Printf(TEXT("\t/** %s */\n"), *Variable.Comment);
            }
            
            // UPROPERTY macro
            if (Variable.Specifiers.Num() > 0)
            {
                HeaderCode += TEXT("\tUPROPERTY(");
                for (int32 i = 0; i < Variable.Specifiers.Num(); ++i)
                {
                    HeaderCode += Variable.Specifiers[i];
                    if (i < Variable.Specifiers.Num() - 1)
                    {
                        HeaderCode += TEXT(", ");
                    }
                }
                HeaderCode += TEXT(")\n");
            }
            
            // Variable declaration
            HeaderCode += FString::Printf(TEXT("\t%s %s"), *Variable.Type, *Variable.Name);
            if (!Variable.DefaultValue.IsEmpty())
            {
                HeaderCode += FString::Printf(TEXT(" = %s"), *Variable.DefaultValue);
            }
            HeaderCode += TEXT(";\n\n");
        }
    }
    
    // Functions
    if (CppClass.Functions.Num() > 0)
    {
        ECppAccessPattern CurrentAccess = ECppAccessPattern::Public;
        
        for (const FCppFunction& Function : CppClass.Functions)
        {
            // Change access level if needed
            if (Function.AccessPattern != CurrentAccess)
            {
                CurrentAccess = Function.AccessPattern;
                switch (CurrentAccess)
                {
                case ECppAccessPattern::Public:
                case ECppAccessPattern::BlueprintReadOnly:
                case ECppAccessPattern::BlueprintReadWrite:
                    HeaderCode += TEXT("public:\n");
                    break;
                case ECppAccessPattern::Protected:
                    HeaderCode += TEXT("protected:\n");
                    break;
                case ECppAccessPattern::Private:
                    HeaderCode += TEXT("private:\n");
                    break;
                }
            }
            
            // Function comment
            if (Config.bGenerateComments && !Function.Comment.IsEmpty())
            {
                HeaderCode += FString::Printf(TEXT("\t/** %s */\n"), *Function.Comment);
            }
            
            // UFUNCTION macro
            if (Function.Specifiers.Num() > 0)
            {
                HeaderCode += TEXT("\tUFUNCTION(");
                for (int32 i = 0; i < Function.Specifiers.Num(); ++i)
                {
                    HeaderCode += Function.Specifiers[i];
                    if (i < Function.Specifiers.Num() - 1)
                    {
                        HeaderCode += TEXT(", ");
                    }
                }
                HeaderCode += TEXT(")\n");
            }
            
            // Function declaration
            HeaderCode += FString::Printf(TEXT("\t%s %s("), *Function.ReturnType, *Function.Name);
            
            // Parameters
            for (int32 i = 0; i < Function.Parameters.Num(); ++i)
            {
                const FCppVariable& Param = Function.Parameters[i];
                HeaderCode += FString::Printf(TEXT("%s %s"), *Param.Type, *Param.Name);
                if (i < Function.Parameters.Num() - 1)
                {
                    HeaderCode += TEXT(", ");
                }
            }
            
            HeaderCode += TEXT(")");
            
            // Function modifiers
            if (Function.bIsConst)
            {
                HeaderCode += TEXT(" const");
            }
            if (Function.bIsOverride)
            {
                HeaderCode += TEXT(" override");
            }
            
            HeaderCode += TEXT(";\n\n");
        }
    }
    
    // Close class
    HeaderCode += TEXT("};\n");
    
    return FormatCode(HeaderCode, Config);
}

FString UBlueprintToCppConverter::GenerateImplementationFile(const FCppClass& CppClass, const FCppConversionConfig& Config) const
{
    FString ImplCode;
    
    // Include header
    ImplCode += FString::Printf(TEXT("#include \"%s%s\"\n\n"), *CppClass.Name, *Config.HeaderFileExtension);
    
    // Constructor implementation
    ImplCode += FString::Printf(TEXT("%s::%s()\n{\n"), *CppClass.Name, *CppClass.Name);
    ImplCode += TEXT("\t// Set default values\n");
    
    // Initialize variables with default values
    for (const FCppVariable& Variable : CppClass.Variables)
    {
        if (!Variable.DefaultValue.IsEmpty())
        {
            ImplCode += FString::Printf(TEXT("\t%s = %s;\n"), *Variable.Name, *Variable.DefaultValue);
        }
    }
    
    ImplCode += TEXT("}\n\n");
    
    // Function implementations
    for (const FCppFunction& Function : CppClass.Functions)
    {
        // Function signature
        ImplCode += FString::Printf(TEXT("%s %s::%s("), *Function.ReturnType, *CppClass.Name, *Function.Name);
        
        // Parameters
        for (int32 i = 0; i < Function.Parameters.Num(); ++i)
        {
            const FCppVariable& Param = Function.Parameters[i];
            ImplCode += FString::Printf(TEXT("%s %s"), *Param.Type, *Param.Name);
            if (i < Function.Parameters.Num() - 1)
            {
                ImplCode += TEXT(", ");
            }
        }
        
        ImplCode += TEXT(")");
        
        // Function modifiers
        if (Function.bIsConst)
        {
            ImplCode += TEXT(" const");
        }
        
        ImplCode += TEXT("\n");
        
        // Function body
        if (!Function.Implementation.IsEmpty())
        {
            ImplCode += Function.Implementation;
        }
        else
        {
            ImplCode += TEXT("{\n\t// TODO: Implement function\n}");
        }
        
        ImplCode += TEXT("\n\n");
    }
    
    return FormatCode(ImplCode, Config);
}

FString UBlueprintToCppConverter::FormatCode(const FString& Code, const FCppConversionConfig& Config) const
{
    FString FormattedCode = Code;
    
    // Apply formatting based on config
    if (Config.bUseTabs)
    {
        // Convert spaces to tabs (basic implementation)
        FormattedCode = FormattedCode.Replace(TEXT("    "), TEXT("\t"));
    }
    
    // Line length formatting (basic implementation)
    if (Config.MaxLineLength > 0)
    {
        // This would need more sophisticated implementation for proper line wrapping
    }
    
    return FormattedCode;
}

float UBlueprintToCppConverter::CalculateQualityScore(const FCppClass& CppClass, const FCppConversionConfig& Config) const
{
    float Score = 0.0f;
    float MaxScore = 0.0f;
    
    // Base score for successful conversion
    Score += 20.0f;
    MaxScore += 20.0f;
    
    // Score for variables
    Score += CppClass.Variables.Num() * 5.0f;
    MaxScore += CppClass.Variables.Num() * 5.0f;
    
    // Score for functions
    Score += CppClass.Functions.Num() * 10.0f;
    MaxScore += CppClass.Functions.Num() * 10.0f;
    
    // Score for comments
    if (Config.bGenerateComments)
    {
        Score += 10.0f;
    }
    MaxScore += 10.0f;
    
    // Score for documentation
    if (Config.bGenerateDocumentation)
    {
        Score += 10.0f;
    }
    MaxScore += 10.0f;
    
    // Score for proper includes
    Score += CppClass.Includes.Num() * 2.0f;
    MaxScore += CppClass.Includes.Num() * 2.0f;
    
    return MaxScore > 0.0f ? FMath::Clamp(Score / MaxScore, 0.0f, 1.0f) : 0.0f;
}

void UBlueprintToCppConverter::UpdateStatistics(const FCppConversionResult& Result)
{
    Statistics.TotalConversions++;
    
    if (Result.bSuccess)
    {
        Statistics.SuccessfulConversions++;
        Statistics.TotalLinesGenerated += Result.LinesOfCode;
        
        // Update averages
        float TotalTime = Statistics.AverageConversionTime * (Statistics.SuccessfulConversions - 1) + Result.ConversionTime;
        Statistics.AverageConversionTime = TotalTime / Statistics.SuccessfulConversions;
        
        float TotalQuality = Statistics.AverageQualityScore * (Statistics.SuccessfulConversions - 1) + Result.QualityScore;
        Statistics.AverageQualityScore = TotalQuality / Statistics.SuccessfulConversions;
    }
    else
    {
        Statistics.FailedConversions++;
    }
}

TSharedPtr<ICppCodeGenerator> UBlueprintToCppConverter::FindBestGenerator(const FCppConversionRequest& Request) const
{
    TSharedPtr<ICppCodeGenerator> BestGenerator;
    int32 BestPriority = -1;
    
    for (const auto& Generator : RegisteredGenerators)
    {
        if (Generator.IsValid() && Generator->CanHandleRequest(Request))
        {
            int32 Priority = Generator->GetPriority();
            if (Priority > BestPriority)
            {
                BestPriority = Priority;
                BestGenerator = Generator;
            }
        }
    }
    
    return BestGenerator;
} 