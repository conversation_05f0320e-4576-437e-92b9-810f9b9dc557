#include "BlueprintGeneration/NodeTypeRegistry.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_CustomEvent.h"
#include "BlueprintGraph/Classes/K2Node_IfThenElse.h"
#include "BlueprintGraph/Classes/K2Node_ExecutionSequence.h"
#include "BlueprintGraph/Classes/K2Node_ForLoop.h"
#include "BlueprintGraph/Classes/K2Node_WhileLoop.h"
#include "BlueprintGraph/Classes/K2Node_Switch.h"
#include "BlueprintGraph/Classes/K2Node_Delay.h"
#include "BlueprintGraph/Classes/K2Node_Timeline.h"
#include "BlueprintGraph/Classes/K2Node_DynamicCast.h"
#include "BlueprintGraph/Classes/K2Node_ForEachElementInArray.h"
#include "BlueprintGraph/Classes/K2Node_Self.h"
#include "BlueprintGraph/Classes/K2Node_InputAction.h"
#include "BlueprintGraph/Classes/K2Node_MacroInstance.h"
#include "BlueprintGraph/Classes/K2Node_CommutativeAssociativeBinaryOperator.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"

DEFINE_LOG_CATEGORY(LogNodeTypeRegistry);

FNodeTypeRegistry::FNodeTypeRegistry()
    : bIsInitialized(false)
    , bVerboseLogging(false)
    , bTrackUsageStatistics(true)
    , LastSearchTimeMs(0.0f)
    , TotalSearchCount(0)
{
    UE_LOG(LogNodeTypeRegistry, Log, TEXT("NodeTypeRegistry created"));
}

FNodeTypeRegistry::~FNodeTypeRegistry()
{
    Shutdown();
    UE_LOG(LogNodeTypeRegistry, Log, TEXT("NodeTypeRegistry destroyed"));
}

bool FNodeTypeRegistry::Initialize()
{
    if (bIsInitialized)
    {
        UE_LOG(LogNodeTypeRegistry, Warning, TEXT("NodeTypeRegistry already initialized"));
        return true;
    }

    UE_LOG(LogNodeTypeRegistry, Log, TEXT("Initializing NodeTypeRegistry..."));

    // Clear existing data
    NodeTypeRegistry.Empty();
    AliasMap.Empty();
    KeywordMap.Empty();
    CategoryMap.Empty();
    CompatibilityMap.Empty();
    ComplexityMap.Empty();
    RegistryStatistics.Empty();

    // Initialize built-in node types
    InitializeFlowControlNodes();
    InitializeFunctionNodes();
    InitializeVariableNodes();
    InitializeEventNodes();
    InitializeTimerNodes();
    InitializeMathNodes();
    InitializeCastingNodes();
    InitializeArrayNodes();
    InitializeStringNodes();
    InitializeDebugNodes();
    InitializeInputNodes();
    InitializeUtilityNodes();

    // Rebuild indices for fast lookup
    RebuildIndices();

    bIsInitialized = true;
    
    UE_LOG(LogNodeTypeRegistry, Log, TEXT("NodeTypeRegistry initialized with %d node types"), NodeTypeRegistry.Num());
    LogRegistryActivity(FString::Printf(TEXT("Registry initialized with %d node types"), NodeTypeRegistry.Num()));

    return true;
}

void FNodeTypeRegistry::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    UE_LOG(LogNodeTypeRegistry, Log, TEXT("Shutting down NodeTypeRegistry..."));

    // Clear all data
    NodeTypeRegistry.Empty();
    AliasMap.Empty();
    KeywordMap.Empty();
    CategoryMap.Empty();
    CompatibilityMap.Empty();
    ComplexityMap.Empty();
    RegistryStatistics.Empty();

    bIsInitialized = false;
    
    UE_LOG(LogNodeTypeRegistry, Log, TEXT("NodeTypeRegistry shutdown complete"));
}

bool FNodeTypeRegistry::RegisterNodeType(const FNODETYPEREGISTRYENTRY& NodeEntry)
{
    if (!bIsInitialized)
    {
        UE_LOG(LogNodeTypeRegistry, Error, TEXT("Cannot register node type - registry not initialized"));
        return false;
    }

    FString ErrorMessage;
    if (!ValidateNodeEntry(NodeEntry, ErrorMessage))
    {
        UE_LOG(LogNodeTypeRegistry, Error, TEXT("Failed to register node type '%s': %s"), *NodeEntry.NodeTypeID, *ErrorMessage);
        return false;
    }

    // Check for existing entry
    if (NodeTypeRegistry.Contains(NodeEntry.NodeTypeID))
    {
        UE_LOG(LogNodeTypeRegistry, Warning, TEXT("Node type '%s' already exists - updating"), *NodeEntry.NodeTypeID);
        RemoveFromIndices(NodeEntry.NodeTypeID);
    }

    // Add to registry
    NodeTypeRegistry.Add(NodeEntry.NodeTypeID, NodeEntry);
    
    // Update indices
    IndexNodeType(NodeEntry);

    LogRegistryActivity(FString::Printf(TEXT("Registered node type: %s"), *NodeEntry.NodeTypeID));
    UpdateRegistryStatistics(TEXT("Register"), NodeEntry.NodeTypeID);

    return true;
}

bool FNodeTypeRegistry::UnregisterNodeType(const FString& NodeTypeID)
{
    if (!bIsInitialized)
    {
        UE_LOG(LogNodeTypeRegistry, Error, TEXT("Cannot unregister node type - registry not initialized"));
        return false;
    }

    if (!NodeTypeRegistry.Contains(NodeTypeID))
    {
        UE_LOG(LogNodeTypeRegistry, Warning, TEXT("Node type '%s' not found for unregistration"), *NodeTypeID);
        return false;
    }

    // Remove from indices
    RemoveFromIndices(NodeTypeID);
    
    // Remove from registry
    NodeTypeRegistry.Remove(NodeTypeID);

    LogRegistryActivity(FString::Printf(TEXT("Unregistered node type: %s"), *NodeTypeID));
    UpdateRegistryStatistics(TEXT("Unregister"), NodeTypeID);

    return true;
}

FNODETYPEREGISTRYENTRY FNodeTypeRegistry::GetNodeType(const FString& NodeTypeID)
{
    if (NodeTypeRegistry.Contains(NodeTypeID))
    {
        return NodeTypeRegistry[NodeTypeID];
    }
    
    return FNODETYPEREGISTRYENTRY(); // Return empty entry if not found
}

TArray<FNODETYPEREGISTRYENTRY> FNodeTypeRegistry::GetAllNodeTypes()
{
    TArray<FNODETYPEREGISTRYENTRY> AllNodeTypes;
    NodeTypeRegistry.GenerateValueArray(AllNodeTypes);
    return AllNodeTypes;
}

TArray<FNODETYPEREGISTRYENTRY> FNodeTypeRegistry::GetNodeTypesByCategory(ENodeCategory Category)
{
    TArray<FNODETYPEREGISTRYENTRY> CategoryNodeTypes;
    
    if (CategoryMap.Contains(Category))
    {
        const TArray<FString>& NodeTypeIDs = CategoryMap[Category];
        for (const FString& NodeTypeID : NodeTypeIDs)
        {
            if (NodeTypeRegistry.Contains(NodeTypeID))
            {
                CategoryNodeTypes.Add(NodeTypeRegistry[NodeTypeID]);
            }
        }
    }
    
    return CategoryNodeTypes;
}

FNODESEARCHRESULT FNodeTypeRegistry::SearchNodeTypes(const FNODESEARCHCRITERIA& SearchCriteria)
{
    FNODESEARCHRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    TArray<TPair<FNODETYPEREGISTRYENTRY, float>> ScoredResults;

    // Search through all node types
    for (const auto& Pair : NodeTypeRegistry)
    {
        const FNODETYPEREGISTRYENTRY& NodeEntry = Pair.Value;
        
        // Skip deprecated nodes if not included
        if (NodeEntry.bIsDeprecated && !SearchCriteria.bIncludeDeprecated)
        {
            continue;
        }
        
        // Skip experimental nodes if not included
        if (NodeEntry.bIsExperimental && !SearchCriteria.bIncludeExperimental)
        {
            continue;
        }

        // Calculate relevance score
        float RelevanceScore = CalculateSearchRelevance(NodeEntry, SearchCriteria);
        
        if (RelevanceScore > 0.0f)
        {
            ScoredResults.Add(TPair<FNODETYPEREGISTRYENTRY, float>(NodeEntry, RelevanceScore));
        }
    }

    // Sort by relevance score (highest first)
    ScoredResults.Sort([](const TPair<FNODETYPEREGISTRYENTRY, float>& A, const TPair<FNODETYPEREGISTRYENTRY, float>& B)
    {
        return A.Value > B.Value;
    });

    // Extract results up to max limit
    int32 ResultCount = FMath::Min(ScoredResults.Num(), SearchCriteria.MaxResults);
    for (int32 i = 0; i < ResultCount; i++)
    {
        Result.FoundNodes.Add(ScoredResults[i].Key);
    }

    Result.TotalMatches = ScoredResults.Num();
    Result.SearchTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    Result.bSearchSuccessful = true;

    // Update statistics
    LastSearchTimeMs = Result.SearchTimeMs;
    TotalSearchCount++;
    UpdateRegistryStatistics(TEXT("Search"));

    LogRegistryActivity(FString::Printf(TEXT("Search completed: %d results in %.2fms"), 
        Result.FoundNodes.Num(), Result.SearchTimeMs));

    return Result;
}

TArray<FNODETYPEREGISTRYENTRY> FNodeTypeRegistry::SearchByText(const FString& SearchText, int32 MaxResults)
{
    FNODESEARCHCRITERIA SearchCriteria;
    SearchCriteria.SearchText = SearchText;
    SearchCriteria.MaxResults = MaxResults;
    
    FNODESEARCHRESULT Result = SearchNodeTypes(SearchCriteria);
    return Result.FoundNodes;
}

bool FNodeTypeRegistry::IsNodeTypeCompatible(const FString& NodeTypeID, const FString& GraphType)
{
    if (!NodeTypeRegistry.Contains(NodeTypeID))
    {
        return false;
    }

    const FNODETYPEREGISTRYENTRY& NodeEntry = NodeTypeRegistry[NodeTypeID];
    
    // If no specific graph types are specified, assume universal compatibility
    if (NodeEntry.SupportedGraphTypes.Num() == 0)
    {
        return NodeEntry.Compatibility == ENodeCompatibility::Universal;
    }

    // Check if the graph type is in the supported list
    return NodeEntry.SupportedGraphTypes.Contains(GraphType);
}

void FNodeTypeRegistry::RecordNodeTypeUsage(const FString& NodeTypeID)
{
    if (!bTrackUsageStatistics || !NodeTypeRegistry.Contains(NodeTypeID))
    {
        return;
    }

    FNODETYPEREGISTRYENTRY& NodeEntry = NodeTypeRegistry[NodeTypeID];
    NodeEntry.UsageCount++;
    NodeEntry.LastUsed = FDateTime::Now();

    UpdateRegistryStatistics(TEXT("Usage"), NodeTypeID);
}

bool FNodeTypeRegistry::HasNodeType(const FString& NodeTypeID)
{
    return NodeTypeRegistry.Contains(NodeTypeID);
}

int32 FNodeTypeRegistry::GetNodeTypeCount()
{
    return NodeTypeRegistry.Num();
}

void FNodeTypeRegistry::InitializeFlowControlNodes()
{
    // Branch node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("Branch");
        NodeEntry.DisplayName = TEXT("Branch");
        NodeEntry.NodeClass = UK2Node_IfThenElse::StaticClass();
        NodeEntry.Category = ENodeCategory::FlowControl;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Simple;
        NodeEntry.Description = TEXT("Conditional execution based on boolean input");
        NodeEntry.Keywords = { TEXT("if"), TEXT("condition"), TEXT("boolean"), TEXT("true"), TEXT("false") };
        NodeEntry.Aliases = { TEXT("If"), TEXT("IfThenElse"), TEXT("Conditional") };
        RegisterNodeType(NodeEntry);
    }

    // Sequence node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("Sequence");
        NodeEntry.DisplayName = TEXT("Sequence");
        NodeEntry.NodeClass = UK2Node_ExecutionSequence::StaticClass();
        NodeEntry.Category = ENodeCategory::FlowControl;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Simple;
        NodeEntry.Description = TEXT("Execute multiple outputs in sequence");
        NodeEntry.Keywords = { TEXT("sequence"), TEXT("order"), TEXT("multiple"), TEXT("execution") };
        NodeEntry.Aliases = { TEXT("ExecutionSequence"), TEXT("Then") };
        RegisterNodeType(NodeEntry);
    }

    // For Loop node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("ForLoop");
        NodeEntry.DisplayName = TEXT("For Loop");
        NodeEntry.NodeClass = UK2Node_ForLoop::StaticClass();
        NodeEntry.Category = ENodeCategory::FlowControl;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Moderate;
        NodeEntry.Description = TEXT("Loop a specified number of times");
        NodeEntry.Keywords = { TEXT("loop"), TEXT("for"), TEXT("iterate"), TEXT("repeat"), TEXT("count") };
        NodeEntry.Aliases = { TEXT("For"), TEXT("Loop") };
        RegisterNodeType(NodeEntry);
    }

    // While Loop node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("WhileLoop");
        NodeEntry.DisplayName = TEXT("While Loop");
        NodeEntry.NodeClass = UK2Node_WhileLoop::StaticClass();
        NodeEntry.Category = ENodeCategory::FlowControl;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Moderate;
        NodeEntry.Description = TEXT("Loop while condition is true");
        NodeEntry.Keywords = { TEXT("while"), TEXT("loop"), TEXT("condition"), TEXT("repeat") };
        NodeEntry.Aliases = { TEXT("While") };
        RegisterNodeType(NodeEntry);
    }

    // Switch node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("Switch");
        NodeEntry.DisplayName = TEXT("Switch");
        NodeEntry.NodeClass = UK2Node_Switch::StaticClass();
        NodeEntry.Category = ENodeCategory::FlowControl;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Moderate;
        NodeEntry.Description = TEXT("Select execution path based on input value");
        NodeEntry.Keywords = { TEXT("switch"), TEXT("case"), TEXT("select"), TEXT("choose") };
        NodeEntry.Aliases = { TEXT("SwitchOnInt"), TEXT("Case") };
        RegisterNodeType(NodeEntry);
    }
}

void FNodeTypeRegistry::InitializeVariableNodes()
{
    // Variable Get node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("VariableGet");
        NodeEntry.DisplayName = TEXT("Get Variable");
        NodeEntry.NodeClass = UK2Node_VariableGet::StaticClass();
        NodeEntry.Category = ENodeCategory::Variables;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Simple;
        NodeEntry.Description = TEXT("Get the value of a variable");
        NodeEntry.Keywords = { TEXT("get"), TEXT("variable"), TEXT("value"), TEXT("read") };
        NodeEntry.Aliases = { TEXT("Get"), TEXT("Variable") };
        RegisterNodeType(NodeEntry);
    }

    // Variable Set node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("VariableSet");
        NodeEntry.DisplayName = TEXT("Set Variable");
        NodeEntry.NodeClass = UK2Node_VariableSet::StaticClass();
        NodeEntry.Category = ENodeCategory::Variables;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Simple;
        NodeEntry.Description = TEXT("Set the value of a variable");
        NodeEntry.Keywords = { TEXT("set"), TEXT("variable"), TEXT("assign"), TEXT("write") };
        NodeEntry.Aliases = { TEXT("Set"), TEXT("Assign") };
        RegisterNodeType(NodeEntry);
    }
}

void FNodeTypeRegistry::InitializeEventNodes()
{
    // Event node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("Event");
        NodeEntry.DisplayName = TEXT("Event");
        NodeEntry.NodeClass = UK2Node_Event::StaticClass();
        NodeEntry.Category = ENodeCategory::Events;
        NodeEntry.Compatibility = ENodeCompatibility::EventGraph;
        NodeEntry.Complexity = ENodeComplexity::Simple;
        NodeEntry.Description = TEXT("Built-in event node");
        NodeEntry.Keywords = { TEXT("event"), TEXT("trigger"), TEXT("begin"), TEXT("start") };
        NodeEntry.SupportedGraphTypes = { TEXT("EventGraph") };
        RegisterNodeType(NodeEntry);
    }

    // Custom Event node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("CustomEvent");
        NodeEntry.DisplayName = TEXT("Custom Event");
        NodeEntry.NodeClass = UK2Node_CustomEvent::StaticClass();
        NodeEntry.Category = ENodeCategory::Events;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Moderate;
        NodeEntry.Description = TEXT("Custom event that can be called");
        NodeEntry.Keywords = { TEXT("custom"), TEXT("event"), TEXT("call"), TEXT("dispatch") };
        NodeEntry.Aliases = { TEXT("CallCustomEvent") };
        RegisterNodeType(NodeEntry);
    }
}

void FNodeTypeRegistry::InitializeFunctionNodes()
{
    // Function Call node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("FunctionCall");
        NodeEntry.DisplayName = TEXT("Call Function");
        NodeEntry.NodeClass = UK2Node_CallFunction::StaticClass();
        NodeEntry.Category = ENodeCategory::Functions;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Moderate;
        NodeEntry.Description = TEXT("Call a function");
        NodeEntry.Keywords = { TEXT("function"), TEXT("call"), TEXT("invoke"), TEXT("execute") };
        NodeEntry.Aliases = { TEXT("Call"), TEXT("Function") };
        NodeEntry.bRequiresSpecialConfiguration = true;
        RegisterNodeType(NodeEntry);
    }
}

void FNodeTypeRegistry::InitializeTimerNodes()
{
    // Delay node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("Delay");
        NodeEntry.DisplayName = TEXT("Delay");
        NodeEntry.NodeClass = UK2Node_Delay::StaticClass();
        NodeEntry.Category = ENodeCategory::Timers;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Simple;
        NodeEntry.Description = TEXT("Delay execution for specified time");
        NodeEntry.Keywords = { TEXT("delay"), TEXT("wait"), TEXT("timer"), TEXT("pause") };
        NodeEntry.Aliases = { TEXT("Wait") };
        RegisterNodeType(NodeEntry);
    }
}

void FNodeTypeRegistry::InitializeMathNodes()
{
    // Add basic math nodes here
    // This is a simplified version - full implementation would include many more math nodes
}

void FNodeTypeRegistry::InitializeCastingNodes()
{
    // Cast node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("Cast");
        NodeEntry.DisplayName = TEXT("Cast");
        NodeEntry.NodeClass = UK2Node_DynamicCast::StaticClass();
        NodeEntry.Category = ENodeCategory::Casting;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Moderate;
        NodeEntry.Description = TEXT("Cast object to specified type");
        NodeEntry.Keywords = { TEXT("cast"), TEXT("convert"), TEXT("type"), TEXT("as") };
        NodeEntry.Aliases = { TEXT("CastTo"), TEXT("As") };
        RegisterNodeType(NodeEntry);
    }
}

void FNodeTypeRegistry::InitializeArrayNodes()
{
    // ForEach Loop node
    {
        FNODETYPEREGISTRYENTRY NodeEntry;
        NodeEntry.NodeTypeID = TEXT("ForEachLoop");
        NodeEntry.DisplayName = TEXT("For Each Loop");
        NodeEntry.NodeClass = UK2Node_ForEachElementInArray::StaticClass();
        NodeEntry.Category = ENodeCategory::Arrays;
        NodeEntry.Compatibility = ENodeCompatibility::Universal;
        NodeEntry.Complexity = ENodeComplexity::Moderate;
        NodeEntry.Description = TEXT("Loop through each element in an array");
        NodeEntry.Keywords = { TEXT("foreach"), TEXT("array"), TEXT("loop"), TEXT("iterate") };
        NodeEntry.Aliases = { TEXT("ForEach") };
        RegisterNodeType(NodeEntry);
    }
}

void FNodeTypeRegistry::InitializeStringNodes()
{
    // Add string manipulation nodes here
}

void FNodeTypeRegistry::InitializeDebugNodes()
{
    // Add debug nodes here (Print String, etc.)
}

void FNodeTypeRegistry::InitializeInputNodes()
{
    // Add input nodes here
}

void FNodeTypeRegistry::InitializeUtilityNodes()
{
    // Add utility nodes here
}

void FNodeTypeRegistry::IndexNodeType(const FNODETYPEREGISTRYENTRY& NodeEntry)
{
    // Add to category map
    if (!CategoryMap.Contains(NodeEntry.Category))
    {
        CategoryMap.Add(NodeEntry.Category, TArray<FString>());
    }
    CategoryMap[NodeEntry.Category].AddUnique(NodeEntry.NodeTypeID);

    // Add to compatibility map
    if (!CompatibilityMap.Contains(NodeEntry.Compatibility))
    {
        CompatibilityMap.Add(NodeEntry.Compatibility, TArray<FString>());
    }
    CompatibilityMap[NodeEntry.Compatibility].AddUnique(NodeEntry.NodeTypeID);

    // Add to complexity map
    if (!ComplexityMap.Contains(NodeEntry.Complexity))
    {
        ComplexityMap.Add(NodeEntry.Complexity, TArray<FString>());
    }
    ComplexityMap[NodeEntry.Complexity].AddUnique(NodeEntry.NodeTypeID);

    // Add aliases to alias map
    for (const FString& Alias : NodeEntry.Aliases)
    {
        AliasMap.Add(Alias, NodeEntry.NodeTypeID);
    }

    // Add keywords to keyword map
    for (const FString& Keyword : NodeEntry.Keywords)
    {
        if (!KeywordMap.Contains(Keyword))
        {
            KeywordMap.Add(Keyword, TArray<FString>());
        }
        KeywordMap[Keyword].AddUnique(NodeEntry.NodeTypeID);
    }
}

void FNodeTypeRegistry::RemoveFromIndices(const FString& NodeTypeID)
{
    if (!NodeTypeRegistry.Contains(NodeTypeID))
    {
        return;
    }

    const FNODETYPEREGISTRYENTRY& NodeEntry = NodeTypeRegistry[NodeTypeID];

    // Remove from category map
    if (CategoryMap.Contains(NodeEntry.Category))
    {
        CategoryMap[NodeEntry.Category].Remove(NodeTypeID);
    }

    // Remove from compatibility map
    if (CompatibilityMap.Contains(NodeEntry.Compatibility))
    {
        CompatibilityMap[NodeEntry.Compatibility].Remove(NodeTypeID);
    }

    // Remove from complexity map
    if (ComplexityMap.Contains(NodeEntry.Complexity))
    {
        ComplexityMap[NodeEntry.Complexity].Remove(NodeTypeID);
    }

    // Remove aliases
    for (const FString& Alias : NodeEntry.Aliases)
    {
        AliasMap.Remove(Alias);
    }

    // Remove keywords
    for (const FString& Keyword : NodeEntry.Keywords)
    {
        if (KeywordMap.Contains(Keyword))
        {
            KeywordMap[Keyword].Remove(NodeTypeID);
        }
    }
}

void FNodeTypeRegistry::RebuildIndices()
{
    // Clear all indices
    AliasMap.Empty();
    KeywordMap.Empty();
    CategoryMap.Empty();
    CompatibilityMap.Empty();
    ComplexityMap.Empty();

    // Rebuild indices for all registered node types
    for (const auto& Pair : NodeTypeRegistry)
    {
        IndexNodeType(Pair.Value);
    }
}

bool FNodeTypeRegistry::ValidateNodeEntry(const FNODETYPEREGISTRYENTRY& NodeEntry, FString& OutErrorMessage)
{
    if (NodeEntry.NodeTypeID.IsEmpty())
    {
        OutErrorMessage = TEXT("Node type ID cannot be empty");
        return false;
    }

    if (NodeEntry.DisplayName.IsEmpty())
    {
        OutErrorMessage = TEXT("Display name cannot be empty");
        return false;
    }

    if (!NodeEntry.NodeClass)
    {
        OutErrorMessage = TEXT("Node class cannot be null");
        return false;
    }

    return true;
}

float FNodeTypeRegistry::CalculateSearchRelevance(const FNODETYPEREGISTRYENTRY& NodeEntry, const FNODESEARCHCRITERIA& SearchCriteria)
{
    float RelevanceScore = 0.0f;

    // Text matching (if search text is provided)
    if (!SearchCriteria.SearchText.IsEmpty())
    {
        float TextScore = 0.0f;
        
        // Check display name
        TextScore = FMath::Max(TextScore, PerformTextMatch(NodeEntry.DisplayName, SearchCriteria.SearchText) * 1.0f);
        
        // Check description
        TextScore = FMath::Max(TextScore, PerformTextMatch(NodeEntry.Description, SearchCriteria.SearchText) * 0.8f);
        
        // Check keywords
        for (const FString& Keyword : NodeEntry.Keywords)
        {
            TextScore = FMath::Max(TextScore, PerformTextMatch(Keyword, SearchCriteria.SearchText) * 0.9f);
        }
        
        // Check aliases
        for (const FString& Alias : NodeEntry.Aliases)
        {
            TextScore = FMath::Max(TextScore, PerformTextMatch(Alias, SearchCriteria.SearchText) * 0.9f);
        }
        
        RelevanceScore += TextScore * 0.6f; // 60% weight for text matching
    }
    else
    {
        RelevanceScore += 0.6f; // If no text search, give base relevance
    }

    // Category filtering
    if (SearchCriteria.Categories.Num() > 0)
    {
        if (SearchCriteria.Categories.Contains(NodeEntry.Category))
        {
            RelevanceScore += 0.2f; // 20% weight for category match
        }
        else
        {
            return 0.0f; // No match if category doesn't match filter
        }
    }
    else
    {
        RelevanceScore += 0.2f; // If no category filter, give base relevance
    }

    // Compatibility filtering
    if (SearchCriteria.CompatibilityLevels.Num() > 0)
    {
        if (SearchCriteria.CompatibilityLevels.Contains(NodeEntry.Compatibility))
        {
            RelevanceScore += 0.1f; // 10% weight for compatibility match
        }
        else
        {
            return 0.0f; // No match if compatibility doesn't match filter
        }
    }
    else
    {
        RelevanceScore += 0.1f; // If no compatibility filter, give base relevance
    }

    // Complexity filtering
    if (SearchCriteria.ComplexityLevels.Num() > 0)
    {
        if (SearchCriteria.ComplexityLevels.Contains(NodeEntry.Complexity))
        {
            RelevanceScore += 0.1f; // 10% weight for complexity match
        }
        else
        {
            return 0.0f; // No match if complexity doesn't match filter
        }
    }
    else
    {
        RelevanceScore += 0.1f; // If no complexity filter, give base relevance
    }

    return FMath::Clamp(RelevanceScore, 0.0f, 1.0f);
}

float FNodeTypeRegistry::PerformTextMatch(const FString& Text, const FString& SearchText)
{
    if (Text.IsEmpty() || SearchText.IsEmpty())
    {
        return 0.0f;
    }

    FString LowerText = Text.ToLower();
    FString LowerSearchText = SearchText.ToLower();

    // Exact match
    if (LowerText == LowerSearchText)
    {
        return 1.0f;
    }

    // Starts with
    if (LowerText.StartsWith(LowerSearchText))
    {
        return 0.9f;
    }

    // Contains
    if (LowerText.Contains(LowerSearchText))
    {
        return 0.7f;
    }

    // Fuzzy matching (simple implementation)
    // This could be enhanced with more sophisticated algorithms
    int32 CommonChars = 0;
    for (int32 i = 0; i < LowerSearchText.Len(); i++)
    {
        if (LowerText.Contains(FString::Chr(LowerSearchText[i])))
        {
            CommonChars++;
        }
    }

    if (CommonChars > 0)
    {
        return (float)CommonChars / (float)LowerSearchText.Len() * 0.5f;
    }

    return 0.0f;
}

void FNodeTypeRegistry::LogRegistryActivity(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogNodeTypeRegistry, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogNodeTypeRegistry, Log, TEXT("%s"), *Message);
    }
}

void FNodeTypeRegistry::UpdateRegistryStatistics(const FString& Operation, const FString& NodeTypeID)
{
    if (!bTrackUsageStatistics)
    {
        return;
    }

    FString StatKey = FString::Printf(TEXT("%s_Count"), *Operation);
    if (RegistryStatistics.Contains(StatKey))
    {
        RegistryStatistics[StatKey]++;
    }
    else
    {
        RegistryStatistics.Add(StatKey, 1);
    }
} 