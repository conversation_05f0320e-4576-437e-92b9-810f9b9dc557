#include "NLP/StructureExtractor.h"
#include "NLP/IntentAnalyzer.h"
#include "NLP/BlueprintPatternDetector.h"
#include "UE5BlueprintGeneratorModule.h"
#include "Misc/Regex.h"

DEFINE_LOG_CATEGORY_EXTERN(LogStructureExtractor, Log, All);
DEFINE_LOG_CATEGORY(LogStructureExtractor);

FStructureExtractor::FStructureExtractor()
	: ConfidenceThreshold(0.3f)
{
	InitializeVariablePatterns();
	InitializeFunctionPatterns();
	InitializeEventPatterns();
	InitializeComponentPatterns();
	InitializeRelationshipPatterns();
	InitializeTypeMappings();
	
	UE_LOG(LogStructureExtractor, Log, TEXT("Structure extractor initialized with %d variable patterns, %d function patterns"), 
		VariablePatterns.Num(), FunctionPatterns.Num());
}

FStructureExtractor::~FStructureExtractor()
{
	UE_LOG(LogStructureExtractor, Log, TEXT("Structure extractor destroyed"));
}

FExtractedBlueprintStructure FStructureExtractor::ExtractStructure(const FString& InputText, 
	const FIntentAnalysisResult& IntentResult, 
	const EBlueprintPatternType PatternType)
{
	FExtractedBlueprintStructure Structure;
	
	if (InputText.IsEmpty())
	{
		return Structure;
	}
	
	UE_LOG(LogStructureExtractor, VeryVerbose, TEXT("Extracting structure from input: %s"), *InputText);
	
	// Extract individual elements
	Structure.Variables = ExtractVariables(InputText);
	Structure.Functions = ExtractFunctions(InputText);
	Structure.Events = ExtractEvents(InputText);
	Structure.Components = ExtractComponents(InputText);
	
	// Analyze relationships between elements
	Structure.Relationships = AnalyzeRelationships(Structure);
	
	// Calculate overall confidence and complexity
	Structure.OverallConfidence = CalculateOverallConfidence(Structure);
	Structure.EstimatedComplexity = CalculateOverallComplexity(Structure, PatternType);
	
	// Determine suggested blueprint type
	Structure.SuggestedBlueprintType = DetermineBlueprintType(Structure, IntentResult, PatternType);
	
	// Generate extraction notes and warnings
	Structure.ExtractionNotes = GetValidationWarnings(Structure);
	
	// Update statistics
	UpdateExtractionStatistics(TEXT("Variables"), Structure.Variables.Num());
	UpdateExtractionStatistics(TEXT("Functions"), Structure.Functions.Num());
	UpdateExtractionStatistics(TEXT("Events"), Structure.Events.Num());
	UpdateExtractionStatistics(TEXT("Components"), Structure.Components.Num());
	
	UE_LOG(LogStructureExtractor, Log, TEXT("Extracted structure: %d variables, %d functions, %d events, %d components"), 
		Structure.Variables.Num(), Structure.Functions.Num(), Structure.Events.Num(), Structure.Components.Num());
	
	return Structure;
}

TArray<FExtractedVariable> FStructureExtractor::ExtractVariables(const FString& InputText)
{
	TArray<FExtractedVariable> Variables;
	
	if (InputText.IsEmpty())
	{
		return Variables;
	}
	
	FString LowerInput = InputText.ToLower();
	
	// Extract variables using patterns
	for (const auto& PatternPair : VariablePatterns)
	{
		const FString& PatternType = PatternPair.Key;
		const TArray<FString>& Patterns = PatternPair.Value;
		
		for (const FString& Pattern : Patterns)
		{
			FExtractedVariable Variable = ExtractVariableFromPattern(InputText, Pattern);
			if (!Variable.Name.IsEmpty() && Variable.Confidence >= ConfidenceThreshold)
			{
				// Check for duplicates
				bool bDuplicate = false;
				for (const FExtractedVariable& ExistingVar : Variables)
				{
					if (ExistingVar.Name.Equals(Variable.Name, ESearchCase::IgnoreCase))
					{
						bDuplicate = true;
						break;
					}
				}
				
				if (!bDuplicate)
				{
					Variables.Add(Variable);
				}
			}
		}
	}
	
	// Extract implicit variables from context
	TArray<FExtractedVariable> ImplicitVariables = ExtractImplicitVariables(InputText);
	for (const FExtractedVariable& ImplicitVar : ImplicitVariables)
	{
		bool bDuplicate = false;
		for (const FExtractedVariable& ExistingVar : Variables)
		{
			if (ExistingVar.Name.Equals(ImplicitVar.Name, ESearchCase::IgnoreCase))
			{
				bDuplicate = true;
				break;
			}
		}
		
		if (!bDuplicate)
		{
			Variables.Add(ImplicitVar);
		}
	}
	
	return Variables;
}

TArray<FExtractedFunction> FStructureExtractor::ExtractFunctions(const FString& InputText)
{
	TArray<FExtractedFunction> Functions;
	
	if (InputText.IsEmpty())
	{
		return Functions;
	}
	
	// Extract functions using patterns
	for (const auto& PatternPair : FunctionPatterns)
	{
		const TArray<FString>& Patterns = PatternPair.Value;
		
		for (const FString& Pattern : Patterns)
		{
			FExtractedFunction Function = ExtractFunctionFromPattern(InputText, Pattern);
			if (!Function.Name.IsEmpty() && Function.Confidence >= ConfidenceThreshold)
			{
				// Check for duplicates
				bool bDuplicate = false;
				for (const FExtractedFunction& ExistingFunc : Functions)
				{
					if (ExistingFunc.Name.Equals(Function.Name, ESearchCase::IgnoreCase))
					{
						bDuplicate = true;
						break;
					}
				}
				
				if (!bDuplicate)
				{
					Functions.Add(Function);
				}
			}
		}
	}
	
	return Functions;
}

TArray<FExtractedEvent> FStructureExtractor::ExtractEvents(const FString& InputText)
{
	TArray<FExtractedEvent> Events;
	
	if (InputText.IsEmpty())
	{
		return Events;
	}
	
	// Extract events using patterns
	for (const auto& PatternPair : EventPatterns)
	{
		const TArray<FString>& Patterns = PatternPair.Value;
		
		for (const FString& Pattern : Patterns)
		{
			FExtractedEvent Event = ExtractEventFromPattern(InputText, Pattern);
			if (!Event.EventType.IsEmpty() && Event.Confidence >= ConfidenceThreshold)
			{
				// Check for duplicates
				bool bDuplicate = false;
				for (const FExtractedEvent& ExistingEvent : Events)
				{
					if (ExistingEvent.EventType.Equals(Event.EventType, ESearchCase::IgnoreCase))
					{
						bDuplicate = true;
						break;
					}
				}
				
				if (!bDuplicate)
				{
					Events.Add(Event);
				}
			}
		}
	}
	
	return Events;
}

TArray<FExtractedComponent> FStructureExtractor::ExtractComponents(const FString& InputText)
{
	TArray<FExtractedComponent> Components;
	
	if (InputText.IsEmpty())
	{
		return Components;
	}
	
	// Extract components using patterns
	for (const auto& PatternPair : ComponentPatterns)
	{
		const TArray<FString>& Patterns = PatternPair.Value;
		
		for (const FString& Pattern : Patterns)
		{
			FExtractedComponent Component = ExtractComponentFromPattern(InputText, Pattern);
			if (!Component.Name.IsEmpty() && Component.Confidence >= ConfidenceThreshold)
			{
				// Check for duplicates
				bool bDuplicate = false;
				for (const FExtractedComponent& ExistingComp : Components)
				{
					if (ExistingComp.Name.Equals(Component.Name, ESearchCase::IgnoreCase))
					{
						bDuplicate = true;
						break;
					}
				}
				
				if (!bDuplicate)
				{
					Components.Add(Component);
				}
			}
		}
	}
	
	return Components;
}

void FStructureExtractor::InitializeVariablePatterns()
{
	// Variable declaration patterns
	VariablePatterns.Add(TEXT("Declaration"), {
		TEXT("variable\\s+(\\w+)"),
		TEXT("(\\w+)\\s+variable"),
		TEXT("create\\s+variable\\s+(\\w+)"),
		TEXT("add\\s+variable\\s+(\\w+)"),
		TEXT("(\\w+)\\s+property"),
		TEXT("property\\s+(\\w+)"),
		TEXT("store\\s+(\\w+)"),
		TEXT("keep\\s+track\\s+of\\s+(\\w+)")
	});
	
	// Type-specific patterns
	VariablePatterns.Add(TEXT("Boolean"), {
		TEXT("(\\w+)\\s+(?:is|should be|can be)\\s+(?:true|false|enabled|disabled)"),
		TEXT("(?:true|false|enabled|disabled)\\s+(\\w+)"),
		TEXT("boolean\\s+(\\w+)"),
		TEXT("flag\\s+(\\w+)"),
		TEXT("toggle\\s+(\\w+)")
	});
	
	VariablePatterns.Add(TEXT("Numeric"), {
		TEXT("(\\w+)\\s+(?:value|amount|count|number)"),
		TEXT("(?:value|amount|count|number)\\s+of\\s+(\\w+)"),
		TEXT("(\\w+)\\s+(?:equals|is)\\s+\\d+"),
		TEXT("float\\s+(\\w+)"),
		TEXT("integer\\s+(\\w+)"),
		TEXT("(\\w+)\\s+speed"),
		TEXT("(\\w+)\\s+health"),
		TEXT("(\\w+)\\s+damage")
	});
	
	VariablePatterns.Add(TEXT("Vector"), {
		TEXT("(\\w+)\\s+(?:position|location)"),
		TEXT("(?:position|location)\\s+of\\s+(\\w+)"),
		TEXT("(\\w+)\\s+(?:rotation|direction)"),
		TEXT("(\\w+)\\s+scale"),
		TEXT("vector\\s+(\\w+)")
	});
	
	VariablePatterns.Add(TEXT("String"), {
		TEXT("(\\w+)\\s+name"),
		TEXT("name\\s+of\\s+(\\w+)"),
		TEXT("(\\w+)\\s+text"),
		TEXT("string\\s+(\\w+)"),
		TEXT("(\\w+)\\s+message")
	});
}

void FStructureExtractor::InitializeFunctionPatterns()
{
	// Function declaration patterns
	FunctionPatterns.Add(TEXT("Declaration"), {
		TEXT("function\\s+(\\w+)"),
		TEXT("create\\s+function\\s+(\\w+)"),
		TEXT("add\\s+function\\s+(\\w+)"),
		TEXT("(\\w+)\\s+function"),
		TEXT("method\\s+(\\w+)"),
		TEXT("procedure\\s+(\\w+)")
	});
	
	// Action-based function patterns
	FunctionPatterns.Add(TEXT("Action"), {
		TEXT("calculate\\s+(\\w+)"),
		TEXT("compute\\s+(\\w+)"),
		TEXT("get\\s+(\\w+)"),
		TEXT("set\\s+(\\w+)"),
		TEXT("update\\s+(\\w+)"),
		TEXT("process\\s+(\\w+)"),
		TEXT("handle\\s+(\\w+)"),
		TEXT("manage\\s+(\\w+)")
	});
	
	// Return value patterns
	FunctionPatterns.Add(TEXT("ReturnValue"), {
		TEXT("return\\s+(\\w+)"),
		TEXT("returns\\s+(\\w+)"),
		TEXT("output\\s+(\\w+)"),
		TEXT("result\\s+(\\w+)")
	});
}

void FStructureExtractor::InitializeEventPatterns()
{
	// Built-in event patterns
	EventPatterns.Add(TEXT("BuiltIn"), {
		TEXT("begin\\s+play"),
		TEXT("start"),
		TEXT("tick"),
		TEXT("update"),
		TEXT("end\\s+play"),
		TEXT("destroy"),
		TEXT("overlap"),
		TEXT("hit"),
		TEXT("collision"),
		TEXT("input"),
		TEXT("key\\s+press"),
		TEXT("mouse\\s+click")
	});
	
	// Custom event patterns
	EventPatterns.Add(TEXT("Custom"), {
		TEXT("event\\s+(\\w+)"),
		TEXT("custom\\s+event\\s+(\\w+)"),
		TEXT("trigger\\s+(\\w+)"),
		TEXT("when\\s+(\\w+)"),
		TEXT("on\\s+(\\w+)")
	});
}

void FStructureExtractor::InitializeComponentPatterns()
{
	// Component type patterns
	ComponentPatterns.Add(TEXT("Mesh"), {
		TEXT("mesh\\s+component"),
		TEXT("static\\s+mesh"),
		TEXT("skeletal\\s+mesh"),
		TEXT("(\\w+)\\s+mesh")
	});
	
	ComponentPatterns.Add(TEXT("Collision"), {
		TEXT("collision\\s+component"),
		TEXT("box\\s+collision"),
		TEXT("sphere\\s+collision"),
		TEXT("capsule\\s+collision"),
		TEXT("(\\w+)\\s+collider")
	});
	
	ComponentPatterns.Add(TEXT("Movement"), {
		TEXT("movement\\s+component"),
		TEXT("character\\s+movement"),
		TEXT("floating\\s+pawn\\s+movement"),
		TEXT("projectile\\s+movement")
	});
	
	ComponentPatterns.Add(TEXT("Audio"), {
		TEXT("audio\\s+component"),
		TEXT("sound\\s+component"),
		TEXT("(\\w+)\\s+audio")
	});
	
	ComponentPatterns.Add(TEXT("Light"), {
		TEXT("light\\s+component"),
		TEXT("point\\s+light"),
		TEXT("directional\\s+light"),
		TEXT("spot\\s+light")
	});
}

void FStructureExtractor::InitializeRelationshipPatterns()
{
	// Usage relationships
	RelationshipPatterns.Add(TEXT("Uses"), {
		TEXT("uses\\s+(\\w+)"),
		TEXT("utilizes\\s+(\\w+)"),
		TEXT("accesses\\s+(\\w+)"),
		TEXT("reads\\s+(\\w+)")
	});
	
	// Modification relationships
	RelationshipPatterns.Add(TEXT("Modifies"), {
		TEXT("modifies\\s+(\\w+)"),
		TEXT("changes\\s+(\\w+)"),
		TEXT("updates\\s+(\\w+)"),
		TEXT("sets\\s+(\\w+)")
	});
	
	// Call relationships
	RelationshipPatterns.Add(TEXT("Calls"), {
		TEXT("calls\\s+(\\w+)"),
		TEXT("invokes\\s+(\\w+)"),
		TEXT("executes\\s+(\\w+)"),
		TEXT("runs\\s+(\\w+)")
	});
	
	// Trigger relationships
	RelationshipPatterns.Add(TEXT("Triggers"), {
		TEXT("triggers\\s+(\\w+)"),
		TEXT("fires\\s+(\\w+)"),
		TEXT("activates\\s+(\\w+)"),
		TEXT("starts\\s+(\\w+)")
	});
}

void FStructureExtractor::InitializeTypeMappings()
{
	// Variable type mappings
	VariableTypeMap.Add(TEXT("health"), TEXT("float"));
	VariableTypeMap.Add(TEXT("damage"), TEXT("float"));
	VariableTypeMap.Add(TEXT("speed"), TEXT("float"));
	VariableTypeMap.Add(TEXT("position"), TEXT("vector"));
	VariableTypeMap.Add(TEXT("location"), TEXT("vector"));
	VariableTypeMap.Add(TEXT("rotation"), TEXT("rotator"));
	VariableTypeMap.Add(TEXT("scale"), TEXT("vector"));
	VariableTypeMap.Add(TEXT("name"), TEXT("string"));
	VariableTypeMap.Add(TEXT("text"), TEXT("string"));
	VariableTypeMap.Add(TEXT("message"), TEXT("string"));
	VariableTypeMap.Add(TEXT("enabled"), TEXT("boolean"));
	VariableTypeMap.Add(TEXT("active"), TEXT("boolean"));
	VariableTypeMap.Add(TEXT("visible"), TEXT("boolean"));
	VariableTypeMap.Add(TEXT("count"), TEXT("integer"));
	VariableTypeMap.Add(TEXT("index"), TEXT("integer"));
	VariableTypeMap.Add(TEXT("id"), TEXT("integer"));
	
	// Component type mappings
	ComponentTypeMap.Add(TEXT("mesh"), TEXT("StaticMeshComponent"));
	ComponentTypeMap.Add(TEXT("static mesh"), TEXT("StaticMeshComponent"));
	ComponentTypeMap.Add(TEXT("skeletal mesh"), TEXT("SkeletalMeshComponent"));
	ComponentTypeMap.Add(TEXT("box collision"), TEXT("BoxComponent"));
	ComponentTypeMap.Add(TEXT("sphere collision"), TEXT("SphereComponent"));
	ComponentTypeMap.Add(TEXT("capsule collision"), TEXT("CapsuleComponent"));
	ComponentTypeMap.Add(TEXT("movement"), TEXT("CharacterMovementComponent"));
	ComponentTypeMap.Add(TEXT("audio"), TEXT("AudioComponent"));
	ComponentTypeMap.Add(TEXT("sound"), TEXT("AudioComponent"));
	ComponentTypeMap.Add(TEXT("point light"), TEXT("PointLightComponent"));
	ComponentTypeMap.Add(TEXT("directional light"), TEXT("DirectionalLightComponent"));
	ComponentTypeMap.Add(TEXT("spot light"), TEXT("SpotLightComponent"));
	ComponentTypeMap.Add(TEXT("camera"), TEXT("CameraComponent"));
	
	// Event type mappings
	EventTypeMap.Add(TEXT("begin play"), TEXT("BeginPlay"));
	EventTypeMap.Add(TEXT("start"), TEXT("BeginPlay"));
	EventTypeMap.Add(TEXT("tick"), TEXT("Tick"));
	EventTypeMap.Add(TEXT("update"), TEXT("Tick"));
	EventTypeMap.Add(TEXT("end play"), TEXT("EndPlay"));
	EventTypeMap.Add(TEXT("destroy"), TEXT("EndPlay"));
	EventTypeMap.Add(TEXT("overlap"), TEXT("OnActorBeginOverlap"));
	EventTypeMap.Add(TEXT("hit"), TEXT("OnHit"));
	EventTypeMap.Add(TEXT("collision"), TEXT("OnHit"));
}

FExtractedVariable FStructureExtractor::ExtractVariableFromPattern(const FString& InputText, const FString& Pattern)
{
	FExtractedVariable Variable;
	
	FRegexPattern RegexPattern(Pattern);
	FRegexMatcher Matcher(RegexPattern, InputText);
	
	if (Matcher.FindNext())
	{
		Variable.Name = Matcher.GetCaptureGroup(1);
		Variable.Type = DetermineVariableType(InputText, Variable.Name);
		Variable.bRequired = IsVariableRequired(InputText);
		Variable.bEditable = IsVariableEditable(InputText);
		Variable.DefaultValue = ExtractVariableDefaultValue(InputText, Variable.Type);
		Variable.Category = TEXT("Generated");
		Variable.Description = FString::Printf(TEXT("Variable extracted from: %s"), *Variable.Name);
		Variable.Confidence = CalculateElementConfidence(InputText, { Variable.Name });
	}
	
	return Variable;
}

FString FStructureExtractor::DetermineVariableType(const FString& Context, const FString& VariableName)
{
	FString LowerContext = Context.ToLower();
	FString LowerName = VariableName.ToLower();
	
	// Check type mappings first
	for (const auto& TypePair : VariableTypeMap)
	{
		if (LowerContext.Contains(TypePair.Key) || LowerName.Contains(TypePair.Key))
		{
			return TypePair.Value;
		}
	}
	
	// Pattern-based type detection
	if (LowerContext.Contains(TEXT("true")) || LowerContext.Contains(TEXT("false")) || 
		LowerContext.Contains(TEXT("enabled")) || LowerContext.Contains(TEXT("disabled")))
	{
		return TEXT("boolean");
	}
	
	if (LowerContext.Contains(TEXT("position")) || LowerContext.Contains(TEXT("location")) || 
		LowerContext.Contains(TEXT("scale")) || LowerName.Contains(TEXT("pos")))
	{
		return TEXT("vector");
	}
	
	if (LowerContext.Contains(TEXT("rotation")) || LowerContext.Contains(TEXT("angle")) || 
		LowerName.Contains(TEXT("rot")))
	{
		return TEXT("rotator");
	}
	
	if (LowerContext.Contains(TEXT("name")) || LowerContext.Contains(TEXT("text")) || 
		LowerContext.Contains(TEXT("message")) || LowerName.Contains(TEXT("str")))
	{
		return TEXT("string");
	}
	
	// Check for numeric patterns
	FRegexPattern NumericPattern(TEXT("\\d+"));
	FRegexMatcher NumericMatcher(NumericPattern, LowerContext);
	if (NumericMatcher.FindNext())
	{
		if (LowerContext.Contains(TEXT(".")))
		{
			return TEXT("float");
		}
		else
		{
			return TEXT("integer");
		}
	}
	
	// Default to float
	return TEXT("float");
}

// ... continue with remaining helper methods ... 