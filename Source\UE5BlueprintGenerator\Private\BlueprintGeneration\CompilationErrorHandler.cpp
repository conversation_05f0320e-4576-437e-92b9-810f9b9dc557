#include "BlueprintGeneration/CompilationErrorHandler.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node.h"

DEFINE_LOG_CATEGORY(LogCompilationErrorHandler);

FCompilationErrorHandler::FCompilationErrorHandler()
{
    InitializeComponents();
    InitializeClassificationPatterns();
    InitializeAutoFixHandlers();
    InitializeRecoveryHandlers();
    
    // Initialize default configuration
    Config.HandlingMode = EErrorHandlingMode::Tolerant;
    Config.bEnableAutoFix = true;
    Config.bEnableErrorRecovery = true;
    Config.bEnableErrorAnalysis = true;
    Config.bEnableErrorLogging = true;
    Config.bEnableErrorStatistics = true;
    Config.MaxAutoFixAttempts = 3;
    Config.AutoFixConfidenceThreshold = 0.7f;
    Config.MaxRecoveryAttempts = 5;
    Config.RecoveryTimeout = 30.0f;
    Config.SeverityThreshold = ECompilationErrorSeverity::Warning;
}

FCompilationErrorHandler::~FCompilationErrorHandler()
{
    ShutdownComponents();
}

bool FCompilationErrorHandler::HandleError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    UE_LOG(LogCompilationErrorHandler, Log, TEXT("Handling compilation error: %s"), *Error.Message);
    
    BroadcastErrorDetected(Error, Context);
    
    // Check if error should be ignored
    if (ShouldIgnoreError(Error))
    {
        UE_LOG(LogCompilationErrorHandler, Log, TEXT("Error ignored based on configuration"));
        OutResult.bRecoverySuccessful = true;
        OutResult.StrategyUsed = EErrorRecoveryStrategy::Skip;
        OutResult.RecoveryDescription = TEXT("Error ignored based on configuration");
        return true;
    }
    
    // Analyze error if enabled
    FErrorAnalysisResult Analysis;
    if (Config.bEnableErrorAnalysis)
    {
        Analysis = AnalyzeError(Error, Context);
        BroadcastErrorAnalyzed(Error, Analysis);
    }
    
    // Attempt recovery based on analysis
    bool bRecoverySuccessful = false;
    EErrorRecoveryStrategy SelectedStrategy = Analysis.RecommendedStrategy;
    
    if (SelectedStrategy == EErrorRecoveryStrategy::None)
    {
        SelectedStrategy = SelectRecoveryStrategy(Error, Context);
    }
    
    if (Config.bEnableErrorRecovery && SelectedStrategy != EErrorRecoveryStrategy::None)
    {
        bRecoverySuccessful = AttemptRecovery(Error, Context, SelectedStrategy, OutResult);
    }
    
    // Update statistics
    if (Config.bEnableErrorStatistics)
    {
        UpdateStatistics(Error, OutResult);
    }
    
    // Broadcast result
    if (bRecoverySuccessful)
    {
        BroadcastErrorResolved(Error);
    }
    else
    {
        BroadcastErrorUnresolved(Error);
    }
    
    return bRecoverySuccessful;
}

bool FCompilationErrorHandler::HandleErrors(const TArray<FCompilationError>& Errors, const FErrorContext& Context, TArray<FErrorRecoveryResult>& OutResults)
{
    OutResults.Empty();
    OutResults.Reserve(Errors.Num());
    
    bool bAllSuccessful = true;
    
    for (const FCompilationError& Error : Errors)
    {
        FErrorRecoveryResult Result;
        bool bSuccess = HandleError(Error, Context, Result);
        OutResults.Add(Result);
        
        if (!bSuccess)
        {
            bAllSuccessful = false;
        }
    }
    
    return bAllSuccessful;
}

FErrorAnalysisResult FCompilationErrorHandler::AnalyzeError(const FCompilationError& Error, const FErrorContext& Context)
{
    FErrorAnalysisResult Result;
    
    // Classify error
    Result.ErrorClass = ClassifyError(Error, Context);
    Result.ClassificationConfidence = CalculateClassificationConfidence(Error, Result.ErrorClass);
    
    // Analyze root cause
    Result.RootCause = AnalyzeRootCause(Error, Context);
    
    // Identify contributing factors
    Result.ContributingFactors = IdentifyContributingFactors(Error, Context);
    
    // Determine auto-fix feasibility
    Result.bCanAutoFix = CanAutoFix(Error, Context);
    Result.AutoFixConfidence = CalculateAutoFixConfidence(Error, Context);
    
    // Select recovery strategy
    Result.RecommendedStrategy = SelectRecoveryStrategy(Error, Context);
    Result.AlternativeStrategies = GetAvailableStrategies(Error, Context);
    
    // Generate suggestions
    Result.SuggestedFixes = GetAutoFixSuggestions(Error, Context);
    Result.PreventionSuggestions = GetPreventionSuggestions(Error, Context);
    
    return Result;
}

ECompilationErrorClass FCompilationErrorHandler::ClassifyError(const FCompilationError& Error, const FErrorContext& Context)
{
    // Try classification by syntax first
    ECompilationErrorClass SyntaxClass = ClassifyBySyntax(Error);
    if (SyntaxClass != ECompilationErrorClass::Unknown)
    {
        return SyntaxClass;
    }
    
    // Try classification by semantic analysis
    ECompilationErrorClass SemanticClass = ClassifyBySemantic(Error);
    if (SemanticClass != ECompilationErrorClass::Unknown)
    {
        return SemanticClass;
    }
    
    // Try classification by context
    ECompilationErrorClass ContextClass = ClassifyByContext(Error, Context);
    if (ContextClass != ECompilationErrorClass::Unknown)
    {
        return ContextClass;
    }
    
    return ECompilationErrorClass::Unknown;
}

float FCompilationErrorHandler::CalculateClassificationConfidence(const FCompilationError& Error, ECompilationErrorClass ErrorClass)
{
    if (ErrorClass == ECompilationErrorClass::Unknown)
    {
        return 0.0f;
    }
    
    // Check pattern matching confidence
    if (MatchesClassificationPattern(Error, ErrorClass))
    {
        return 0.8f; // High confidence for pattern matches
    }
    
    return 0.5f; // Medium confidence for other classifications
}

bool FCompilationErrorHandler::CanAutoFix(const FCompilationError& Error, const FErrorContext& Context)
{
    if (!Config.bEnableAutoFix)
    {
        return false;
    }
    
    // Check if error class has auto-fix handler
    ECompilationErrorClass ErrorClass = ClassifyError(Error, Context);
    return AutoFixHandlers.Contains(ErrorClass);
}

bool FCompilationErrorHandler::AttemptAutoFix(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    if (!CanAutoFix(Error, Context))
    {
        OutResult.bRecoverySuccessful = false;
        OutResult.RecoveryDescription = TEXT("Auto-fix not available for this error type");
        return false;
    }
    
    ECompilationErrorClass ErrorClass = ClassifyError(Error, Context);
    auto AutoFixHandler = AutoFixHandlers.Find(ErrorClass);
    
    if (AutoFixHandler)
    {
        BroadcastAutoFixAttempted(Error, TEXT("Attempting auto-fix"));
        
        FDateTime StartTime = FDateTime::Now();
        bool bSuccess = (*AutoFixHandler)(Error, Context, OutResult);
        FDateTime EndTime = FDateTime::Now();
        
        OutResult.RecoveryTime = (EndTime - StartTime).GetTotalSeconds();
        OutResult.StrategyUsed = EErrorRecoveryStrategy::AutoFix;
        
        if (bSuccess)
        {
            Statistics.AutoFixesSuccessful++;
        }
        Statistics.AutoFixesAttempted++;
        
        BroadcastAutoFixCompleted(Error, OutResult);
        return bSuccess;
    }
    
    return false;
}

EErrorRecoveryStrategy FCompilationErrorHandler::SelectRecoveryStrategy(const FCompilationError& Error, const FErrorContext& Context)
{
    // Check if auto-fix is available and confidence is high enough
    if (CanAutoFix(Error, Context))
    {
        float AutoFixConfidence = CalculateAutoFixConfidence(Error, Context);
        if (AutoFixConfidence >= Config.AutoFixConfidenceThreshold)
        {
            return EErrorRecoveryStrategy::AutoFix;
        }
    }
    
    // Select strategy based on error severity
    switch (Error.Severity)
    {
        case ECompilationErrorSeverity::Fatal:
            return EErrorRecoveryStrategy::Rollback;
        case ECompilationErrorSeverity::Error:
            return EErrorRecoveryStrategy::Retry;
        case ECompilationErrorSeverity::Warning:
            return EErrorRecoveryStrategy::Skip;
        case ECompilationErrorSeverity::Info:
            return EErrorRecoveryStrategy::None;
        default:
            return EErrorRecoveryStrategy::Fallback;
    }
}

bool FCompilationErrorHandler::AttemptRecovery(const FCompilationError& Error, const FErrorContext& Context, EErrorRecoveryStrategy Strategy, FErrorRecoveryResult& OutResult)
{
    BroadcastRecoveryAttempted(Error, Strategy);
    
    FDateTime StartTime = FDateTime::Now();
    bool bSuccess = false;
    
    // Execute recovery strategy
    auto RecoveryHandler = RecoveryHandlers.Find(Strategy);
    if (RecoveryHandler)
    {
        bSuccess = (*RecoveryHandler)(Error, Context, OutResult);
    }
    else
    {
        // Fallback to basic recovery strategies
        switch (Strategy)
        {
            case EErrorRecoveryStrategy::AutoFix:
                bSuccess = ExecuteAutoFixStrategy(Error, Context, OutResult);
                break;
            case EErrorRecoveryStrategy::Skip:
                bSuccess = ExecuteSkipStrategy(Error, Context, OutResult);
                break;
            case EErrorRecoveryStrategy::Retry:
                bSuccess = ExecuteRetryStrategy(Error, Context, OutResult);
                break;
            case EErrorRecoveryStrategy::Fallback:
                bSuccess = ExecuteFallbackStrategy(Error, Context, OutResult);
                break;
            default:
                OutResult.RecoveryDescription = TEXT("Recovery strategy not implemented");
                break;
        }
    }
    
    FDateTime EndTime = FDateTime::Now();
    OutResult.RecoveryTime = (EndTime - StartTime).GetTotalSeconds();
    OutResult.StrategyUsed = Strategy;
    OutResult.bRecoverySuccessful = bSuccess;
    
    // Update statistics
    Statistics.RecoveryAttempts++;
    if (bSuccess)
    {
        Statistics.RecoverySuccesses++;
    }
    
    // Update strategy usage
    if (Statistics.StrategyUsage.Contains(Strategy))
    {
        Statistics.StrategyUsage[Strategy]++;
    }
    else
    {
        Statistics.StrategyUsage.Add(Strategy, 1);
    }
    
    BroadcastRecoveryCompleted(Error, OutResult);
    return bSuccess;
}

void FCompilationErrorHandler::SetErrorHandlingConfig(const FErrorHandlingConfig& NewConfig)
{
    Config = NewConfig;
    UE_LOG(LogCompilationErrorHandler, Log, TEXT("Error handling configuration updated"));
}

FErrorHandlingConfig FCompilationErrorHandler::GetErrorHandlingConfig() const
{
    return Config;
}

FErrorHandlingStatistics FCompilationErrorHandler::GetStatistics() const
{
    return Statistics;
}

void FCompilationErrorHandler::ResetStatistics()
{
    Statistics = FErrorHandlingStatistics();
    UE_LOG(LogCompilationErrorHandler, Log, TEXT("Error handling statistics reset"));
}

// Private implementation methods

FString FCompilationErrorHandler::AnalyzeRootCause(const FCompilationError& Error, const FErrorContext& Context)
{
    // Analyze error message for common patterns
    if (Error.Message.Contains(TEXT("missing")))
    {
        return TEXT("Missing required element or connection");
    }
    else if (Error.Message.Contains(TEXT("type mismatch")))
    {
        return TEXT("Incompatible data types");
    }
    else if (Error.Message.Contains(TEXT("circular")))
    {
        return TEXT("Circular dependency detected");
    }
    else if (Error.Message.Contains(TEXT("undefined")))
    {
        return TEXT("Undefined reference or symbol");
    }
    
    return TEXT("Unknown root cause");
}

TArray<FString> FCompilationErrorHandler::IdentifyContributingFactors(const FCompilationError& Error, const FErrorContext& Context)
{
    TArray<FString> Factors;
    
    // Analyze context for contributing factors
    if (Context.Node)
    {
        Factors.Add(FString::Printf(TEXT("Node type: %s"), *Context.Node->GetClass()->GetName()));
    }
    
    if (Context.Graph)
    {
        Factors.Add(FString::Printf(TEXT("Graph type: %s"), *Context.Graph->GetClass()->GetName()));
    }
    
    if (!Context.CompilationPhase.IsEmpty())
    {
        Factors.Add(FString::Printf(TEXT("Compilation phase: %s"), *Context.CompilationPhase));
    }
    
    return Factors;
}

float FCompilationErrorHandler::CalculateAutoFixConfidence(const FCompilationError& Error, const FErrorContext& Context)
{
    float Confidence = 0.0f;
    
    // Base confidence on error classification
    ECompilationErrorClass ErrorClass = ClassifyError(Error, Context);
    switch (ErrorClass)
    {
        case ECompilationErrorClass::Syntax:
            Confidence = 0.8f; // High confidence for syntax errors
            break;
        case ECompilationErrorClass::Property:
            Confidence = 0.7f; // Good confidence for property errors
            break;
        case ECompilationErrorClass::Connection:
            Confidence = 0.6f; // Medium confidence for connection errors
            break;
        case ECompilationErrorClass::Type:
            Confidence = 0.5f; // Lower confidence for type errors
            break;
        default:
            Confidence = 0.3f; // Low confidence for other errors
            break;
    }
    
    return Confidence;
}

bool FCompilationErrorHandler::MatchesClassificationPattern(const FCompilationError& Error, ECompilationErrorClass ErrorClass)
{
    auto Patterns = ClassificationPatterns.Find(ErrorClass);
    if (!Patterns)
    {
        return false;
    }
    
    for (const FString& Pattern : *Patterns)
    {
        if (Error.Message.Contains(Pattern))
        {
            return true;
        }
    }
    
    return false;
}

ECompilationErrorClass FCompilationErrorHandler::ClassifyBySyntax(const FCompilationError& Error)
{
    if (Error.Message.Contains(TEXT("syntax")) || Error.Message.Contains(TEXT("parse")))
    {
        return ECompilationErrorClass::Syntax;
    }
    
    return ECompilationErrorClass::Unknown;
}

ECompilationErrorClass FCompilationErrorHandler::ClassifyBySemantic(const FCompilationError& Error)
{
    if (Error.Message.Contains(TEXT("type")) || Error.Message.Contains(TEXT("mismatch")))
    {
        return ECompilationErrorClass::Type;
    }
    else if (Error.Message.Contains(TEXT("connection")) || Error.Message.Contains(TEXT("pin")))
    {
        return ECompilationErrorClass::Connection;
    }
    else if (Error.Message.Contains(TEXT("property")) || Error.Message.Contains(TEXT("value")))
    {
        return ECompilationErrorClass::Property;
    }
    
    return ECompilationErrorClass::Unknown;
}

ECompilationErrorClass FCompilationErrorHandler::ClassifyByContext(const FCompilationError& Error, const FErrorContext& Context)
{
    // Use context information for classification
    if (Context.CompilationPhase.Contains(TEXT("dependency")))
    {
        return ECompilationErrorClass::Dependency;
    }
    else if (Context.CompilationPhase.Contains(TEXT("validation")))
    {
        return ECompilationErrorClass::Validation;
    }
    
    return ECompilationErrorClass::Unknown;
}

// Auto-fix implementation methods (placeholder implementations)
bool FCompilationErrorHandler::AutoFixSyntaxError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    OutResult.RecoveryDescription = TEXT("Syntax error auto-fix applied");
    OutResult.ChangesMade.Add(TEXT("Fixed syntax error"));
    return true;
}

bool FCompilationErrorHandler::AutoFixPropertyError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    OutResult.RecoveryDescription = TEXT("Property error auto-fix applied");
    OutResult.ChangesMade.Add(TEXT("Fixed property configuration"));
    return true;
}

// Recovery strategy implementation methods (placeholder implementations)
bool FCompilationErrorHandler::ExecuteAutoFixStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    return AttemptAutoFix(Error, Context, OutResult);
}

bool FCompilationErrorHandler::ExecuteSkipStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    OutResult.RecoveryDescription = TEXT("Error skipped");
    OutResult.ChangesMade.Add(TEXT("Skipped problematic element"));
    return true;
}

bool FCompilationErrorHandler::ExecuteRetryStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    OutResult.RecoveryDescription = TEXT("Operation retried");
    OutResult.ChangesMade.Add(TEXT("Retried failed operation"));
    return true;
}

bool FCompilationErrorHandler::ExecuteFallbackStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult)
{
    OutResult.RecoveryDescription = TEXT("Fallback implementation used");
    OutResult.ChangesMade.Add(TEXT("Applied fallback solution"));
    return true;
}

bool FCompilationErrorHandler::ShouldIgnoreError(const FCompilationError& Error)
{
    // Check severity threshold
    if (Error.Severity < Config.SeverityThreshold)
    {
        return true;
    }
    
    // Check ignored error classes
    ECompilationErrorClass ErrorClass = ClassifyError(Error, FErrorContext());
    return Config.IgnoredErrorClasses.Contains(ErrorClass);
}

void FCompilationErrorHandler::UpdateStatistics(const FCompilationError& Error, const FErrorRecoveryResult& Result)
{
    Statistics.TotalErrorsHandled++;
    
    // Update error class statistics
    ECompilationErrorClass ErrorClass = ClassifyError(Error, FErrorContext());
    if (Statistics.ErrorsByClass.Contains(ErrorClass))
    {
        Statistics.ErrorsByClass[ErrorClass]++;
    }
    else
    {
        Statistics.ErrorsByClass.Add(ErrorClass, 1);
    }
    
    // Update severity statistics
    if (Statistics.ErrorsBySeverity.Contains(Error.Severity))
    {
        Statistics.ErrorsBySeverity[Error.Severity]++;
    }
    else
    {
        Statistics.ErrorsBySeverity.Add(Error.Severity, 1);
    }
    
    // Update most common errors
    if (Statistics.MostCommonErrors.Contains(Error.Message))
    {
        Statistics.MostCommonErrors[Error.Message]++;
    }
    else
    {
        Statistics.MostCommonErrors.Add(Error.Message, 1);
    }
    
    // Update average recovery time
    if (Statistics.RecoveryAttempts > 0)
    {
        float TotalTime = Statistics.AverageRecoveryTime * (Statistics.RecoveryAttempts - 1) + Result.RecoveryTime;
        Statistics.AverageRecoveryTime = TotalTime / Statistics.RecoveryAttempts;
    }
    else
    {
        Statistics.AverageRecoveryTime = Result.RecoveryTime;
    }
}

// Event broadcasting methods
void FCompilationErrorHandler::BroadcastErrorDetected(const FCompilationError& Error, const FErrorContext& Context)
{
    OnErrorDetected.Broadcast(Error, Context);
}

void FCompilationErrorHandler::BroadcastErrorAnalyzed(const FCompilationError& Error, const FErrorAnalysisResult& Analysis)
{
    OnErrorAnalyzed.Broadcast(Error, Analysis);
}

void FCompilationErrorHandler::BroadcastAutoFixAttempted(const FCompilationError& Error, const FString& FixDescription)
{
    OnAutoFixAttempted.Broadcast(Error, FixDescription);
}

void FCompilationErrorHandler::BroadcastAutoFixCompleted(const FCompilationError& Error, const FErrorRecoveryResult& Result)
{
    OnAutoFixCompleted.Broadcast(Error, Result);
}

void FCompilationErrorHandler::BroadcastRecoveryAttempted(const FCompilationError& Error, EErrorRecoveryStrategy Strategy)
{
    OnRecoveryAttempted.Broadcast(Error, Strategy);
}

void FCompilationErrorHandler::BroadcastRecoveryCompleted(const FCompilationError& Error, const FErrorRecoveryResult& Result)
{
    OnRecoveryCompleted.Broadcast(Error, Result);
}

void FCompilationErrorHandler::BroadcastErrorResolved(const FCompilationError& Error)
{
    OnErrorResolved.Broadcast(Error);
}

void FCompilationErrorHandler::BroadcastErrorUnresolved(const FCompilationError& Error)
{
    OnErrorUnresolved.Broadcast(Error);
}

// Component initialization methods
void FCompilationErrorHandler::InitializeComponents()
{
    UE_LOG(LogCompilationErrorHandler, Log, TEXT("Initializing error handler components"));
}

void FCompilationErrorHandler::InitializeClassificationPatterns()
{
    // Initialize classification patterns for different error types
    ClassificationPatterns.Add(ECompilationErrorClass::Syntax, {
        TEXT("syntax error"), TEXT("parse error"), TEXT("unexpected token")
    });
    
    ClassificationPatterns.Add(ECompilationErrorClass::Type, {
        TEXT("type mismatch"), TEXT("incompatible types"), TEXT("cannot convert")
    });
    
    ClassificationPatterns.Add(ECompilationErrorClass::Connection, {
        TEXT("connection error"), TEXT("pin mismatch"), TEXT("invalid connection")
    });
    
    ClassificationPatterns.Add(ECompilationErrorClass::Property, {
        TEXT("property error"), TEXT("invalid value"), TEXT("property not found")
    });
    
    ClassificationPatterns.Add(ECompilationErrorClass::Dependency, {
        TEXT("dependency error"), TEXT("circular dependency"), TEXT("missing dependency")
    });
}

void FCompilationErrorHandler::InitializeAutoFixHandlers()
{
    // Initialize auto-fix handlers for different error classes
    AutoFixHandlers.Add(ECompilationErrorClass::Syntax, 
        [this](const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& Result) {
            return AutoFixSyntaxError(Error, Context, Result);
        });
    
    AutoFixHandlers.Add(ECompilationErrorClass::Property, 
        [this](const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& Result) {
            return AutoFixPropertyError(Error, Context, Result);
        });
}

void FCompilationErrorHandler::InitializeRecoveryHandlers()
{
    // Initialize recovery strategy handlers
    RecoveryHandlers.Add(EErrorRecoveryStrategy::AutoFix,
        [this](const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& Result) {
            return ExecuteAutoFixStrategy(Error, Context, Result);
        });
    
    RecoveryHandlers.Add(EErrorRecoveryStrategy::Skip,
        [this](const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& Result) {
            return ExecuteSkipStrategy(Error, Context, Result);
        });
    
    RecoveryHandlers.Add(EErrorRecoveryStrategy::Retry,
        [this](const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& Result) {
            return ExecuteRetryStrategy(Error, Context, Result);
        });
    
    RecoveryHandlers.Add(EErrorRecoveryStrategy::Fallback,
        [this](const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& Result) {
            return ExecuteFallbackStrategy(Error, Context, Result);
        });
}

void FCompilationErrorHandler::ShutdownComponents()
{
    UE_LOG(LogCompilationErrorHandler, Log, TEXT("Shutting down error handler components"));
}

// Placeholder implementations for remaining methods
TArray<FString> FCompilationErrorHandler::GetAutoFixSuggestions(const FCompilationError& Error, const FErrorContext& Context)
{
    TArray<FString> Suggestions;
    Suggestions.Add(TEXT("Check node connections"));
    Suggestions.Add(TEXT("Verify property values"));
    Suggestions.Add(TEXT("Review data types"));
    return Suggestions;
}

TArray<EErrorRecoveryStrategy> FCompilationErrorHandler::GetAvailableStrategies(const FCompilationError& Error, const FErrorContext& Context)
{
    TArray<EErrorRecoveryStrategy> Strategies;
    Strategies.Add(EErrorRecoveryStrategy::AutoFix);
    Strategies.Add(EErrorRecoveryStrategy::Skip);
    Strategies.Add(EErrorRecoveryStrategy::Retry);
    Strategies.Add(EErrorRecoveryStrategy::Fallback);
    return Strategies;
}

TArray<FString> FCompilationErrorHandler::GetPreventionSuggestions(const FCompilationError& Error, const FErrorContext& Context)
{
    TArray<FString> Suggestions;
    Suggestions.Add(TEXT("Use proper naming conventions"));
    Suggestions.Add(TEXT("Validate inputs before processing"));
    Suggestions.Add(TEXT("Follow blueprint best practices"));
    return Suggestions;
}

bool FCompilationErrorHandler::CanPreventError(const FCompilationError& Error, const FErrorContext& Context)
{
    // Most errors can be prevented with proper validation
    return true;
}

void FCompilationErrorHandler::AddCustomErrorPattern(const FString& Pattern, const FString& Description)
{
    Config.CustomErrorPatterns.Add(Pattern, Description);
}

void FCompilationErrorHandler::RemoveCustomErrorPattern(const FString& Pattern)
{
    Config.CustomErrorPatterns.Remove(Pattern);
}

bool FCompilationErrorHandler::MatchesCustomPattern(const FCompilationError& Error, FString& OutPattern)
{
    for (const auto& PatternPair : Config.CustomErrorPatterns)
    {
        if (Error.Message.Contains(PatternPair.Key))
        {
            OutPattern = PatternPair.Key;
            return true;
        }
    }
    return false;
}

void FCompilationErrorHandler::RegisterCustomRecoveryStrategy(const FString& StrategyName, TFunction<bool(const FCompilationError&, const FErrorContext&, FErrorRecoveryResult&)> StrategyFunction)
{
    CustomRecoveryStrategies.Add(StrategyName, StrategyFunction);
}

void FCompilationErrorHandler::UnregisterCustomRecoveryStrategy(const FString& StrategyName)
{
    CustomRecoveryStrategies.Remove(StrategyName);
}