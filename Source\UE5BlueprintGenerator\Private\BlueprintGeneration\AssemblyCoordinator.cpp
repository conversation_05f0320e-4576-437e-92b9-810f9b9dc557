#include "BlueprintGeneration/AssemblyCoordinator.h"
#include "Engine/Engine.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"

DEFINE_LOG_CATEGORY(LogAssemblyCoordinator);

// Singleton instance
static FAssemblyCoordinator* AssemblyCoordinatorInstance = nullptr;

FAssemblyCoordinator::FAssemblyCoordinator()
    : bIsAssemblyInProgress(false)
{
    InitializeComponents();
}

FAssemblyCoordinator::~FAssemblyCoordinator()
{
    ShutdownComponents();
}

FAssemblyCoordinator& FAssemblyCoordinator::Get()
{
    if (!AssemblyCoordinatorInstance)
    {
        AssemblyCoordinatorInstance = new FAssemblyCoordinator();
    }
    return *AssemblyCoordinatorInstance;
}

bool FAssemblyCoordinator::AssembleBlueprint(const FAssemblyRequest& Request, FAssemblyResult& OutResult)
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Starting blueprint assembly"));

    // Validate request
    if (!ValidateAssemblyRequest(Request))
    {
        OutResult.bSuccess = false;
        OutResult.ErrorMessages.Add(TEXT("Invalid assembly request"));
        return false;
    }

    // Check if assembly is already in progress
    if (bIsAssemblyInProgress)
    {
        OutResult.bSuccess = false;
        OutResult.ErrorMessages.Add(TEXT("Assembly already in progress"));
        return false;
    }

    // Initialize assembly
    CurrentRequest = Request;
    CurrentResult = FAssemblyResult();
    bIsAssemblyInProgress = true;
    AssemblyStartTime = FDateTime::Now();

    // Start performance monitoring if enabled
    if (Config.bEnablePerformanceMonitoring)
    {
        StartPerformanceMonitoring();
    }

    bool bSuccess = false;

    try
    {
        // Execute assembly pipeline
        bSuccess = InitializeAssembly(Request) &&
                  CreateBlueprintAsset() &&
                  AnalyzeStructure() &&
                  ResolveDependencies() &&
                  CreateNodes() &&
                  ConfigureProperties() &&
                  CalculatePositions() &&
                  EstablishConnections() &&
                  ValidateBlueprint() &&
                  FinalizeAssembly();
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAssemblyCoordinator, Error, TEXT("Assembly failed with exception: %s"), *FString(e.what()));
        bSuccess = false;
    }

    // Finalize result
    CurrentResult.bSuccess = bSuccess;
    CurrentProgress.CurrentPhase = bSuccess ? EAssemblyPhase::Complete : EAssemblyPhase::Failed;
    
    // Stop performance monitoring
    if (Config.bEnablePerformanceMonitoring)
    {
        StopPerformanceMonitoring();
    }

    // Update statistics
    UpdateStatistics(CurrentResult);

    // Broadcast completion
    BroadcastAssemblyCompletion();

    // Copy result
    OutResult = CurrentResult;
    
    // Reset state
    bIsAssemblyInProgress = false;

    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Blueprint assembly completed. Success: %s"), 
           bSuccess ? TEXT("true") : TEXT("false"));

    return bSuccess;
}

bool FAssemblyCoordinator::InitializeAssembly(const FAssemblyRequest& Request)
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Initializing assembly"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::Initialization;
    BroadcastPhaseChange(EAssemblyPhase::Initialization);

    // Create assembly tasks
    CreateAssemblyTasks();

    // Initialize components with request data
    Config = Request.Config;

    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::CreateBlueprintAsset()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Creating blueprint asset"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::BlueprintCreation;
    BroadcastPhaseChange(EAssemblyPhase::BlueprintCreation);

    if (!BlueprintAssetCreator.IsValid())
    {
        HandleAssemblyError(TEXT("Blueprint Asset Creator not initialized"), EAssemblyPhase::BlueprintCreation);
        return false;
    }

    // Create the blueprint asset
    FBlueprintCreationResult CreationResult;
    bool bSuccess = BlueprintAssetCreator->CreateBlueprint(CurrentRequest.BlueprintRequest, CreationResult);

    if (bSuccess && CreationResult.CreatedBlueprint)
    {
        CurrentResult.CreatedBlueprint = CreationResult.CreatedBlueprint;
        UpdateTaskProgress(TEXT("CreateBlueprint"), true);
    }
    else
    {
        HandleAssemblyError(CreationResult.ErrorMessage, EAssemblyPhase::BlueprintCreation);
        return false;
    }

    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::AnalyzeStructure()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Analyzing blueprint structure"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::StructureAnalysis;
    BroadcastPhaseChange(EAssemblyPhase::StructureAnalysis);

    // Structure analysis is already done in the request
    // Here we validate and prepare the structure for assembly

    const FExtractedBlueprintStructure& Structure = CurrentRequest.ExtractedStructure;
    
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Structure contains: %d variables, %d functions, %d events, %d components"),
           Structure.Variables.Num(), Structure.Functions.Num(), Structure.Events.Num(), Structure.Components.Num());

    UpdateTaskProgress(TEXT("AnalyzeStructure"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::ResolveDependencies()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Resolving dependencies"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::DependencyResolution;
    BroadcastPhaseChange(EAssemblyPhase::DependencyResolution);

    // Use dependency analysis results from the request
    const FDependencyAnalysisResult& Dependencies = CurrentRequest.DependencyAnalysis;
    
    if (Dependencies.bHasCircularDependencies)
    {
        UE_LOG(LogAssemblyCoordinator, Warning, TEXT("Circular dependencies detected: %d"), 
               Dependencies.CircularDependencies.Num());
        
        // Add warnings but continue
        for (const FCircularDependency& CircularDep : Dependencies.CircularDependencies)
        {
            CurrentResult.WarningMessages.Add(FString::Printf(TEXT("Circular dependency: %s"), 
                                                             *CircularDep.Description));
        }
    }

    UpdateTaskProgress(TEXT("ResolveDependencies"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::CreateNodes()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Creating blueprint nodes"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::NodeCreation;
    BroadcastPhaseChange(EAssemblyPhase::NodeCreation);

    if (!NodeFactory.IsValid() || !CurrentResult.CreatedBlueprint)
    {
        HandleAssemblyError(TEXT("Node Factory not initialized or blueprint not created"), EAssemblyPhase::NodeCreation);
        return false;
    }

    // Create nodes based on extracted structure
    const FExtractedBlueprintStructure& Structure = CurrentRequest.ExtractedStructure;
    
    // Create variable nodes
    for (const FExtractedVariable& Variable : Structure.Variables)
    {
        // Create variable get/set nodes as needed
        UE_LOG(LogAssemblyCoordinator, Verbose, TEXT("Creating variable: %s"), *Variable.Name);
    }

    // Create function nodes
    for (const FExtractedFunction& Function : Structure.Functions)
    {
        // Create function call nodes
        UE_LOG(LogAssemblyCoordinator, Verbose, TEXT("Creating function: %s"), *Function.Name);
    }

    // Create event nodes
    for (const FExtractedEvent& Event : Structure.Events)
    {
        // Create event nodes
        UE_LOG(LogAssemblyCoordinator, Verbose, TEXT("Creating event: %s"), *Event.Name);
    }

    UpdateTaskProgress(TEXT("CreateNodes"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::ConfigureProperties()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Configuring node properties"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::PropertyConfiguration;
    BroadcastPhaseChange(EAssemblyPhase::PropertyConfiguration);

    if (!PropertyManager.IsValid())
    {
        HandleAssemblyError(TEXT("Property Manager not initialized"), EAssemblyPhase::PropertyConfiguration);
        return false;
    }

    // Configure properties for created nodes
    // This would involve setting default values, configuring pin visibility, etc.

    UpdateTaskProgress(TEXT("ConfigureProperties"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::CalculatePositions()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Calculating node positions"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::PositionCalculation;
    BroadcastPhaseChange(EAssemblyPhase::PositionCalculation);

    if (!PositionCalculator.IsValid())
    {
        HandleAssemblyError(TEXT("Position Calculator not initialized"), EAssemblyPhase::PositionCalculation);
        return false;
    }

    // Calculate optimal positions for all nodes
    // This would use the layout algorithms we implemented

    UpdateTaskProgress(TEXT("CalculatePositions"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::EstablishConnections()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Establishing node connections"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::ConnectionEstablishment;
    BroadcastPhaseChange(EAssemblyPhase::ConnectionEstablishment);

    if (!ConnectionManager.IsValid())
    {
        HandleAssemblyError(TEXT("Connection Manager not initialized"), EAssemblyPhase::ConnectionEstablishment);
        return false;
    }

    // Establish connections between nodes based on the extracted structure
    // This would use the pin compatibility checker and connection manager

    UpdateTaskProgress(TEXT("EstablishConnections"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

bool FAssemblyCoordinator::ValidateBlueprint()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Validating blueprint"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::Validation;
    BroadcastPhaseChange(EAssemblyPhase::Validation);

    if (!GraphValidator.IsValid() || !CurrentResult.CreatedBlueprint)
    {
        HandleAssemblyError(TEXT("Graph Validator not initialized or blueprint not created"), EAssemblyPhase::Validation);
        return false;
    }

    // Validate the assembled blueprint
    FGraphValidationRequest ValidationRequest;
    ValidationRequest.Blueprint = CurrentResult.CreatedBlueprint;
    ValidationRequest.bValidateStructure = true;
    ValidationRequest.bValidateConnections = true;
    ValidationRequest.bValidateLogicFlow = true;

    FGraphValidationResult ValidationResult;
    bool bValidationSuccess = GraphValidator->ValidateGraph(ValidationRequest, ValidationResult);

    CurrentResult.ValidationResult = ValidationResult;

    if (!bValidationSuccess || ValidationResult.OverallQualityScore < 0.5f)
    {
        UE_LOG(LogAssemblyCoordinator, Warning, TEXT("Blueprint validation failed or quality score too low: %f"), 
               ValidationResult.OverallQualityScore);
        
        // Add validation issues as warnings
        for (const FValidationIssue& Issue : ValidationResult.Issues)
        {
            if (Issue.Severity == EValidationSeverity::Error || Issue.Severity == EValidationSeverity::Critical)
            {
                CurrentResult.ErrorMessages.Add(Issue.Message);
            }
            else
            {
                CurrentResult.WarningMessages.Add(Issue.Message);
            }
        }
    }

    UpdateTaskProgress(TEXT("ValidateBlueprint"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    UpdateOverallProgress();

    return bValidationSuccess;
}

bool FAssemblyCoordinator::FinalizeAssembly()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Finalizing assembly"));
    
    CurrentProgress.CurrentPhase = EAssemblyPhase::Finalization;
    BroadcastPhaseChange(EAssemblyPhase::Finalization);

    if (!CurrentResult.CreatedBlueprint)
    {
        HandleAssemblyError(TEXT("No blueprint was created"), EAssemblyPhase::Finalization);
        return false;
    }

    // Compile the blueprint if auto-compilation is enabled
    if (CurrentRequest.BlueprintRequest.bAutoCompile)
    {
        UE_LOG(LogAssemblyCoordinator, Log, TEXT("Compiling blueprint"));
        // Blueprint compilation would happen here
    }

    // Save the blueprint if auto-save is enabled
    if (CurrentRequest.BlueprintRequest.bAutoSave)
    {
        UE_LOG(LogAssemblyCoordinator, Log, TEXT("Saving blueprint"));
        // Blueprint saving would happen here
    }

    UpdateTaskProgress(TEXT("FinalizeAssembly"), true);
    CurrentProgress.PhaseProgress = 1.0f;
    CurrentProgress.OverallProgress = 1.0f;
    UpdateOverallProgress();

    return true;
}

void FAssemblyCoordinator::CreateAssemblyTasks()
{
    CurrentTasks.Empty();

    // Create tasks for each phase
    FAssemblyTask InitTask;
    InitTask.TaskId = TEXT("Initialize");
    InitTask.Description = TEXT("Initialize assembly process");
    InitTask.Priority = EAssemblyPriority::Critical;
    InitTask.EstimatedTime = 1.0f;
    CurrentTasks.Add(InitTask);

    FAssemblyTask CreateBlueprintTask;
    CreateBlueprintTask.TaskId = TEXT("CreateBlueprint");
    CreateBlueprintTask.Description = TEXT("Create blueprint asset");
    CreateBlueprintTask.Priority = EAssemblyPriority::Critical;
    CreateBlueprintTask.Dependencies.Add(TEXT("Initialize"));
    CreateBlueprintTask.EstimatedTime = 2.0f;
    CurrentTasks.Add(CreateBlueprintTask);

    FAssemblyTask AnalyzeTask;
    AnalyzeTask.TaskId = TEXT("AnalyzeStructure");
    AnalyzeTask.Description = TEXT("Analyze blueprint structure");
    AnalyzeTask.Priority = EAssemblyPriority::High;
    AnalyzeTask.Dependencies.Add(TEXT("CreateBlueprint"));
    AnalyzeTask.EstimatedTime = 1.5f;
    CurrentTasks.Add(AnalyzeTask);

    FAssemblyTask DependencyTask;
    DependencyTask.TaskId = TEXT("ResolveDependencies");
    DependencyTask.Description = TEXT("Resolve dependencies");
    DependencyTask.Priority = EAssemblyPriority::High;
    DependencyTask.Dependencies.Add(TEXT("AnalyzeStructure"));
    DependencyTask.EstimatedTime = 1.0f;
    CurrentTasks.Add(DependencyTask);

    FAssemblyTask CreateNodesTask;
    CreateNodesTask.TaskId = TEXT("CreateNodes");
    CreateNodesTask.Description = TEXT("Create blueprint nodes");
    CreateNodesTask.Priority = EAssemblyPriority::Normal;
    CreateNodesTask.Dependencies.Add(TEXT("ResolveDependencies"));
    CreateNodesTask.EstimatedTime = 5.0f;
    CurrentTasks.Add(CreateNodesTask);

    FAssemblyTask ConfigureTask;
    ConfigureTask.TaskId = TEXT("ConfigureProperties");
    ConfigureTask.Description = TEXT("Configure node properties");
    ConfigureTask.Priority = EAssemblyPriority::Normal;
    ConfigureTask.Dependencies.Add(TEXT("CreateNodes"));
    ConfigureTask.EstimatedTime = 3.0f;
    CurrentTasks.Add(ConfigureTask);

    FAssemblyTask PositionTask;
    PositionTask.TaskId = TEXT("CalculatePositions");
    PositionTask.Description = TEXT("Calculate node positions");
    PositionTask.Priority = EAssemblyPriority::Low;
    PositionTask.Dependencies.Add(TEXT("ConfigureProperties"));
    PositionTask.EstimatedTime = 2.0f;
    CurrentTasks.Add(PositionTask);

    FAssemblyTask ConnectionTask;
    ConnectionTask.TaskId = TEXT("EstablishConnections");
    ConnectionTask.Description = TEXT("Establish node connections");
    ConnectionTask.Priority = EAssemblyPriority::Normal;
    ConnectionTask.Dependencies.Add(TEXT("CalculatePositions"));
    ConnectionTask.EstimatedTime = 4.0f;
    CurrentTasks.Add(ConnectionTask);

    FAssemblyTask ValidateTask;
    ValidateTask.TaskId = TEXT("ValidateBlueprint");
    ValidateTask.Description = TEXT("Validate blueprint");
    ValidateTask.Priority = EAssemblyPriority::High;
    ValidateTask.Dependencies.Add(TEXT("EstablishConnections"));
    ValidateTask.EstimatedTime = 2.0f;
    CurrentTasks.Add(ValidateTask);

    FAssemblyTask FinalizeTask;
    FinalizeTask.TaskId = TEXT("FinalizeAssembly");
    FinalizeTask.Description = TEXT("Finalize assembly");
    FinalizeTask.Priority = EAssemblyPriority::Critical;
    FinalizeTask.Dependencies.Add(TEXT("ValidateBlueprint"));
    FinalizeTask.EstimatedTime = 1.0f;
    CurrentTasks.Add(FinalizeTask);

    // Calculate total estimated time
    CurrentProgress.TotalTasks = CurrentTasks.Num();
    CurrentProgress.EstimatedTotalTime = 0.0f;
    for (const FAssemblyTask& Task : CurrentTasks)
    {
        CurrentProgress.EstimatedTotalTime += Task.EstimatedTime;
    }
}

void FAssemblyCoordinator::UpdateTaskProgress(const FString& TaskId, bool bCompleted, bool bFailed, const FString& ErrorMessage)
{
    FAssemblyTask* Task = FindTask(TaskId);
    if (Task)
    {
        Task->bIsCompleted = bCompleted;
        Task->bHasFailed = bFailed;
        Task->ErrorMessage = ErrorMessage;
        
        if (bCompleted)
        {
            CurrentProgress.CompletedTasks++;
        }
        if (bFailed)
        {
            CurrentProgress.FailedTasks++;
        }

        BroadcastTaskCompletion(*Task);
        UpdateOverallProgress();
    }
}

void FAssemblyCoordinator::UpdateOverallProgress()
{
    if (CurrentProgress.TotalTasks > 0)
    {
        CurrentProgress.OverallProgress = static_cast<float>(CurrentProgress.CompletedTasks) / CurrentProgress.TotalTasks;
    }

    // Calculate elapsed time
    if (bIsAssemblyInProgress)
    {
        FTimespan ElapsedTimespan = FDateTime::Now() - AssemblyStartTime;
        CurrentProgress.ElapsedTime = ElapsedTimespan.GetTotalSeconds();
        
        // Estimate remaining time
        if (CurrentProgress.OverallProgress > 0.0f)
        {
            float EstimatedTotalTime = CurrentProgress.ElapsedTime / CurrentProgress.OverallProgress;
            CurrentProgress.EstimatedRemainingTime = EstimatedTotalTime - CurrentProgress.ElapsedTime;
        }
    }

    BroadcastProgressUpdate();
}

FAssemblyTask* FAssemblyCoordinator::FindTask(const FString& TaskId)
{
    for (FAssemblyTask& Task : CurrentTasks)
    {
        if (Task.TaskId == TaskId)
        {
            return &Task;
        }
    }
    return nullptr;
}

bool FAssemblyCoordinator::HandleAssemblyError(const FString& ErrorMessage, EAssemblyPhase Phase)
{
    UE_LOG(LogAssemblyCoordinator, Error, TEXT("Assembly error in phase %d: %s"), 
           static_cast<int32>(Phase), *ErrorMessage);

    CurrentResult.ErrorMessages.Add(ErrorMessage);
    BroadcastAssemblyError(ErrorMessage, Phase);

    if (Config.bEnableErrorRecovery)
    {
        return AttemptErrorRecovery();
    }

    return false;
}

bool FAssemblyCoordinator::AttemptErrorRecovery()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Attempting error recovery"));
    
    // Basic error recovery strategies could be implemented here
    // For now, we just log the attempt
    
    return false; // Recovery not implemented yet
}

void FAssemblyCoordinator::InitializeComponents()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Initializing assembly components"));

    BlueprintAssetCreator = MakeShared<FBlueprintAssetCreator>();
    TypeResolver = MakeShared<FBlueprintTypeResolver>();
    NodeFactory = MakeShared<FNodeFactory>();
    NodeTypeRegistry = MakeShared<FNodeTypeRegistry>();
    PropertyManager = MakeShared<FNodePropertyManager>();
    PositionCalculator = MakeShared<FNodePositionCalculator>();
    CompatibilityChecker = MakeShared<FPinCompatibilityChecker>();
    ConnectionManager = MakeShared<FConnectionManager>();
    GraphValidator = MakeShared<FGraphValidator>();
}

void FAssemblyCoordinator::ShutdownComponents()
{
    UE_LOG(LogAssemblyCoordinator, Log, TEXT("Shutting down assembly components"));

    BlueprintAssetCreator.Reset();
    TypeResolver.Reset();
    NodeFactory.Reset();
    NodeTypeRegistry.Reset();
    PropertyManager.Reset();
    PositionCalculator.Reset();
    CompatibilityChecker.Reset();
    ConnectionManager.Reset();
    GraphValidator.Reset();
}

bool FAssemblyCoordinator::ValidateAssemblyRequest(const FAssemblyRequest& Request)
{
    // Validate blueprint request
    if (Request.BlueprintRequest.BlueprintName.IsEmpty())
    {
        UE_LOG(LogAssemblyCoordinator, Error, TEXT("Blueprint name is empty"));
        return false;
    }

    // Validate extracted structure
    if (Request.ExtractedStructure.Variables.Num() == 0 && 
        Request.ExtractedStructure.Functions.Num() == 0 && 
        Request.ExtractedStructure.Events.Num() == 0)
    {
        UE_LOG(LogAssemblyCoordinator, Warning, TEXT("Extracted structure is empty"));
    }

    return true;
}

void FAssemblyCoordinator::StartPerformanceMonitoring()
{
    UE_LOG(LogAssemblyCoordinator, Verbose, TEXT("Starting performance monitoring"));
    // Performance monitoring implementation
}

void FAssemblyCoordinator::StopPerformanceMonitoring()
{
    UE_LOG(LogAssemblyCoordinator, Verbose, TEXT("Stopping performance monitoring"));
    // Performance monitoring implementation
}

void FAssemblyCoordinator::UpdateStatistics(const FAssemblyResult& Result)
{
    Statistics.TotalAssemblies++;
    
    if (Result.bSuccess)
    {
        Statistics.SuccessfulAssemblies++;
    }
    else
    {
        Statistics.FailedAssemblies++;
    }

    // Update timing statistics
    float AssemblyTime = CurrentProgress.ElapsedTime;
    if (Statistics.TotalAssemblies == 1)
    {
        Statistics.FastestAssemblyTime = AssemblyTime;
        Statistics.SlowestAssemblyTime = AssemblyTime;
        Statistics.AverageAssemblyTime = AssemblyTime;
    }
    else
    {
        Statistics.FastestAssemblyTime = FMath::Min(Statistics.FastestAssemblyTime, AssemblyTime);
        Statistics.SlowestAssemblyTime = FMath::Max(Statistics.SlowestAssemblyTime, AssemblyTime);
        
        // Update average
        float TotalTime = Statistics.AverageAssemblyTime * (Statistics.TotalAssemblies - 1) + AssemblyTime;
        Statistics.AverageAssemblyTime = TotalTime / Statistics.TotalAssemblies;
    }
}

// Delegate broadcast methods
void FAssemblyCoordinator::BroadcastPhaseChange(EAssemblyPhase NewPhase)
{
    OnAssemblyPhaseChanged.Broadcast(NewPhase);
}

void FAssemblyCoordinator::BroadcastProgressUpdate()
{
    OnAssemblyProgressUpdated.Broadcast(CurrentProgress);
}

void FAssemblyCoordinator::BroadcastTaskCompletion(const FAssemblyTask& Task)
{
    OnAssemblyTaskCompleted.Broadcast(Task);
}

void FAssemblyCoordinator::BroadcastAssemblyCompletion()
{
    OnAssemblyCompleted.Broadcast(CurrentResult);
}

void FAssemblyCoordinator::BroadcastAssemblyError(const FString& ErrorMessage, EAssemblyPhase Phase)
{
    OnAssemblyError.Broadcast(ErrorMessage, Phase);
}

// Public interface methods
bool FAssemblyCoordinator::IsAssemblyInProgress() const
{
    return bIsAssemblyInProgress;
}

void FAssemblyCoordinator::CancelAssembly()
{
    if (bIsAssemblyInProgress)
    {
        UE_LOG(LogAssemblyCoordinator, Log, TEXT("Cancelling assembly"));
        bIsAssemblyInProgress = false;
        CurrentProgress.CurrentPhase = EAssemblyPhase::Failed;
        CurrentResult.bSuccess = false;
        CurrentResult.ErrorMessages.Add(TEXT("Assembly was cancelled"));
        BroadcastAssemblyCompletion();
    }
}

FAssemblyProgress FAssemblyCoordinator::GetCurrentProgress() const
{
    return CurrentProgress;
}

TArray<FAssemblyTask> FAssemblyCoordinator::GetCurrentTasks() const
{
    return CurrentTasks;
}

EAssemblyPhase FAssemblyCoordinator::GetCurrentPhase() const
{
    return CurrentProgress.CurrentPhase;
}

void FAssemblyCoordinator::SetAssemblyConfig(const FAssemblyConfig& NewConfig)
{
    Config = NewConfig;
}

FAssemblyConfig FAssemblyCoordinator::GetAssemblyConfig() const
{
    return Config;
}

FAssemblyStatistics FAssemblyCoordinator::GetStatistics() const
{
    return Statistics;
}

void FAssemblyCoordinator::ResetStatistics()
{
    Statistics = FAssemblyStatistics();
} 