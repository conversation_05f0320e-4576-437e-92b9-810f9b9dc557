#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "CodeGenerationFramework.h"
#include "BlueprintToCppConverter.h"

DECLARE_LOG_CATEGORY_EXTERN(LogExportManager, Log, All);

// Forward declarations
class UBlueprint;
class UBlueprintToCppConverter;

/**
 * Enumeration for export formats
 */
UENUM(BlueprintType)
enum class EExportFormat : uint8
{
    CppProject,         // Complete C++ project structure
    CppFiles,           // Individual C++ files
    VisualStudio,       // Visual Studio project
    CMake,              // CMake project
    UnrealProject,      // Unreal Engine project
    Archive,            // Compressed archive
    Documentation,      // Documentation files
    Custom              // Custom export format
};

/**
 * Enumeration for export targets
 */
UENUM(BlueprintType)
enum class EExportTarget : uint8
{
    LocalDirectory,     // Local file system directory
    NetworkShare,       // Network shared directory
    CloudStorage,       // Cloud storage service
    VersionControl,     // Version control system
    Email,              // Email attachment
    FTP,                // FTP server
    Custom              // Custom export target
};

/**
 * Enumeration for export compression types
 */
UENUM(BlueprintType)
enum class EExportCompression : uint8
{
    None,               // No compression
    ZIP,                // ZIP compression
    RAR,                // RAR compression
    SevenZip,           // 7-Zip compression
    TAR,                // TAR archive
    GZIP,               // GZIP compression
    Custom              // Custom compression
};

/**
 * Structure for export file information
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FExportFile
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    FString FileName;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    FString FilePath;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    FString Content;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    FString FileType;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    int64 FileSize;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    FDateTime CreationTime;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    FDateTime ModificationTime;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    TMap<FString, FString> Metadata;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    bool bIsGenerated;

    UPROPERTY(BlueprintReadWrite, Category = "Export File")
    bool bIsCompressed;

    FExportFile()
        : FileSize(0)
        , bIsGenerated(false)
        , bIsCompressed(false)
    {}
};

/**
 * Structure for export package information
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FExportPackage
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    FString PackageName;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    FString PackagePath;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    EExportFormat Format;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    EExportCompression Compression;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    TArray<FExportFile> Files;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    TMap<FString, FString> Properties;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    FString Description;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    FString Version;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    int64 TotalSize;

    UPROPERTY(BlueprintReadWrite, Category = "Export Package")
    FDateTime CreationTime;

    FExportPackage()
        : Format(EExportFormat::CppFiles)
        , Compression(EExportCompression::None)
        , TotalSize(0)
    {}
};

/**
 * Structure for export configuration
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FExportConfig
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    EExportFormat Format;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    EExportTarget Target;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    EExportCompression Compression;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    FString OutputDirectory;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    FString PackageName;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bIncludeDocumentation;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bIncludeSourceCode;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bIncludeAssets;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bIncludeProjectFiles;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bCreateBackup;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bOverwriteExisting;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bValidateBeforeExport;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    bool bGenerateManifest;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    TArray<FString> IncludePatterns;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    TArray<FString> ExcludePatterns;

    UPROPERTY(BlueprintReadWrite, Category = "Export Config")
    TMap<FString, FString> CustomProperties;

    FExportConfig()
        : Format(EExportFormat::CppFiles)
        , Target(EExportTarget::LocalDirectory)
        , Compression(EExportCompression::None)
        , bIncludeDocumentation(true)
        , bIncludeSourceCode(true)
        , bIncludeAssets(false)
        , bIncludeProjectFiles(true)
        , bCreateBackup(true)
        , bOverwriteExisting(false)
        , bValidateBeforeExport(true)
        , bGenerateManifest(true)
    {}
};

/**
 * Structure for export request
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FExportRequest
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Export Request")
    FString RequestId;

    UPROPERTY(BlueprintReadWrite, Category = "Export Request")
    TArray<TWeakObjectPtr<UBlueprint>> Blueprints;

    UPROPERTY(BlueprintReadWrite, Category = "Export Request")
    TArray<FGeneratedCodeFile> GeneratedFiles;

    UPROPERTY(BlueprintReadWrite, Category = "Export Request")
    FExportConfig Config;

    UPROPERTY(BlueprintReadWrite, Category = "Export Request")
    int32 Priority;

    UPROPERTY(BlueprintReadWrite, Category = "Export Request")
    float Timestamp;

    UPROPERTY(BlueprintReadWrite, Category = "Export Request")
    TMap<FString, FString> Metadata;

    FExportRequest()
        : Priority(0)
        , Timestamp(0.0f)
    {}
};

/**
 * Structure for export result
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FExportResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    FString RequestId;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    bool bSuccess;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    FExportPackage ExportedPackage;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    TArray<FString> ExportedFiles;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    float ExportTime;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    int64 TotalSize;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    int32 FileCount;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    float CompressionRatio;

    UPROPERTY(BlueprintReadWrite, Category = "Export Result")
    FString ManifestPath;

    FExportResult()
        : bSuccess(false)
        , ExportTime(0.0f)
        , TotalSize(0)
        , FileCount(0)
        , CompressionRatio(1.0f)
    {}
};

/**
 * Structure for export statistics
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FExportStatistics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    int32 TotalExports;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    int32 SuccessfulExports;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    int32 FailedExports;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    float AverageExportTime;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    int64 TotalDataExported;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    float AverageCompressionRatio;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    TMap<FString, int32> FormatUsage;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    TMap<FString, int32> TargetUsage;

    UPROPERTY(BlueprintReadWrite, Category = "Export Statistics")
    TMap<FString, int32> CompressionUsage;

    FExportStatistics()
        : TotalExports(0)
        , SuccessfulExports(0)
        , FailedExports(0)
        , AverageExportTime(0.0f)
        , TotalDataExported(0)
        , AverageCompressionRatio(1.0f)
    {}
};

// Event delegates for export monitoring
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnExportStarted, const FString&, RequestId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnExportProgress, const FString&, RequestId, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnExportFileGenerated, const FString&, RequestId, const FString&, FilePath);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnExportCompleted, const FString&, RequestId, const FExportResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnExportError, const FString&, RequestId, const FString&, ErrorMessage);

/**
 * Interface for export handlers
 */
class BLUEPRINTGENERATOR_API IExportHandler
{
public:
    virtual ~IExportHandler() = default;

    /**
     * Export files using this handler
     */
    virtual FExportResult ExportFiles(const FExportRequest& Request) = 0;

    /**
     * Get handler name
     */
    virtual FString GetHandlerName() const = 0;

    /**
     * Get supported export formats
     */
    virtual TArray<EExportFormat> GetSupportedFormats() const = 0;

    /**
     * Get supported export targets
     */
    virtual TArray<EExportTarget> GetSupportedTargets() const = 0;

    /**
     * Get handler priority (higher = preferred)
     */
    virtual int32 GetPriority() const = 0;

    /**
     * Check if handler can handle the request
     */
    virtual bool CanHandleRequest(const FExportRequest& Request) const = 0;

    /**
     * Validate export request
     */
    virtual bool ValidateRequest(const FExportRequest& Request, FString& ErrorMessage) const = 0;
};

/**
 * Main Export Manager class
 * Handles coordination and management of code export operations
 */
UCLASS(BlueprintType, Blueprintable)
class BLUEPRINTGENERATOR_API UExportManager : public UObject
{
    GENERATED_BODY()

public:
    UExportManager();

    /**
     * Get singleton instance
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    static UExportManager* GetInstance();

    /**
     * Export generated code
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    FExportResult ExportCode(const FExportRequest& Request);

    /**
     * Export generated code asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    FString ExportCodeAsync(const FExportRequest& Request);

    /**
     * Register an export handler
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    bool RegisterHandler(TSharedPtr<IExportHandler> Handler);

    /**
     * Unregister an export handler
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    bool UnregisterHandler(const FString& HandlerName);

    /**
     * Get available export handlers
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    TArray<FString> GetAvailableHandlers() const;

    /**
     * Get supported export formats
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    TArray<FString> GetSupportedFormats() const;

    /**
     * Get supported export targets
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    TArray<FString> GetSupportedTargets() const;

    /**
     * Validate export request
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    bool ValidateRequest(const FExportRequest& Request, FString& ErrorMessage) const;

    /**
     * Create export package
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    FExportPackage CreatePackage(const FExportRequest& Request) const;

    /**
     * Compress export package
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    bool CompressPackage(FExportPackage& Package, EExportCompression CompressionType) const;

    /**
     * Generate export manifest
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    FString GenerateManifest(const FExportPackage& Package) const;

    /**
     * Get export statistics
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    FExportStatistics GetStatistics() const;

    /**
     * Reset statistics
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    void ResetStatistics();

    /**
     * Cancel export request
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    bool CancelExport(const FString& RequestId);

    /**
     * Get active export requests
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    TArray<FString> GetActiveRequests() const;

    /**
     * Clean up temporary files
     */
    UFUNCTION(BlueprintCallable, Category = "Export Manager")
    void CleanupTemporaryFiles();

    // Event delegates
    UPROPERTY(BlueprintAssignable, Category = "Export Manager")
    FOnExportStarted OnExportStarted;

    UPROPERTY(BlueprintAssignable, Category = "Export Manager")
    FOnExportProgress OnExportProgress;

    UPROPERTY(BlueprintAssignable, Category = "Export Manager")
    FOnExportFileGenerated OnExportFileGenerated;

    UPROPERTY(BlueprintAssignable, Category = "Export Manager")
    FOnExportCompleted OnExportCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Export Manager")
    FOnExportError OnExportError;

protected:
    /**
     * Find best export handler for request
     */
    TSharedPtr<IExportHandler> FindBestHandler(const FExportRequest& Request) const;

    /**
     * Prepare export directory
     */
    bool PrepareExportDirectory(const FString& Directory) const;

    /**
     * Generate project files
     */
    TArray<FExportFile> GenerateProjectFiles(const FExportRequest& Request) const;

    /**
     * Generate documentation files
     */
    TArray<FExportFile> GenerateDocumentationFiles(const FExportRequest& Request) const;

    /**
     * Apply file filters
     */
    TArray<FExportFile> ApplyFileFilters(const TArray<FExportFile>& Files, const FExportConfig& Config) const;

    /**
     * Calculate package size
     */
    int64 CalculatePackageSize(const FExportPackage& Package) const;

    /**
     * Update statistics
     */
    void UpdateStatistics(const FExportResult& Result);

    /**
     * Generate unique request ID
     */
    FString GenerateRequestId() const;

private:
    // Singleton instance
    static UExportManager* Instance;

    // Registered export handlers
    TArray<TSharedPtr<IExportHandler>> RegisteredHandlers;

    // Active export requests
    TMap<FString, FExportRequest> ActiveRequests;

    // Export statistics
    FExportStatistics Statistics;

    // Temporary files for cleanup
    TArray<FString> TemporaryFiles;

    // Thread safety
    mutable FCriticalSection CriticalSection;
};

#include "ExportManager.generated.h"