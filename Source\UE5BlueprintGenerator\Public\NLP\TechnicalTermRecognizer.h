#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

/**
 * Structure representing a technical term with its category and description
 */
USTRUCT()
struct UE5BLUEPRINTGENERATOR_API FTechnicalTerm
{
	GENERATED_BODY()

	/** The technical term itself */
	UPROPERTY()
	FString Term;

	/** Category this term belongs to (e.g., "Blueprint", "Data Type", "UE5 Class") */
	UPROPERTY()
	FString Category;

	/** Description of what this term means */
	UPROPERTY()
	FString Description;

	/** Alternative names or synonyms for this term */
	UPROPERTY()
	TArray<FString> Synonyms;

	/** Confidence weight for this term (higher = more important) */
	UPROPERTY()
	float Weight;

	FTechnicalTerm()
		: Weight(1.0f)
	{
	}

	FTechnicalTerm(const FString& InTerm, const FString& InCategory, const FString& InDescription, float InWeight = 1.0f)
		: Term(InTerm)
		, Category(InCategory)
		, Description(InDescription)
		, Weight(InWeight)
	{
	}
};

/**
 * Result of technical term recognition analysis
 */
USTRUCT()
struct UE5BLUEPRINTGENERATOR_API FTermRecognitionResult
{
	GENERATED_BODY()

	/** All technical terms found in the input */
	UPROPERTY()
	TArray<FTechnicalTerm> RecognizedTerms;

	/** Categories of terms found */
	UPROPERTY()
	TArray<FString> Categories;

	/** Overall technical complexity score (0.0 to 1.0) */
	UPROPERTY()
	float TechnicalComplexity;

	/** Whether the input contains UE5-specific terminology */
	UPROPERTY()
	bool bContainsUE5Terms;

	/** Whether the input contains Blueprint-specific terminology */
	UPROPERTY()
	bool bContainsBlueprintTerms;

	FTermRecognitionResult()
		: TechnicalComplexity(0.0f)
		, bContainsUE5Terms(false)
		, bContainsBlueprintTerms(false)
	{
	}
};

/**
 * Recognizes and analyzes technical terms in natural language input
 * Specializes in UE5 and Blueprint terminology
 */
class UE5BLUEPRINTGENERATOR_API FTechnicalTermRecognizer
{
public:
	FTechnicalTermRecognizer();
	~FTechnicalTermRecognizer();

	// Term recognition
	FTermRecognitionResult RecognizeTerms(const FString& InputText);
	TArray<FTechnicalTerm> GetRecognizedTerms(const FString& InputText);
	bool ContainsTechnicalTerms(const FString& InputText) const;

	// Category analysis
	TArray<FString> GetRecognizedCategories(const FString& InputText);
	bool ContainsCategory(const FString& InputText, const FString& Category) const;
	float GetCategoryWeight(const FString& InputText, const FString& Category);

	// Complexity analysis
	float CalculateTechnicalComplexity(const FString& InputText);
	bool IsHighlyTechnical(const FString& InputText) const;

	// Term database management
	void LoadTermDatabase();
	void AddCustomTerm(const FTechnicalTerm& Term);
	void RemoveTerm(const FString& TermName);
	TArray<FTechnicalTerm> GetTermsByCategory(const FString& Category) const;
	TArray<FString> GetAllCategories() const;

	// Term lookup
	bool IsTermInDatabase(const FString& Term) const;
	FTechnicalTerm GetTermInfo(const FString& Term) const;
	TArray<FString> GetSimilarTerms(const FString& Term) const;

	// Configuration
	void SetMinimumTermLength(int32 MinLength);
	void SetCaseSensitive(bool bCaseSensitive);
	void SetComplexityThreshold(float Threshold);

private:
	// Term database
	TArray<FTechnicalTerm> TermDatabase;
	TMap<FString, int32> TermIndexMap; // For fast lookup
	TMap<FString, TArray<int32>> CategoryMap; // Terms by category

	// Configuration
	int32 MinimumTermLength;
	bool bCaseSensitive;
	float ComplexityThreshold;

	// Recognition methods
	bool IsTermInInput(const FString& InputText, const FString& Term) const;
	TArray<FString> ExtractPotentialTerms(const FString& InputText) const;
	float CalculateTermRelevance(const FString& Term, const FString& InputText) const;

	// Database initialization
	void InitializeBlueprintTerms();
	void InitializeDataTypeTerms();
	void InitializeUE5ClassTerms();
	void InitializeComponentTerms();
	void InitializeEventTerms();
	void InitializeFunctionTerms();
	void InitializeNodeTerms();
	void InitializeSystemTerms();

	// Helper methods
	FString NormalizeTermForComparison(const FString& Term) const;
	bool DoesTermMatch(const FString& InputTerm, const FString& DatabaseTerm) const;
	void RebuildIndexMaps();
	TArray<FString> TokenizeInput(const FString& InputText) const;
	bool IsWordBoundary(const FString& Text, int32 Position) const;
}; 