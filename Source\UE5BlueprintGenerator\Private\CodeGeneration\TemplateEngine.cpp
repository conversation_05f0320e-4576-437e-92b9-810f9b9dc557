#include "CodeGeneration/TemplateEngine.h"
#include "Engine/Blueprint.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/Guid.h"
#include "Async/Async.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Misc/CString.h"
#include "Misc/DateTime.h"
#include "GenericPlatform/GenericPlatformFile.h"

DEFINE_LOG_CATEGORY(LogTemplateEngine);

// Singleton instance
TSharedPtr<FTemplateEngine> FTemplateEngine::Instance = nullptr;

FTemplateEngine::FTemplateEngine()
    : bIsProcessingInProgress(false)
{
    UE_LOG(LogTemplateEngine, Log, TEXT("Template Engine initialized"));
    InitializeComponents();
}

FTemplateEngine::~FTemplateEngine()
{
    ShutdownComponents();
}

FTemplateEngine& FTemplateEngine::Get()
{
    if (!Instance.IsValid())
    {
        Instance = MakeShareable(new FTemplateEngine());
    }
    return *Instance;
}

bool FTemplateEngine::ProcessTemplate(const FTemplateProcessingRequest& Request, FTemplateProcessingResult& OutResult)
{
    FScopeLock Lock(&CriticalSection);
    
    UE_LOG(LogTemplateEngine, Log, TEXT("Processing template: %s"), *Request.Template.Name);
    
    // Initialize result
    OutResult = FTemplateProcessingResult();
    OutResult.ProcessingTime = 0.0f;
    
    // Validate request
    TArray<FString> ValidationErrors;
    if (!ValidateProcessingRequest(Request, ValidationErrors))
    {
        OutResult.bSuccess = false;
        OutResult.Errors = ValidationErrors;
        UE_LOG(LogTemplateEngine, Error, TEXT("Template processing request validation failed"));
        BroadcastProcessingError(TEXT("Request validation failed"), Request);
        return false;
    }
    
    // Initialize processing
    if (!InitializeProcessing(Request))
    {
        OutResult.bSuccess = false;
        OutResult.Errors.Add(TEXT("Failed to initialize template processing"));
        BroadcastProcessingError(TEXT("Initialization failed"), Request);
        return false;
    }
    
    // Execute processing
    bool bSuccess = ExecuteProcessing(Request, OutResult);
    
    // Finalize processing
    FinalizeProcessing(OutResult);
    
    // Update statistics
    UpdateStatistics(OutResult);
    
    if (bSuccess)
    {
        UE_LOG(LogTemplateEngine, Log, TEXT("Template processing completed successfully"));
        BroadcastProcessingCompleted(OutResult);
    }
    else
    {
        UE_LOG(LogTemplateEngine, Error, TEXT("Template processing failed"));
        BroadcastProcessingError(TEXT("Processing execution failed"), Request);
    }
    
    return bSuccess;
}

bool FTemplateEngine::IsProcessingInProgress() const
{
    FScopeLock Lock(&CriticalSection);
    return bIsProcessingInProgress;
}

void FTemplateEngine::CancelProcessing()
{
    FScopeLock Lock(&CriticalSection);
    
    if (bIsProcessingInProgress)
    {
        bIsProcessingInProgress = false;
        UE_LOG(LogTemplateEngine, Warning, TEXT("Template processing cancelled"));
    }
}

bool FTemplateEngine::RegisterTemplate(const FTemplate& Template)
{
    FScopeLock Lock(&CriticalSection);
    
    // Validate template
    TArray<FString> ValidationErrors;
    if (!ValidateTemplate(Template, ValidationErrors))
    {
        UE_LOG(LogTemplateEngine, Error, TEXT("Template validation failed: %s"), *Template.Name);
        return false;
    }
    
    // Register template
    RegisteredTemplates.Add(Template.Id, Template);
    
    // Add to cache if caching is enabled
    if (Config.bEnableCaching)
    {
        AddToCache(Template.Id, Template);
    }
    
    // Update statistics
    Statistics.TotalRegisteredTemplates = RegisteredTemplates.Num();
    
    UE_LOG(LogTemplateEngine, Log, TEXT("Template registered: %s (%s)"), *Template.Name, *Template.Id);
    BroadcastTemplateRegistered(Template);
    
    return true;
}

bool FTemplateEngine::UnregisterTemplate(const FString& TemplateId)
{
    FScopeLock Lock(&CriticalSection);
    
    if (!RegisteredTemplates.Contains(TemplateId))
    {
        UE_LOG(LogTemplateEngine, Warning, TEXT("Template not found for unregistration: %s"), *TemplateId);
        return false;
    }
    
    // Remove from registry
    RegisteredTemplates.Remove(TemplateId);
    
    // Remove from cache
    RemoveFromCache(TemplateId);
    
    // Update statistics
    Statistics.TotalRegisteredTemplates = RegisteredTemplates.Num();
    
    UE_LOG(LogTemplateEngine, Log, TEXT("Template unregistered: %s"), *TemplateId);
    BroadcastTemplateUnregistered(TemplateId);
    
    return true;
}

bool FTemplateEngine::UpdateTemplate(const FTemplate& Template)
{
    FScopeLock Lock(&CriticalSection);
    
    if (!RegisteredTemplates.Contains(Template.Id))
    {
        UE_LOG(LogTemplateEngine, Warning, TEXT("Template not found for update: %s"), *Template.Id);
        return false;
    }
    
    // Validate template
    TArray<FString> ValidationErrors;
    if (!ValidateTemplate(Template, ValidationErrors))
    {
        UE_LOG(LogTemplateEngine, Error, TEXT("Template validation failed during update: %s"), *Template.Name);
        return false;
    }
    
    // Update template
    RegisteredTemplates[Template.Id] = Template;
    
    // Update cache
    AddToCache(Template.Id, Template);
    
    UE_LOG(LogTemplateEngine, Log, TEXT("Template updated: %s"), *Template.Name);
    
    return true;
}

TArray<FTemplate> FTemplateEngine::GetRegisteredTemplates() const
{
    FScopeLock Lock(&CriticalSection);
    
    TArray<FTemplate> Templates;
    RegisteredTemplates.GenerateValueArray(Templates);
    return Templates;
}

const FTemplate* FTemplateEngine::FindTemplate(const FString& TemplateId) const
{
    FScopeLock Lock(&CriticalSection);
    
    // Check cache first
    if (Config.bEnableCaching)
    {
        const FTemplate* CachedTemplate = GetFromCache(TemplateId);
        if (CachedTemplate)
        {
            return CachedTemplate;
        }
    }
    
    // Check registry
    return RegisteredTemplates.Find(TemplateId);
}

TArray<FTemplate> FTemplateEngine::FindTemplatesByType(ETemplateType Type) const
{
    FScopeLock Lock(&CriticalSection);
    
    TArray<FTemplate> MatchingTemplates;
    
    for (const auto& TemplatePair : RegisteredTemplates)
    {
        if (TemplatePair.Value.Type == Type)
        {
            MatchingTemplates.Add(TemplatePair.Value);
        }
    }
    
    return MatchingTemplates;
}

TArray<FTemplate> FTemplateEngine::FindTemplatesByTags(const TArray<FString>& Tags) const
{
    FScopeLock Lock(&CriticalSection);
    
    TArray<FTemplate> MatchingTemplates;
    
    for (const auto& TemplatePair : RegisteredTemplates)
    {
        const FTemplate& Template = TemplatePair.Value;
        
        // Check if template has any of the specified tags
        for (const FString& Tag : Tags)
        {
            if (Template.Tags.Contains(Tag))
            {
                MatchingTemplates.Add(Template);
                break;
            }
        }
    }
    
    return MatchingTemplates;
}

bool FTemplateEngine::LoadTemplateFromFile(const FString& FilePath, FTemplate& OutTemplate)
{
    FString FileContent;
    if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        UE_LOG(LogTemplateEngine, Error, TEXT("Failed to load template file: %s"), *FilePath);
        return false;
    }
    
    // Parse JSON content
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
    
    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        UE_LOG(LogTemplateEngine, Error, TEXT("Failed to parse template JSON: %s"), *FilePath);
        return false;
    }
    
    // Extract template data
    OutTemplate.Id = JsonObject->GetStringField(TEXT("Id"));
    OutTemplate.Name = JsonObject->GetStringField(TEXT("Name"));
    OutTemplate.Description = JsonObject->GetStringField(TEXT("Description"));
    OutTemplate.Content = JsonObject->GetStringField(TEXT("Content"));
    OutTemplate.Version = JsonObject->GetStringField(TEXT("Version"));
    OutTemplate.Author = JsonObject->GetStringField(TEXT("Author"));
    OutTemplate.FilePath = FilePath;
    
    // Parse template type
    FString TypeString = JsonObject->GetStringField(TEXT("Type"));
    if (TypeString == TEXT("CppHeader"))
        OutTemplate.Type = ETemplateType::CppHeader;
    else if (TypeString == TEXT("CppSource"))
        OutTemplate.Type = ETemplateType::CppSource;
    else if (TypeString == TEXT("CppClass"))
        OutTemplate.Type = ETemplateType::CppClass;
    else if (TypeString == TEXT("Documentation"))
        OutTemplate.Type = ETemplateType::Documentation;
    else
        OutTemplate.Type = ETemplateType::Custom;
    
    // Parse template format
    FString FormatString = JsonObject->GetStringField(TEXT("Format"));
    if (FormatString == TEXT("Mustache"))
        OutTemplate.Format = ETemplateFormat::Mustache;
    else if (FormatString == TEXT("Jinja"))
        OutTemplate.Format = ETemplateFormat::Jinja;
    else
        OutTemplate.Format = ETemplateFormat::PlainText;
    
    // Parse tags
    const TArray<TSharedPtr<FJsonValue>>* TagsArray;
    if (JsonObject->TryGetArrayField(TEXT("Tags"), TagsArray))
    {
        for (const auto& TagValue : *TagsArray)
        {
            OutTemplate.Tags.Add(TagValue->AsString());
        }
    }
    
    UE_LOG(LogTemplateEngine, Log, TEXT("Template loaded from file: %s"), *OutTemplate.Name);
    return true;
}

bool FTemplateEngine::SaveTemplateToFile(const FTemplate& Template, const FString& FilePath)
{
    // Create JSON object
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    
    JsonObject->SetStringField(TEXT("Id"), Template.Id);
    JsonObject->SetStringField(TEXT("Name"), Template.Name);
    JsonObject->SetStringField(TEXT("Description"), Template.Description);
    JsonObject->SetStringField(TEXT("Content"), Template.Content);
    JsonObject->SetStringField(TEXT("Version"), Template.Version);
    JsonObject->SetStringField(TEXT("Author"), Template.Author);
    
    // Convert template type to string
    FString TypeString;
    switch (Template.Type)
    {
    case ETemplateType::CppHeader: TypeString = TEXT("CppHeader"); break;
    case ETemplateType::CppSource: TypeString = TEXT("CppSource"); break;
    case ETemplateType::CppClass: TypeString = TEXT("CppClass"); break;
    case ETemplateType::Documentation: TypeString = TEXT("Documentation"); break;
    default: TypeString = TEXT("Custom"); break;
    }
    JsonObject->SetStringField(TEXT("Type"), TypeString);
    
    // Convert template format to string
    FString FormatString;
    switch (Template.Format)
    {
    case ETemplateFormat::Mustache: FormatString = TEXT("Mustache"); break;
    case ETemplateFormat::Jinja: FormatString = TEXT("Jinja"); break;
    default: FormatString = TEXT("PlainText"); break;
    }
    JsonObject->SetStringField(TEXT("Format"), FormatString);
    
    // Add tags array
    TArray<TSharedPtr<FJsonValue>> TagsArray;
    for (const FString& Tag : Template.Tags)
    {
        TagsArray.Add(MakeShareable(new FJsonValueString(Tag)));
    }
    JsonObject->SetArrayField(TEXT("Tags"), TagsArray);
    
    // Serialize to string
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    // Save to file
    if (!FFileHelper::SaveStringToFile(OutputString, *FilePath))
    {
        UE_LOG(LogTemplateEngine, Error, TEXT("Failed to save template to file: %s"), *FilePath);
        return false;
    }
    
    UE_LOG(LogTemplateEngine, Log, TEXT("Template saved to file: %s"), *FilePath);
    return true;
}

void FTemplateEngine::RegisterBuiltInTemplates()
{
    UE_LOG(LogTemplateEngine, Log, TEXT("Registering built-in templates"));
    
    // Register C++ header template
    FTemplate CppHeaderTemplate = CreateCppHeaderTemplate();
    RegisterTemplate(CppHeaderTemplate);
    
    // Register C++ source template
    FTemplate CppSourceTemplate = CreateCppSourceTemplate();
    RegisterTemplate(CppSourceTemplate);
    
    // Register C++ class template
    FTemplate CppClassTemplate = CreateCppClassTemplate();
    RegisterTemplate(CppClassTemplate);
    
    // Register C++ function template
    FTemplate CppFunctionTemplate = CreateCppFunctionTemplate();
    RegisterTemplate(CppFunctionTemplate);
    
    // Register C++ variable template
    FTemplate CppVariableTemplate = CreateCppVariableTemplate();
    RegisterTemplate(CppVariableTemplate);
    
    // Register documentation template
    FTemplate DocumentationTemplate = CreateDocumentationTemplate();
    RegisterTemplate(DocumentationTemplate);
    
    // Register project template
    FTemplate ProjectTemplate = CreateProjectTemplate();
    RegisterTemplate(ProjectTemplate);
    
    // Register CMake template
    FTemplate CMakeTemplate = CreateCMakeTemplate();
    RegisterTemplate(CMakeTemplate);
    
    UE_LOG(LogTemplateEngine, Log, TEXT("Built-in templates registered: %d"), 8);
}

FTemplateEngineConfig FTemplateEngine::GetEngineConfig() const
{
    FScopeLock Lock(&CriticalSection);
    return Config;
}

void FTemplateEngine::SetEngineConfig(const FTemplateEngineConfig& NewConfig)
{
    FScopeLock Lock(&CriticalSection);
    Config = NewConfig;
    
    // Update cache size if needed
    if (Config.bEnableCaching && TemplateCache.Num() > Config.MaxCacheSize)
    {
        CleanupCache();
    }
}

FTemplateEngineStatistics FTemplateEngine::GetStatistics() const
{
    FScopeLock Lock(&CriticalSection);
    return Statistics;
}

void FTemplateEngine::ResetStatistics()
{
    FScopeLock Lock(&CriticalSection);
    Statistics = FTemplateEngineStatistics();
    UE_LOG(LogTemplateEngine, Log, TEXT("Template engine statistics reset"));
}

bool FTemplateEngine::ValidateTemplate(const FTemplate& Template, TArray<FString>& OutErrors) const
{
    OutErrors.Empty();
    
    // Validate ID
    if (Template.Id.IsEmpty())
    {
        OutErrors.Add(TEXT("Template ID cannot be empty"));
    }
    
    // Validate name
    if (Template.Name.IsEmpty())
    {
        OutErrors.Add(TEXT("Template name cannot be empty"));
    }
    
    // Validate content
    if (Template.Content.IsEmpty())
    {
        OutErrors.Add(TEXT("Template content cannot be empty"));
    }
    
    return OutErrors.Num() == 0;
}

bool FTemplateEngine::ValidateProcessingRequest(const FTemplateProcessingRequest& Request, TArray<FString>& OutErrors) const
{
    OutErrors.Empty();
    
    // Validate template
    if (!ValidateTemplate(Request.Template, OutErrors))
    {
        return false;
    }
    
    // Validate required variables
    for (const FTemplateVariable& RequiredVar : Request.Template.RequiredVariables)
    {
        if (!Request.FindVariable(RequiredVar.Name))
        {
            OutErrors.Add(FString::Printf(TEXT("Required variable '%s' is missing"), *RequiredVar.Name));
        }
    }
    
    return OutErrors.Num() == 0;
}

void FTemplateEngine::ClearCache()
{
    FScopeLock Lock(&CriticalSection);
    TemplateCache.Empty();
    Statistics.CachedTemplates = 0;
    UE_LOG(LogTemplateEngine, Log, TEXT("Template cache cleared"));
}

int32 FTemplateEngine::GetCacheSize() const
{
    FScopeLock Lock(&CriticalSection);
    return TemplateCache.Num();
}

void FTemplateEngine::SetCacheSize(int32 NewSize)
{
    FScopeLock Lock(&CriticalSection);
    Config.MaxCacheSize = FMath::Max(0, NewSize);
    
    if (TemplateCache.Num() > Config.MaxCacheSize)
    {
        CleanupCache();
    }
}

bool FTemplateEngine::InitializeProcessing(const FTemplateProcessingRequest& Request)
{
    bIsProcessingInProgress = true;
    ProcessingStartTime = FDateTime::Now();
    CurrentRequest = Request;
    
    BroadcastProcessingStarted(Request);
    return true;
}

bool FTemplateEngine::ExecuteProcessing(const FTemplateProcessingRequest& Request, FTemplateProcessingResult& OutResult)
{
    FDateTime StartTime = FDateTime::Now();
    
    BroadcastProcessingProgress(0.1f, TEXT("Starting template processing"));
    
    // Process template based on format
    FString ProcessedContent;
    bool bSuccess = false;
    
    switch (Request.Template.Format)
    {
    case ETemplateFormat::Mustache:
        bSuccess = ProcessMustacheTemplate(Request.Template, Request.Variables, ProcessedContent);
        break;
    case ETemplateFormat::Jinja:
        bSuccess = ProcessJinjaTemplate(Request.Template, Request.Variables, ProcessedContent);
        break;
    case ETemplateFormat::PlainText:
        bSuccess = ProcessPlainTextTemplate(Request.Template, Request.Variables, ProcessedContent);
        break;
    default:
        OutResult.Errors.Add(TEXT("Unsupported template format"));
        return false;
    }
    
    if (!bSuccess)
    {
        OutResult.bSuccess = false;
        return false;
    }
    
    BroadcastProcessingProgress(0.9f, TEXT("Finalizing processed content"));
    
    // Set result
    OutResult.bSuccess = true;
    OutResult.ProcessedContent = ProcessedContent;
    OutResult.ProcessingTime = (FDateTime::Now() - StartTime).GetTotalSeconds();
    
    BroadcastProcessingProgress(1.0f, TEXT("Processing completed"));
    
    return true;
}

bool FTemplateEngine::FinalizeProcessing(const FTemplateProcessingResult& Result)
{
    bIsProcessingInProgress = false;
    return Result.bSuccess;
}

bool FTemplateEngine::ProcessMustacheTemplate(const FTemplate& Template, const TArray<FTemplateVariable>& Variables, FString& OutContent)
{
    OutContent = Template.Content;
    
    // Simple variable substitution for Mustache format
    for (const FTemplateVariable& Variable : Variables)
    {
        FString VariablePlaceholder = FString::Printf(TEXT("{{%s}}"), *Variable.Name);
        FString VariableValue = Variable.GetValueAsString();
        OutContent = OutContent.Replace(*VariablePlaceholder, *VariableValue);
    }
    
    return true;
}

bool FTemplateEngine::ProcessJinjaTemplate(const FTemplate& Template, const TArray<FTemplateVariable>& Variables, FString& OutContent)
{
    OutContent = Template.Content;
    
    // Simple variable substitution for Jinja format
    for (const FTemplateVariable& Variable : Variables)
    {
        FString VariablePlaceholder = FString::Printf(TEXT("{{ %s }}"), *Variable.Name);
        FString VariableValue = Variable.GetValueAsString();
        OutContent = OutContent.Replace(*VariablePlaceholder, *VariableValue);
    }
    
    return true;
}

bool FTemplateEngine::ProcessPlainTextTemplate(const FTemplate& Template, const TArray<FTemplateVariable>& Variables, FString& OutContent)
{
    OutContent = Template.Content;
    
    // Simple variable substitution for plain text
    for (const FTemplateVariable& Variable : Variables)
    {
        FString VariablePlaceholder = FString::Printf(TEXT("{%s}"), *Variable.Name);
        FString VariableValue = Variable.GetValueAsString();
        OutContent = OutContent.Replace(*VariablePlaceholder, *VariableValue);
    }
    
    return true;
}

void FTemplateEngine::AddToCache(const FString& TemplateId, const FTemplate& Template)
{
    if (!Config.bEnableCaching)
        return;
    
    FCachedTemplate CachedTemplate;
    CachedTemplate.Template = Template;
    CachedTemplate.CacheTime = FDateTime::Now();
    CachedTemplate.AccessCount = 1;
    
    TemplateCache.Add(TemplateId, CachedTemplate);
    Statistics.CachedTemplates = TemplateCache.Num();
    
    // Clean up cache if it exceeds maximum size
    if (TemplateCache.Num() > Config.MaxCacheSize)
    {
        CleanupCache();
    }
}

const FTemplate* FTemplateEngine::GetFromCache(const FString& TemplateId)
{
    FCachedTemplate* CachedTemplate = TemplateCache.Find(TemplateId);
    if (!CachedTemplate)
        return nullptr;
    
    // Check if cache entry has expired
    FTimespan CacheAge = FDateTime::Now() - CachedTemplate->CacheTime;
    if (CacheAge.GetTotalSeconds() > Config.CacheExpirationTime)
    {
        TemplateCache.Remove(TemplateId);
        Statistics.CachedTemplates = TemplateCache.Num();
        return nullptr;
    }
    
    // Update access count
    CachedTemplate->AccessCount++;
    return &CachedTemplate->Template;
}

void FTemplateEngine::RemoveFromCache(const FString& TemplateId)
{
    TemplateCache.Remove(TemplateId);
    Statistics.CachedTemplates = TemplateCache.Num();
}

void FTemplateEngine::CleanupCache()
{
    if (TemplateCache.Num() <= Config.MaxCacheSize)
        return;
    
    // Remove oldest entries
    TArray<TPair<FString, FCachedTemplate>> CacheArray;
    for (const auto& CachePair : TemplateCache)
    {
        CacheArray.Add(CachePair);
    }
    
    // Sort by cache time (oldest first)
    CacheArray.Sort([](const TPair<FString, FCachedTemplate>& A, const TPair<FString, FCachedTemplate>& B)
    {
        return A.Value.CacheTime < B.Value.CacheTime;
    });
    
    // Remove oldest entries
    int32 EntriesToRemove = TemplateCache.Num() - Config.MaxCacheSize;
    for (int32 i = 0; i < EntriesToRemove && i < CacheArray.Num(); ++i)
    {
        TemplateCache.Remove(CacheArray[i].Key);
    }
    
    Statistics.CachedTemplates = TemplateCache.Num();
}

FTemplate FTemplateEngine::CreateCppHeaderTemplate() const
{
    FTemplate Template;
    Template.Id = FGuid::NewGuid().ToString();
    Template.Name = TEXT("C++ Header Template");
    Template.Description = TEXT("Template for generating C++ header files from blueprints");
    Template.Type = ETemplateType::CppHeader;
    Template.Format = ETemplateFormat::Mustache;
    Template.bIsBuiltIn = true;
    Template.Version = TEXT("1.0");
    Template.Author = TEXT("UE5 Blueprint Generator");
    Template.Tags.Add(TEXT("cpp"));
    Template.Tags.Add(TEXT("header"));
    Template.Tags.Add(TEXT("builtin"));
    
    Template.Content = TEXT(
        "#pragma once\n\n"
        "#include \"CoreMinimal.h\"\n"
        "#include \"{{BaseInclude}}\"\n"
        "#include \"{{ClassName}}.generated.h\"\n\n"
        "/**\n"
        " * {{Description}}\n"
        " */\n"
        "UCLASS({{ClassSpecifiers}})\n"
        "class {{ModuleName}}_API {{ClassName}} : public {{BaseClass}}\n"
        "{\n"
        "\tGENERATED_BODY()\n\n"
        "public:\n"
        "\t{{ClassName}}();\n\n"
        "{{PublicFunctions}}\n\n"
        "{{PublicVariables}}\n\n"
        "protected:\n"
        "{{ProtectedFunctions}}\n\n"
        "{{ProtectedVariables}}\n\n"
        "private:\n"
        "{{PrivateFunctions}}\n\n"
        "{{PrivateVariables}}\n"
        "};\n"
    );
    
    return Template;
}

FTemplate FTemplateEngine::CreateCppSourceTemplate() const
{
    FTemplate Template;
    Template.Id = FGuid::NewGuid().ToString();
    Template.Name = TEXT("C++ Source Template");
    Template.Description = TEXT("Template for generating C++ source files from blueprints");
    Template.Type = ETemplateType::CppSource;
    Template.Format = ETemplateFormat::Mustache;
    Template.bIsBuiltIn = true;
    Template.Version = TEXT("1.0");
    Template.Author = TEXT("UE5 Blueprint Generator");
    Template.Tags.Add(TEXT("cpp"));
    Template.Tags.Add(TEXT("source"));
    Template.Tags.Add(TEXT("builtin"));
    
    Template.Content = TEXT(
        "#include \"{{ClassName}}.h\"\n\n"
        "{{ClassName}}::{{ClassName}}\n"
        "{\n"
        "\t// Set default values\n"
        "{{DefaultValues}}\n"
        "}\n\n"
        "{{FunctionImplementations}}\n"
    );
    
    return Template;
}

FTemplate FTemplateEngine::CreateDocumentationTemplate() const
{
    FTemplate Template;
    Template.Id = FGuid::NewGuid().ToString();
    Template.Name = TEXT("Documentation Template");
    Template.Description = TEXT("Template for generating documentation from blueprints");
    Template.Type = ETemplateType::Documentation;
    Template.Format = ETemplateFormat::Mustache;
    Template.bIsBuiltIn = true;
    Template.Version = TEXT("1.0");
    Template.Author = TEXT("UE5 Blueprint Generator");
    Template.Tags.Add(TEXT("documentation"));
    Template.Tags.Add(TEXT("markdown"));
    Template.Tags.Add(TEXT("builtin"));
    
    Template.Content = TEXT(
        "# {{ClassName}}\n\n"
        "## Description\n"
        "{{Description}}\n\n"
        "## Variables\n"
        "{{VariableDocumentation}}\n\n"
        "## Functions\n"
        "{{FunctionDocumentation}}\n\n"
        "## Events\n"
        "{{EventDocumentation}}\n"
    );
    
    return Template;
}

void FTemplateEngine::UpdateStatistics(const FTemplateProcessingResult& Result)
{
    Statistics.TotalTemplatesProcessed++;
    
    if (Result.bSuccess)
    {
        Statistics.SuccessfulProcessing++;
    }
    else
    {
        Statistics.FailedProcessing++;
    }
    
    // Update timing statistics
    if (Statistics.TotalTemplatesProcessed == 1)
    {
        Statistics.FastestProcessingTime = Result.ProcessingTime;
        Statistics.SlowestProcessingTime = Result.ProcessingTime;
        Statistics.AverageProcessingTime = Result.ProcessingTime;
    }
    else
    {
        Statistics.FastestProcessingTime = FMath::Min(Statistics.FastestProcessingTime, Result.ProcessingTime);
        Statistics.SlowestProcessingTime = FMath::Max(Statistics.SlowestProcessingTime, Result.ProcessingTime);
        
        // Recalculate average
        float TotalTime = Statistics.AverageProcessingTime * (Statistics.TotalTemplatesProcessed - 1) + Result.ProcessingTime;
        Statistics.AverageProcessingTime = TotalTime / Statistics.TotalTemplatesProcessed;
    }
}

void FTemplateEngine::BroadcastProcessingStarted(const FTemplateProcessingRequest& Request)
{
    if (OnTemplateProcessingStarted.IsBound())
    {
        OnTemplateProcessingStarted.Broadcast(Request);
    }
}

void FTemplateEngine::BroadcastProcessingProgress(float Progress, const FString& CurrentTask)
{
    if (OnTemplateProcessingProgress.IsBound())
    {
        OnTemplateProcessingProgress.Broadcast(Progress, CurrentTask);
    }
}

void FTemplateEngine::BroadcastProcessingCompleted(const FTemplateProcessingResult& Result)
{
    if (OnTemplateProcessingCompleted.IsBound())
    {
        OnTemplateProcessingCompleted.Broadcast(Result);
    }
}

void FTemplateEngine::BroadcastProcessingError(const FString& ErrorMessage, const FTemplateProcessingRequest& Request)
{
    if (OnTemplateProcessingError.IsBound())
    {
        OnTemplateProcessingError.Broadcast(ErrorMessage, Request);
    }
}

void FTemplateEngine::BroadcastTemplateRegistered(const FTemplate& Template)
{
    if (OnTemplateRegistered.IsBound())
    {
        OnTemplateRegistered.Broadcast(Template);
    }
}

void FTemplateEngine::BroadcastTemplateUnregistered(const FString& TemplateId)
{
    if (OnTemplateUnregistered.IsBound())
    {
        OnTemplateUnregistered.Broadcast(TemplateId);
    }
}

void FTemplateEngine::InitializeComponents()
{
    // Set default configuration
    Config = FTemplateEngineConfig();
    
    // Initialize statistics
    Statistics = FTemplateEngineStatistics();
    
    // Register built-in templates
    RegisterBuiltInTemplates();
}

void FTemplateEngine::ShutdownComponents()
{
    ClearCache();
    RegisteredTemplates.Empty();
}

// FTemplateVariable implementation
FString FTemplateVariable::GetValueAsString() const
{
    switch (Type)
    {
    case ETemplateVariableType::String:
        return StringValue;
    case ETemplateVariableType::Integer:
        return FString::Printf(TEXT("%d"), IntegerValue);
    case ETemplateVariableType::Float:
        return FString::Printf(TEXT("%.2f"), FloatValue);
    case ETemplateVariableType::Boolean:
        return BooleanValue ? TEXT("true") : TEXT("false");
    case ETemplateVariableType::Array:
        return FString::Join(ArrayValues, TEXT(", "));
    default:
        return StringValue;
    }
}

void FTemplateVariable::SetValueFromString(const FString& Value)
{
    switch (Type)
    {
    case ETemplateVariableType::Integer:
        IntegerValue = FCString::Atoi(*Value);
        break;
    case ETemplateVariableType::Float:
        FloatValue = FCString::Atof(*Value);
        break;
    case ETemplateVariableType::Boolean:
        BooleanValue = Value.ToBool();
        break;
    default:
        StringValue = Value;
        break;
    }
}

bool FTemplateVariable::IsValid() const
{
    return !Name.IsEmpty();
}

// FTemplate implementation
TArray<FTemplateVariable> FTemplate::GetAllVariables() const
{
    TArray<FTemplateVariable> AllVariables = RequiredVariables;
    AllVariables.Append(OptionalVariables);
    return AllVariables;
}

const FTemplateVariable* FTemplate::FindVariable(const FString& VariableName) const
{
    for (const FTemplateVariable& Variable : RequiredVariables)
    {
        if (Variable.Name == VariableName)
        {
            return &Variable;
        }
    }
    
    for (const FTemplateVariable& Variable : OptionalVariables)
    {
        if (Variable.Name == VariableName)
        {
            return &Variable;
        }
    }
    
    return nullptr;
}

bool FTemplate::IsValid() const
{
    return !Id.IsEmpty() && !Name.IsEmpty() && !Content.IsEmpty();
}

FString FTemplate::GetTemplateHash() const
{
    FString CombinedString = Id + Name + Content + Version;
    uint32 Hash = GetTypeHash(CombinedString);
    return FString::Printf(TEXT("%08X"), Hash);
}

// FTemplateProcessingRequest implementation
const FTemplateVariable* FTemplateProcessingRequest::FindVariable(const FString& VariableName) const
{
    for (const FTemplateVariable& Variable : Variables)
    {
        if (Variable.Name == VariableName)
        {
            return &Variable;
        }
    }
    return nullptr;
}

void FTemplateProcessingRequest::SetVariable(const FTemplateVariable& Variable)
{
    // Remove existing variable with same name
    Variables.RemoveAll([&Variable](const FTemplateVariable& Var)
    {
        return Var.Name == Variable.Name;
    });
    
    // Add new variable
    Variables.Add(Variable);
}

bool FTemplateProcessingRequest::IsValid() const
{
    return Template.IsValid() && !RequestId.IsEmpty();
} 