#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "NLP/StructureExtractor.h"
#include "NLP/IntentAnalyzer.h"
#include "NLP/BlueprintPatternDetector.h"

DECLARE_LOG_CATEGORY_EXTERN(LogComplexityEstimator, Log, All);

/**
 * Complexity level enumeration for blueprint structures
 */
UENUM()
enum class EComplexityLevel : uint8
{
	/** Very simple blueprint with minimal elements */
	Trivial = 0,
	
	/** Simple blueprint with basic functionality */
	Simple = 1,
	
	/** Moderate complexity with multiple systems */
	Moderate = 2,
	
	/** Complex blueprint with advanced features */
	Complex = 3,
	
	/** Very complex blueprint requiring expert knowledge */
	VeryComplex = 4,
	
	/** Extremely complex, may exceed practical limits */
	ExtremelyComplex = 5
};

/**
 * Performance impact assessment for different aspects
 */
UENUM()
enum class EPerformanceImpact : uint8
{
	/** Minimal performance impact */
	Negligible = 0,
	
	/** Low performance impact */
	Low = 1,
	
	/** Moderate performance impact */
	Moderate = 2,
	
	/** High performance impact */
	High = 3,
	
	/** Very high performance impact */
	VeryHigh = 4,
	
	/** Critical performance impact */
	Critical = 5
};

/**
 * Resource requirement estimation
 */
USTRUCT()
struct FResourceRequirements
{
	GENERATED_BODY()

	/** Estimated memory usage in MB */
	UPROPERTY()
	float EstimatedMemoryMB = 0.0f;

	/** Estimated CPU usage percentage (0-100) */
	UPROPERTY()
	float EstimatedCPUUsage = 0.0f;

	/** Estimated GPU usage percentage (0-100) */
	UPROPERTY()
	float EstimatedGPUUsage = 0.0f;

	/** Estimated network bandwidth in KB/s */
	UPROPERTY()
	float EstimatedNetworkKBps = 0.0f;

	/** Estimated disk I/O in MB/s */
	UPROPERTY()
	float EstimatedDiskIOps = 0.0f;

	/** Number of blueprint nodes estimated */
	UPROPERTY()
	int32 EstimatedNodeCount = 0;

	/** Number of blueprint connections estimated */
	UPROPERTY()
	int32 EstimatedConnectionCount = 0;

	/** Estimated compilation time in seconds */
	UPROPERTY()
	float EstimatedCompileTime = 0.0f;

	FResourceRequirements()
	{
		EstimatedMemoryMB = 1.0f;
		EstimatedCPUUsage = 5.0f;
		EstimatedGPUUsage = 0.0f;
		EstimatedNetworkKBps = 0.0f;
		EstimatedDiskIOps = 0.0f;
		EstimatedNodeCount = 5;
		EstimatedConnectionCount = 10;
		EstimatedCompileTime = 1.0f;
	}
};

/**
 * Performance impact analysis for different systems
 */
USTRUCT()
struct FPerformanceAnalysis
{
	GENERATED_BODY()

	/** Overall performance impact */
	UPROPERTY()
	EPerformanceImpact OverallImpact = EPerformanceImpact::Low;

	/** Runtime performance impact */
	UPROPERTY()
	EPerformanceImpact RuntimeImpact = EPerformanceImpact::Low;

	/** Memory performance impact */
	UPROPERTY()
	EPerformanceImpact MemoryImpact = EPerformanceImpact::Low;

	/** Network performance impact */
	UPROPERTY()
	EPerformanceImpact NetworkImpact = EPerformanceImpact::Negligible;

	/** Rendering performance impact */
	UPROPERTY()
	EPerformanceImpact RenderingImpact = EPerformanceImpact::Negligible;

	/** Blueprint compilation impact */
	UPROPERTY()
	EPerformanceImpact CompilationImpact = EPerformanceImpact::Low;

	/** Detailed performance notes */
	UPROPERTY()
	TArray<FString> PerformanceNotes;

	/** Optimization suggestions */
	UPROPERTY()
	TArray<FString> OptimizationSuggestions;
};

/**
 * User capability assessment and recommendations
 */
USTRUCT()
struct FUserCapabilityAssessment
{
	GENERATED_BODY()

	/** Required skill level (1-5, where 1=Beginner, 5=Expert) */
	UPROPERTY()
	int32 RequiredSkillLevel = 1;

	/** Estimated development time in hours */
	UPROPERTY()
	float EstimatedDevelopmentHours = 1.0f;

	/** Required UE5 knowledge areas */
	UPROPERTY()
	TArray<FString> RequiredKnowledgeAreas;

	/** Recommended learning resources */
	UPROPERTY()
	TArray<FString> RecommendedResources;

	/** Prerequisites that should be completed first */
	UPROPERTY()
	TArray<FString> Prerequisites;

	/** Difficulty warnings */
	UPROPERTY()
	TArray<FString> DifficultyWarnings;

	/** Alternative simpler approaches */
	UPROPERTY()
	TArray<FString> SimplerAlternatives;
};

/**
 * Complete complexity analysis result
 */
USTRUCT()
struct FComplexityAnalysisResult
{
	GENERATED_BODY()

	/** Overall complexity level */
	UPROPERTY()
	EComplexityLevel ComplexityLevel = EComplexityLevel::Simple;

	/** Numerical complexity score (0.0-10.0) */
	UPROPERTY()
	float ComplexityScore = 1.0f;

	/** Confidence in the complexity assessment (0.0-1.0) */
	UPROPERTY()
	float AssessmentConfidence = 0.8f;

	/** Resource requirements estimation */
	UPROPERTY()
	FResourceRequirements ResourceRequirements;

	/** Performance impact analysis */
	UPROPERTY()
	FPerformanceAnalysis PerformanceAnalysis;

	/** User capability assessment */
	UPROPERTY()
	FUserCapabilityAssessment UserCapabilityAssessment;

	/** Complexity factors that contributed to the score */
	UPROPERTY()
	TMap<FString, float> ComplexityFactors;

	/** Detailed analysis notes */
	UPROPERTY()
	TArray<FString> AnalysisNotes;

	/** Recommendations for managing complexity */
	UPROPERTY()
	TArray<FString> ComplexityRecommendations;

	/** Whether the blueprint is feasible to implement */
	UPROPERTY()
	bool bIsFeasible = true;

	/** Reasons why it might not be feasible */
	UPROPERTY()
	TArray<FString> FeasibilityIssues;
};

/**
 * Complexity Estimator - Analyzes blueprint structure complexity and provides comprehensive assessment
 * 
 * This component evaluates the complexity of extracted blueprint structures, estimates resource
 * requirements, assesses performance impact, and provides user capability recommendations.
 */
class UEBLUEPRINT_GENERATOR_API FComplexityEstimator
{
public:
	FComplexityEstimator();
	~FComplexityEstimator();

	// Main analysis methods
	
	/**
	 * Perform complete complexity analysis on extracted blueprint structure
	 * @param Structure The extracted blueprint structure to analyze
	 * @param IntentResult Intent analysis result for context
	 * @param PatternType Detected blueprint pattern type
	 * @return Complete complexity analysis result
	 */
	FComplexityAnalysisResult AnalyzeComplexity(const FExtractedBlueprintStructure& Structure,
		const FIntentAnalysisResult& IntentResult,
		const EBlueprintPatternType PatternType);

	// Individual analysis methods
	
	/**
	 * Calculate overall complexity score based on multiple factors
	 * @param Structure The blueprint structure to analyze
	 * @return Complexity score (0.0-10.0)
	 */
	float CalculateComplexityScore(const FExtractedBlueprintStructure& Structure);

	/**
	 * Determine complexity level from numerical score
	 * @param ComplexityScore The numerical complexity score
	 * @return Complexity level enumeration
	 */
	EComplexityLevel DetermineComplexityLevel(float ComplexityScore);

	/**
	 * Estimate resource requirements for the blueprint
	 * @param Structure The blueprint structure to analyze
	 * @param PatternType The blueprint pattern type
	 * @return Resource requirements estimation
	 */
	FResourceRequirements EstimateResourceRequirements(const FExtractedBlueprintStructure& Structure,
		const EBlueprintPatternType PatternType);

	/**
	 * Analyze performance impact of the blueprint
	 * @param Structure The blueprint structure to analyze
	 * @param ResourceReqs Resource requirements for context
	 * @return Performance impact analysis
	 */
	FPerformanceAnalysis AnalyzePerformanceImpact(const FExtractedBlueprintStructure& Structure,
		const FResourceRequirements& ResourceReqs);

	/**
	 * Assess user capability requirements and provide recommendations
	 * @param ComplexityLevel The determined complexity level
	 * @param Structure The blueprint structure for context
	 * @return User capability assessment
	 */
	FUserCapabilityAssessment AssessUserCapability(EComplexityLevel ComplexityLevel,
		const FExtractedBlueprintStructure& Structure);

	// Configuration and customization
	
	/**
	 * Set complexity weight for different blueprint elements
	 * @param ElementType Type of element (Variable, Function, Event, Component)
	 * @param Weight Weight factor for complexity calculation
	 */
	void SetElementComplexityWeight(const FString& ElementType, float Weight);

	/**
	 * Set performance impact weight for different systems
	 * @param SystemType Type of system (Runtime, Memory, Network, etc.)
	 * @param Weight Weight factor for performance calculation
	 */
	void SetPerformanceWeight(const FString& SystemType, float Weight);

	/**
	 * Add custom complexity factor
	 * @param FactorName Name of the complexity factor
	 * @param CalculationFunction Function to calculate the factor value
	 */
	void AddCustomComplexityFactor(const FString& FactorName, 
		TFunction<float(const FExtractedBlueprintStructure&)> CalculationFunction);

	/**
	 * Get complexity analysis statistics
	 * @return Map of analysis statistics
	 */
	TMap<FString, float> GetAnalysisStatistics() const;

private:
	// Configuration
	TMap<FString, float> ElementComplexityWeights;
	TMap<FString, float> PerformanceWeights;
	TMap<FString, TFunction<float(const FExtractedBlueprintStructure&)>> CustomComplexityFactors;
	
	// Complexity thresholds
	float TrivialThreshold;
	float SimpleThreshold;
	float ModerateThreshold;
	float ComplexThreshold;
	float VeryComplexThreshold;
	
	// Performance thresholds
	TMap<FString, TArray<float>> PerformanceThresholds;
	
	// Statistics
	mutable TMap<FString, float> AnalysisStats;
	
	// Initialization methods
	void InitializeComplexityWeights();
	void InitializePerformanceWeights();
	void InitializeComplexityThresholds();
	void InitializePerformanceThresholds();
	
	// Complexity calculation helpers
	float CalculateVariableComplexity(const TArray<FExtractedVariable>& Variables);
	float CalculateFunctionComplexity(const TArray<FExtractedFunction>& Functions);
	float CalculateEventComplexity(const TArray<FExtractedEvent>& Events);
	float CalculateComponentComplexity(const TArray<FExtractedComponent>& Components);
	float CalculateRelationshipComplexity(const TArray<FElementRelationship>& Relationships);
	float CalculatePatternComplexity(EBlueprintPatternType PatternType);
	
	// Resource estimation helpers
	float EstimateMemoryUsage(const FExtractedBlueprintStructure& Structure);
	float EstimateCPUUsage(const FExtractedBlueprintStructure& Structure, EBlueprintPatternType PatternType);
	float EstimateGPUUsage(const FExtractedBlueprintStructure& Structure);
	float EstimateNetworkUsage(const FExtractedBlueprintStructure& Structure);
	int32 EstimateNodeCount(const FExtractedBlueprintStructure& Structure);
	int32 EstimateConnectionCount(const FExtractedBlueprintStructure& Structure);
	float EstimateCompileTime(const FExtractedBlueprintStructure& Structure);
	
	// Performance analysis helpers
	EPerformanceImpact CalculateRuntimeImpact(const FExtractedBlueprintStructure& Structure);
	EPerformanceImpact CalculateMemoryImpact(const FResourceRequirements& ResourceReqs);
	EPerformanceImpact CalculateNetworkImpact(const FExtractedBlueprintStructure& Structure);
	EPerformanceImpact CalculateRenderingImpact(const FExtractedBlueprintStructure& Structure);
	EPerformanceImpact CalculateCompilationImpact(const FResourceRequirements& ResourceReqs);
	TArray<FString> GeneratePerformanceNotes(const FExtractedBlueprintStructure& Structure);
	TArray<FString> GenerateOptimizationSuggestions(const FPerformanceAnalysis& Analysis);
	
	// User capability helpers
	int32 DetermineRequiredSkillLevel(EComplexityLevel ComplexityLevel, const FExtractedBlueprintStructure& Structure);
	float EstimateDevelopmentTime(EComplexityLevel ComplexityLevel, const FExtractedBlueprintStructure& Structure);
	TArray<FString> IdentifyRequiredKnowledgeAreas(const FExtractedBlueprintStructure& Structure);
	TArray<FString> GenerateRecommendedResources(const TArray<FString>& KnowledgeAreas);
	TArray<FString> IdentifyPrerequisites(EComplexityLevel ComplexityLevel);
	TArray<FString> GenerateDifficultyWarnings(const FExtractedBlueprintStructure& Structure);
	TArray<FString> SuggestSimplerAlternatives(const FExtractedBlueprintStructure& Structure);
	
	// Analysis helpers
	TMap<FString, float> CalculateComplexityFactors(const FExtractedBlueprintStructure& Structure);
	TArray<FString> GenerateAnalysisNotes(const FComplexityAnalysisResult& Result);
	TArray<FString> GenerateComplexityRecommendations(const FComplexityAnalysisResult& Result);
	bool AssessFeasibility(const FComplexityAnalysisResult& Result);
	TArray<FString> IdentifyFeasibilityIssues(const FComplexityAnalysisResult& Result);
	
	// Utility methods
	EPerformanceImpact ScoreToPerformanceImpact(float Score, const TArray<float>& Thresholds);
	float NormalizeComplexityScore(float RawScore);
	void UpdateAnalysisStatistics(const FComplexityAnalysisResult& Result) const;
	
	// Validation methods
	bool ValidateAnalysisResult(const FComplexityAnalysisResult& Result) const;
	TArray<FString> GetAnalysisWarnings(const FComplexityAnalysisResult& Result) const;
}; 