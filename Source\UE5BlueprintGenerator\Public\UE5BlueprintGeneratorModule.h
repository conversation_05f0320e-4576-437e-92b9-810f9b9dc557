#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Framework/Docking/TabManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogBlueprintGenerator, Log, All);

/**
 * The main module for the UE5 Blueprint Generator plugin.
 * This module handles initialization, UI registration, and coordination between components.
 */
class FUE5BlueprintGeneratorModule : public IModuleInterface
{
public:

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

	/**
	 * Singleton-like access to this module's interface. This is just for convenience!
	 * Beware of calling this during the shutdown phase, though. Your module might have been unloaded already.
	 *
	 * @return Returns singleton instance, loading the module on demand if needed
	 */
	static inline FUE5BlueprintGeneratorModule& Get()
	{
		return FModuleManager::LoadModuleChecked<FUE5BlueprintGeneratorModule>("UE5BlueprintGenerator");
	}

	/**
	 * Checks to see if this module is loaded and ready. It is only valid to call Get() if IsAvailable() returns true.
	 *
	 * @return True if the module is loaded and ready to use
	 */
	static inline bool IsAvailable()
	{
		return FModuleManager::Get().IsModuleLoaded("UE5BlueprintGenerator");
	}

	/** Opens the Blueprint Generator tab */
	void OpenBlueprintGeneratorTab();

private:
	/** Registers the Blueprint Generator tab spawner */
	void RegisterTabSpawners();

	/** Unregisters the Blueprint Generator tab spawner */
	void UnregisterTabSpawners();

	/** Creates the Blueprint Generator tab */
	TSharedRef<SDockTab> OnSpawnBlueprintGeneratorTab(const FSpawnTabArgs& Args);

	/** Registers menu extensions */
	void RegisterMenuExtensions();

	/** Unregisters menu extensions */
	void UnregisterMenuExtensions();

	/** Creates the Blueprint Generator menu entry */
	void CreateBlueprintGeneratorMenuEntry(FMenuBuilder& MenuBuilder);

private:
	/** Tab identifier for the Blueprint Generator tab */
	static const FName BlueprintGeneratorTabName;

	/** Shared pointer to the tab manager */
	TSharedPtr<FTabManager> TabManager;
}; 