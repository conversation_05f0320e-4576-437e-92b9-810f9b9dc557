#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

/**
 * Handles text input processing and validation for the Blueprint Generator
 * This is the first component in the natural language processing pipeline
 */
class UE5BLUEPRINTGENERATOR_API FTextInputHandler
{
public:
	FTextInputHandler();
	~FTextInputHandler();

	// Input handling
	void SetInputText(const FString& InputText);
	FString GetInputText() const;
	void ClearInput();

	// Input validation
	bool IsInputValid() const;
	FString GetValidationMessage() const;
	int32 GetInputLength() const;
	bool HasMinimumLength() const;

	// Input processing
	FString GetProcessedInput() const;
	FString GetSanitizedInput() const;

	// Input analysis
	bool ContainsTechnicalTerms() const;
	bool HasComplexStructure() const;
	float EstimateComplexity() const;

	// Configuration
	void SetMinimumLength(int32 MinLength);
	void SetMaximumLength(int32 MaxLength);
	int32 GetMinimumLength() const;
	int32 GetMaximumLength() const;

private:
	// Input data
	FString RawInputText;
	FString ProcessedInputText;
	FString SanitizedInputText;
	
	// Validation state
	bool bIsInputValid;
	FString ValidationMessage;
	
	// Configuration
	int32 MinimumLength;
	int32 MaximumLength;
	
	// Processing methods
	void ProcessInput();
	void ValidateInput();
	void SanitizeInput();
	
	// Validation helpers
	bool CheckLength() const;
	bool CheckContent() const;
	bool CheckCharacters() const;
	
	// Sanitization helpers
	FString RemoveSpecialCharacters(const FString& Input) const;
	FString NormalizeWhitespace(const FString& Input) const;
	FString TruncateIfTooLong(const FString& Input) const;
	
	// Analysis helpers
	TArray<FString> ExtractWords(const FString& Input) const;
	TArray<FString> ExtractSentences(const FString& Input) const;
	bool ContainsUE5Keywords(const FString& Input) const;
}; 