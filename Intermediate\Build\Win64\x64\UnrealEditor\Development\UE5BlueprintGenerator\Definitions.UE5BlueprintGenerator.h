// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for UE5BlueprintGenerator
#pragma once
#include "H:/GameDev/SurvivalLS/Intermediate/Build/Win64/x64/SurvivalLSEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.ValApi.Cpp20.h"
#undef UE5BLUEPRINTGENERATOR_API
#define UE_IS_ENGINE_MODULE 0
#define DEPRECATED_FORGAME DEPRECATED
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_PROJECT_NAME SurvivalLS
#define UE_TARGET_NAME SurvivalLSEditor
#define UE_MODULE_NAME "UE5BlueprintGenerator"
#define UE_PLUGIN_NAME "UE5BlueprintGenerator"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define EDITORWIDGETS_API DLLIMPORT
#define WORKSPACEMENUSTRUCTURE_API DLLIMPORT
#define EDITORSCRIPTINGUTILITIES_API DLLIMPORT
#define STATICMESHEDITOR_API DLLIMPORT
#define UE5BLUEPRINTGENERATOR_API DLLEXPORT
#define EDITORSTYLE_API DLLIMPORT
