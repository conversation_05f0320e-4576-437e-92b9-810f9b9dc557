#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "K2Node.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphPin.h"
#include "EdGraph/EdGraphNode.h"
#include "BlueprintGeneration/NodeFactory.h"

class UEdGraphPin;

DECLARE_LOG_CATEGORY_EXTERN(LogPinCompatibilityChecker, Log, All);

/**
 * Enumeration for pin compatibility levels
 */
UENUM(BlueprintType)
enum class EPinCompatibilityLevel : uint8
{
    Incompatible        UMETA(DisplayName = "Incompatible"),
    RequiresConversion  UMETA(DisplayName = "Requires Conversion"),
    Compatible          UMETA(DisplayName = "Compatible"),
    PerfectMatch        UMETA(DisplayName = "Perfect Match")
};

/**
 * Enumeration for pin connection types
 */
UENUM(BlueprintType)
enum class EPinConnectionType : uint8
{
    Unknown         UMETA(DisplayName = "Unknown"),
    Execution       UMETA(DisplayName = "Execution"),
    Data            UMETA(DisplayName = "Data"),
    Delegate        UMETA(DisplayName = "Delegate"),
    Wildcard        UMETA(DisplayName = "Wildcard"),
    Reference       UMETA(DisplayName = "Reference"),
    Array           UMETA(DisplayName = "Array"),
    Map             UMETA(DisplayName = "Map"),
    Set             UMETA(DisplayName = "Set")
};

/**
 * Enumeration for conversion types
 */
UENUM(BlueprintType)
enum class EPinConversionType : uint8
{
    None            UMETA(DisplayName = "No Conversion"),
    Implicit        UMETA(DisplayName = "Implicit Conversion"),
    Explicit        UMETA(DisplayName = "Explicit Conversion"),
    Cast            UMETA(DisplayName = "Cast Required"),
    Promotion       UMETA(DisplayName = "Type Promotion"),
    Wrapper         UMETA(DisplayName = "Wrapper Required"),
    Custom          UMETA(DisplayName = "Custom Conversion")
};

/**
 * Structure containing detailed pin information
 */
USTRUCT(BlueprintType)
struct FPININFO
{
    GENERATED_BODY()

    /** The pin being analyzed */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    UEdGraphPin* Pin = nullptr;

    /** Name of the pin */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    FString PinName;

    /** Display name of the pin */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    FString DisplayName;

    /** Pin category (e.g., "exec", "bool", "int", "float", "string", "object") */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    FString PinCategory;

    /** Pin sub-category for more specific typing */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    FString PinSubCategory;

    /** Pin type object (for object pins) */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    FString PinTypeObject;

    /** Whether this is an input or output pin */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    bool bIsInput = false;

    /** Whether this is an output pin */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    bool bIsOutput = false;

    /** Whether this pin is an array */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    bool bIsArray = false;

    /** Whether this pin is a reference */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    bool bIsReference = false;

    /** Whether this pin is optional */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    bool bIsOptional = false;

    /** Whether this pin has a default value */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    bool bHasDefaultValue = false;

    /** Default value of the pin (if any) */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    FString DefaultValue;

    /** Connection type classification */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    EPinConnectionType ConnectionType = EPinConnectionType::Unknown;

    /** Tooltip/description for the pin */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    FString Description;

    /** Additional metadata */
    UPROPERTY(BlueprintReadOnly, Category = "Pin Info")
    TMap<FString, FString> Metadata;

    FPININFO()
    {
        Pin = nullptr;
        bIsInput = false;
        bIsOutput = false;
        bIsArray = false;
        bIsReference = false;
        bIsOptional = false;
        bHasDefaultValue = false;
        ConnectionType = EPinConnectionType::Unknown;
    }
};

/**
 * Structure containing pin compatibility analysis results
 */
USTRUCT(BlueprintType)
struct FPINCOMPATIBILITYRESULT
{
    GENERATED_BODY()

    /** Whether the pins are compatible */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    bool bIsCompatible = false;

    /** Level of compatibility */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    EPinCompatibilityLevel CompatibilityLevel = EPinCompatibilityLevel::Incompatible;

    /** Type of conversion required (if any) */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    EPinConversionType ConversionType = EPinConversionType::None;

    /** Confidence score (0.0 to 1.0) */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    float ConfidenceScore = 0.0f;

    /** Detailed explanation of compatibility */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    FString CompatibilityExplanation;

    /** Suggested conversion node (if applicable) */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    FString SuggestedConversionNode;

    /** Warning messages */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    TArray<FString> WarningMessages;

    /** Error messages */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    TArray<FString> ErrorMessages;

    /** Time taken for analysis (in milliseconds) */
    UPROPERTY(BlueprintReadOnly, Category = "Compatibility")
    float AnalysisTimeMs = 0.0f;

    FPINCOMPATIBILITYRESULT()
    {
        bIsCompatible = false;
        CompatibilityLevel = EPinCompatibilityLevel::Incompatible;
        ConversionType = EPinConversionType::None;
        ConfidenceScore = 0.0f;
        AnalysisTimeMs = 0.0f;
    }
};

/**
 * Structure for a pair of pins to check compatibility
 */
USTRUCT(BlueprintType)
struct FPINPAIR
{
    GENERATED_BODY()

    /** Source pin */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin Pair")
    UEdGraphPin* SourcePin;

    /** Target pin */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin Pair")
    UEdGraphPin* TargetPin;

    FPINPAIR()
        : SourcePin(nullptr)
        , TargetPin(nullptr)
    {
    }

    FPINPAIR(UEdGraphPin* InSourcePin, UEdGraphPin* InTargetPin)
        : SourcePin(InSourcePin)
        , TargetPin(InTargetPin)
    {
    }
};

/**
 * Structure for batch compatibility checking
 */
USTRUCT(BlueprintType)
struct FBATCHCOMPATIBILITYREQUEST
{
    GENERATED_BODY()

    /** Pairs of pins to check for compatibility */
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    TArray<FPINPAIR> PinPairs;

    /** Whether to include detailed analysis */
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bIncludeDetailedAnalysis = true;

    /** Whether to suggest conversion nodes */
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bSuggestConversions = true;

    /** Maximum analysis time per pin pair (in milliseconds) */
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    float MaxAnalysisTimeMs = 100.0f;

    FBATCHCOMPATIBILITYREQUEST()
    {
        bIncludeDetailedAnalysis = true;
        bSuggestConversions = true;
        MaxAnalysisTimeMs = 100.0f;
    }
};

/**
 * Structure for batch compatibility results
 */
USTRUCT(BlueprintType)
struct FBATCHCOMPATIBILITYRESULT
{
    GENERATED_BODY()

    /** Individual compatibility results */
    UPROPERTY(BlueprintReadOnly, Category = "Results")
    TArray<FPINCOMPATIBILITYRESULT> Results;

    /** Overall success status */
    UPROPERTY(BlueprintReadOnly, Category = "Results")
    bool bSuccess = false;

    /** Number of compatible pairs */
    UPROPERTY(BlueprintReadOnly, Category = "Results")
    int32 CompatiblePairs = 0;

    /** Number of incompatible pairs */
    UPROPERTY(BlueprintReadOnly, Category = "Results")
    int32 IncompatiblePairs = 0;

    /** Number of pairs requiring conversion */
    UPROPERTY(BlueprintReadOnly, Category = "Results")
    int32 ConversionRequiredPairs = 0;

    /** Total analysis time (in milliseconds) */
    UPROPERTY(BlueprintReadOnly, Category = "Results")
    float TotalAnalysisTimeMs = 0.0f;

    /** Error messages */
    UPROPERTY(BlueprintReadOnly, Category = "Results")
    TArray<FString> ErrorMessages;

    FBATCHCOMPATIBILITYRESULT()
    {
        bSuccess = false;
        CompatiblePairs = 0;
        IncompatiblePairs = 0;
        ConversionRequiredPairs = 0;
        TotalAnalysisTimeMs = 0.0f;
    }
};

/**
 * Pin Compatibility Checker
 * 
 * Provides comprehensive pin type analysis, compatibility checking, and connection validation
 * for blueprint nodes. Supports detailed compatibility analysis, conversion suggestions,
 * and batch processing for multiple pin pairs.
 */
class UE5BLUEPRINTGENERATOR_API FPinCompatibilityChecker
{
public:
    /** Constructor */
    FPinCompatibilityChecker();

    /** Destructor */
    ~FPinCompatibilityChecker();

    // Core Functionality

    /** Initialize the pin compatibility checker */
    bool Initialize();

    /** Shutdown the pin compatibility checker */
    void Shutdown();

    // Pin Analysis

    /** Get detailed information about a pin */
    FPININFO GetPinInfo(UEdGraphPin* Pin);

    /** Analyze pin type and characteristics */
    FPININFO AnalyzePinType(UEdGraphPin* Pin);

    /** Get all pins from a node with detailed information */
    TArray<FPININFO> GetNodePins(UK2Node* Node);

    /** Get input pins from a node */
    TArray<FPININFO> GetInputPins(UK2Node* Node);

    /** Get output pins from a node */
    TArray<FPININFO> GetOutputPins(UK2Node* Node);

    // Compatibility Checking

    /** Check compatibility between two pins */
    FPINCOMPATIBILITYRESULT CheckPinCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Check if two pins can be connected directly */
    bool CanPinsConnect(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Check if connection requires type conversion */
    bool RequiresTypeConversion(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Get suggested conversion node for incompatible pins */
    FString GetSuggestedConversionNode(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    // Batch Processing

    /** Check compatibility for multiple pin pairs */
    FBATCHCOMPATIBILITYRESULT CheckBatchCompatibility(const FBATCHCOMPATIBILITYREQUEST& Request);

    /** Find all compatible pins for a given pin */
    TArray<UEdGraphPin*> FindCompatiblePins(UEdGraphPin* SourcePin, const TArray<UEdGraphPin*>& CandidatePins);

    /** Find best matching pin from candidates */
    UEdGraphPin* FindBestMatchingPin(UEdGraphPin* SourcePin, const TArray<UEdGraphPin*>& CandidatePins);

    // Validation

    /** Validate an existing pin connection */
    bool ValidatePinConnection(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Validate all connections in a graph */
    TArray<FString> ValidateGraphConnections(UEdGraph* Graph);

    /** Check for circular dependencies in connections */
    bool HasCircularDependency(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    // Utility Functions

    /** Get pin type as human-readable string */
    FString GetPinTypeString(UEdGraphPin* Pin);

    /** Check if pin is execution pin */
    bool IsExecutionPin(UEdGraphPin* Pin);

    /** Check if pin is data pin */
    bool IsDataPin(UEdGraphPin* Pin);

    /** Check if pin is wildcard pin */
    bool IsWildcardPin(UEdGraphPin* Pin);

    /** Check if pin is array pin */
    bool IsArrayPin(UEdGraphPin* Pin);

    /** Check if pin is reference pin */
    bool IsReferencePin(UEdGraphPin* Pin);

    /** Get base type of array pin */
    FString GetArrayElementType(UEdGraphPin* Pin);

    // Statistics and Debugging

    /** Get compatibility checker statistics */
    TMap<FString, FString> GetCompatibilityStatistics();

    /** Enable/disable verbose logging */
    void SetVerboseLogging(bool bEnabled);

    /** Enable/disable compatibility statistics tracking */
    void SetTrackStatistics(bool bEnabled);

private:
    // Internal state
    bool bIsInitialized;
    bool bVerboseLogging;
    bool bTrackStatistics;

    // Statistics
    int32 TotalCompatibilityChecks;
    int32 TotalBatchOperations;
    int32 TotalValidationOperations;
    float LastAnalysisTimeMs;

    // Compatibility rules and mappings
    TMap<FString, TArray<FString>> CompatibilityRules;
    TMap<FString, FString> ConversionNodeMappings;
    TMap<FString, int32> CompatibilityStatistics;

    // Helper Methods

    /** Analyze pin type details */
    EPinConnectionType AnalyzePinConnectionType(UEdGraphPin* Pin);

    /** Check basic type compatibility */
    bool CheckBasicTypeCompatibility(const FString& SourceType, const FString& TargetType);

    /** Check object type compatibility */
    bool CheckObjectTypeCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Check array compatibility */
    bool CheckArrayCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Calculate compatibility score */
    float CalculateCompatibilityScore(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Determine required conversion type */
    EPinConversionType DetermineConversionType(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Generate compatibility explanation */
    FString GenerateCompatibilityExplanation(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, EPinCompatibilityLevel Level);

    /** Initialize compatibility rules */
    void InitializeCompatibilityRules();

    /** Initialize conversion mappings */
    void InitializeConversionMappings();

    /** Log compatibility activity */
    void LogCompatibilityActivity(const FString& Message, bool bIsError = false);

    /** Update compatibility statistics */
    void UpdateCompatibilityStatistics(const FString& Operation, const FString& PinType = FString());
}; 