# UE5 Blueprint Plugin Generator - AI Model Integration Specification

## Overview

This document defines the technical specifications for integrating local AI models (Ollama and LMStudio) with the UE5 Blueprint Generator plugin. It covers REST API endpoints, request/response formats, settings management, and extensibility considerations.

## AI Model Providers

### Supported Providers

1. **Ollama**
   - Default endpoint: `http://localhost:11434/api`
   - API documentation reference: [Ollama API](https://github.com/ollama/ollama/blob/main/docs/api.md)
   - Supported models: Llama3, Mistral, CodeLlama, and other models available in Ollama

2. **LMStudio**
   - Default endpoint: `http://localhost:1234/v1`
   - API documentation reference: [LMStudio API](https://lmstudio.ai/docs/local-inference-server)
   - Supported models: Any model loaded in LMStudio's local inference server

### Provider Interface

```cpp
// AIModelProviderInterface.h
class IAIModelProvider
{
public:
    virtual ~IAIModelProvider() = default;
    
    // Connection management
    virtual bool Initialize(const FString& Endpoint, const FString& ApiKey = TEXT("")) = 0;
    virtual bool TestConnection() = 0;
    virtual bool IsConnected() const = 0;
    
    // Model operations
    virtual TArray<FString> GetAvailableModels() = 0;
    virtual bool SetActiveModel(const FString& ModelName) = 0;
    virtual FString GetActiveModel() const = 0;
    
    // Request handling
    virtual bool SendRequest(const FBlueprintGenerationRequest& Request, FBlueprintGenerationResponse& OutResponse) = 0;
    virtual bool SendStreamingRequest(const FBlueprintGenerationRequest& Request, FOnStreamingResponseDelegate OnChunk, FOnRequestCompleteDelegate OnComplete) = 0;
    virtual void CancelRequest() = 0;
    
    // Settings
    virtual void SetTemperature(float Temperature) = 0;
    virtual void SetMaxTokens(int32 MaxTokens) = 0;
    virtual void SetTopP(float TopP) = 0;
    virtual void SetSystemPrompt(const FString& SystemPrompt) = 0;
    
    // Provider info
    virtual FString GetProviderName() const = 0;
    virtual FString GetProviderVersion() const = 0;
};
```

## REST API Integration

### Ollama API Integration

#### Endpoint Structure
- Base URL: `http://localhost:11434/api`
- Generation endpoint: `/generate`
- Chat endpoint: `/chat`
- Models list endpoint: `/tags`

#### Request Format (Generate)
```json
{
  "model": "llama3",
  "prompt": "Create a blueprint function that calculates damage based on player level and weapon strength",
  "system": "You are an expert in Unreal Engine 5 Blueprint creation. Generate detailed, efficient blueprint structures following UE5 best practices.",
  "stream": false,
  "temperature": 0.7,
  "top_p": 0.9,
  "max_tokens": 2048
}
```

#### Response Format (Generate)
```json
{
  "model": "llama3",
  "created_at": "2025-06-02T15:20:00.000Z",
  "response": "To create a blueprint function that calculates damage based on player level and weapon strength, follow these steps:\n\n1. First, create a new function in your blueprint called \"CalculateDamage\".\n2. Add two input parameters:\n   - PlayerLevel (Integer)\n   - WeaponStrength (Float)\n3. Add one output parameter:\n   - DamageAmount (Float)\n4. Inside the function, add a Float * Float node.\n5. Connect PlayerLevel to the first input of the multiplication node.\n6. Connect WeaponStrength to the second input.\n7. Add a Float + Float node.\n8. Connect the output of the multiplication to the first input of the addition.\n9. Add a constant value of 5.0 as a base damage.\n10. Connect the constant to the second input of the addition.\n11. Connect the output of the addition to the DamageAmount output variable.\n\nThis creates a simple damage calculation formula: Damage = (PlayerLevel * WeaponStrength) + 5.0",
  "done": true,
  "total_duration": 2540359083,
  "load_duration": 1235021,
  "prompt_eval_count": 26,
  "prompt_eval_duration": 343606917,
  "eval_count": 257,
  "eval_duration": 2195409145
}
```

#### Streaming Response Format
```json
{"model":"llama3","created_at":"2025-06-02T15:20:00.000Z","response":"To ","done":false}
{"model":"llama3","created_at":"2025-06-02T15:20:00.000Z","response":"create ","done":false}
{"model":"llama3","created_at":"2025-06-02T15:20:00.000Z","response":"a ","done":false}
// ... more chunks ...
{"model":"llama3","created_at":"2025-06-02T15:20:00.000Z","response":"","done":true,"total_duration":2540359083,"load_duration":1235021,"prompt_eval_count":26,"prompt_eval_duration":343606917,"eval_count":257,"eval_duration":2195409145}
```

### LMStudio API Integration

#### Endpoint Structure
- Base URL: `http://localhost:1234/v1`
- Completions endpoint: `/completions`
- Chat completions endpoint: `/chat/completions`
- Models list endpoint: `/models`

#### Request Format (Chat Completions)
```json
{
  "model": "local-model",
  "messages": [
    {
      "role": "system",
      "content": "You are an expert in Unreal Engine 5 Blueprint creation. Generate detailed, efficient blueprint structures following UE5 best practices."
    },
    {
      "role": "user",
      "content": "Create a blueprint function that calculates damage based on player level and weapon strength"
    }
  ],
  "temperature": 0.7,
  "top_p": 0.9,
  "max_tokens": 2048,
  "stream": false
}
```

#### Response Format (Chat Completions)
```json
{
  "id": "chatcmpl-123456789",
  "object": "chat.completion",
  "created": **********,
  "model": "local-model",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "To create a blueprint function that calculates damage based on player level and weapon strength, follow these steps:\n\n1. First, create a new function in your blueprint called \"CalculateDamage\".\n2. Add two input parameters:\n   - PlayerLevel (Integer)\n   - WeaponStrength (Float)\n3. Add one output parameter:\n   - DamageAmount (Float)\n4. Inside the function, add a Float * Float node.\n5. Connect PlayerLevel to the first input of the multiplication node.\n6. Connect WeaponStrength to the second input.\n7. Add a Float + Float node.\n8. Connect the output of the multiplication to the first input of the addition.\n9. Add a constant value of 5.0 as a base damage.\n10. Connect the constant to the second input of the addition.\n11. Connect the output of the addition to the DamageAmount output variable.\n\nThis creates a simple damage calculation formula: Damage = (PlayerLevel * WeaponStrength) + 5.0"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 48,
    "completion_tokens": 257,
    "total_tokens": 305
  }
}
```

#### Streaming Response Format
```json
{"id":"chatcmpl-123456789","object":"chat.completion.chunk","created":**********,"model":"local-model","choices":[{"index":0,"delta":{"role":"assistant","content":"To "},"finish_reason":null}]}
{"id":"chatcmpl-123456789","object":"chat.completion.chunk","created":**********,"model":"local-model","choices":[{"index":0,"delta":{"content":"create "},"finish_reason":null}]}
// ... more chunks ...
{"id":"chatcmpl-123456789","object":"chat.completion.chunk","created":**********,"model":"local-model","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}
```

## Provider Implementation Classes

### Ollama Provider Implementation

```cpp
// OllamaProvider.h
class FOllamaProvider : public IAIModelProvider
{
public:
    FOllamaProvider();
    virtual ~FOllamaProvider();
    
    // IAIModelProvider interface implementation
    virtual bool Initialize(const FString& Endpoint, const FString& ApiKey = TEXT("")) override;
    virtual bool TestConnection() override;
    virtual bool IsConnected() const override;
    virtual TArray<FString> GetAvailableModels() override;
    virtual bool SetActiveModel(const FString& ModelName) override;
    virtual FString GetActiveModel() const override;
    virtual bool SendRequest(const FBlueprintGenerationRequest& Request, FBlueprintGenerationResponse& OutResponse) override;
    virtual bool SendStreamingRequest(const FBlueprintGenerationRequest& Request, FOnStreamingResponseDelegate OnChunk, FOnRequestCompleteDelegate OnComplete) override;
    virtual void CancelRequest() override;
    virtual void SetTemperature(float Temperature) override;
    virtual void SetMaxTokens(int32 MaxTokens) override;
    virtual void SetTopP(float TopP) override;
    virtual void SetSystemPrompt(const FString& SystemPrompt) override;
    virtual FString GetProviderName() const override;
    virtual FString GetProviderVersion() const override;
    
private:
    // HTTP request handling
    TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateRequest(const FString& Endpoint);
    bool ParseModelsResponse(const FString& ResponseString, TArray<FString>& OutModels);
    bool ParseGenerateResponse(const FString& ResponseString, FString& OutResponse);
    void HandleStreamingResponse(const FString& ChunkData, FOnStreamingResponseDelegate& OnChunk);
    
    // Provider state
    FString BaseEndpoint;
    FString CurrentModel;
    bool bIsInitialized;
    bool bIsConnected;
    
    // Model settings
    float Temperature;
    int32 MaxTokens;
    float TopP;
    FString SystemPrompt;
    
    // Active request tracking
    TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> ActiveRequest;
};
```

### LMStudio Provider Implementation

```cpp
// LMStudioProvider.h
class FLMStudioProvider : public IAIModelProvider
{
public:
    FLMStudioProvider();
    virtual ~FLMStudioProvider();
    
    // IAIModelProvider interface implementation
    virtual bool Initialize(const FString& Endpoint, const FString& ApiKey = TEXT("")) override;
    virtual bool TestConnection() override;
    virtual bool IsConnected() const override;
    virtual TArray<FString> GetAvailableModels() override;
    virtual bool SetActiveModel(const FString& ModelName) override;
    virtual FString GetActiveModel() const override;
    virtual bool SendRequest(const FBlueprintGenerationRequest& Request, FBlueprintGenerationResponse& OutResponse) override;
    virtual bool SendStreamingRequest(const FBlueprintGenerationRequest& Request, FOnStreamingResponseDelegate OnChunk, FOnRequestCompleteDelegate OnComplete) override;
    virtual void CancelRequest() override;
    virtual void SetTemperature(float Temperature) override;
    virtual void SetMaxTokens(int32 MaxTokens) override;
    virtual void SetTopP(float TopP) override;
    virtual void SetSystemPrompt(const FString& SystemPrompt) override;
    virtual FString GetProviderName() const override;
    virtual FString GetProviderVersion() const override;
    
private:
    // HTTP request handling
    TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateRequest(const FString& Endpoint);
    bool ParseModelsResponse(const FString& ResponseString, TArray<FString>& OutModels);
    bool ParseChatCompletionResponse(const FString& ResponseString, FString& OutResponse);
    void HandleStreamingResponse(const FString& ChunkData, FOnStreamingResponseDelegate& OnChunk);
    
    // Provider state
    FString BaseEndpoint;
    FString CurrentModel;
    bool bIsInitialized;
    bool bIsConnected;
    
    // Model settings
    float Temperature;
    int32 MaxTokens;
    float TopP;
    FString SystemPrompt;
    
    // Active request tracking
    TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> ActiveRequest;
};
```

## Provider Factory and Registration

```cpp
// AIModelProviderFactory.h
class FAIModelProviderFactory
{
public:
    static TSharedPtr<IAIModelProvider> CreateProvider(const FString& ProviderName);
    static void RegisterProvider(const FString& ProviderName, TFunction<TSharedPtr<IAIModelProvider>()> FactoryFunction);
    static TArray<FString> GetRegisteredProviders();
    
private:
    static TMap<FString, TFunction<TSharedPtr<IAIModelProvider>()>> RegisteredProviders;
};

// AIModelProviderFactory.cpp
TMap<FString, TFunction<TSharedPtr<IAIModelProvider>()>> FAIModelProviderFactory::RegisteredProviders;

TSharedPtr<IAIModelProvider> FAIModelProviderFactory::CreateProvider(const FString& ProviderName)
{
    if (RegisteredProviders.Contains(ProviderName))
    {
        return RegisteredProviders[ProviderName]();
    }
    return nullptr;
}

void FAIModelProviderFactory::RegisterProvider(const FString& ProviderName, TFunction<TSharedPtr<IAIModelProvider>()> FactoryFunction)
{
    RegisteredProviders.Add(ProviderName, FactoryFunction);
}

TArray<FString> FAIModelProviderFactory::GetRegisteredProviders()
{
    TArray<FString> Result;
    RegisteredProviders.GetKeys(Result);
    return Result;
}
```

## Blueprint Generation Request/Response Structures

```cpp
// BlueprintGenerationTypes.h
enum class EBlueprintElementType : uint8
{
    Function,
    Event,
    Variable,
    MacroLibrary,
    AnimGraph,
    StateGraph
};

struct FBlueprintNodeDefinition
{
    FString NodeType;
    FString NodeTitle;
    TMap<FString, FString> InputPins;
    TMap<FString, FString> OutputPins;
    TArray<FString> NodeComments;
    FVector2D NodePosition;
};

struct FBlueprintConnectionDefinition
{
    int32 SourceNodeIndex;
    FString SourcePinName;
    int32 TargetNodeIndex;
    FString TargetPinName;
};

struct FBlueprintVariableDefinition
{
    FString VariableName;
    FString VariableType;
    FString DefaultValue;
    FString Category;
    TArray<FString> VariableComments;
    bool bIsExposed;
};

struct FBlueprintFunctionDefinition
{
    FString FunctionName;
    TArray<FBlueprintVariableDefinition> InputParameters;
    TArray<FBlueprintVariableDefinition> OutputParameters;
    TArray<FString> FunctionComments;
    bool bIsPure;
    FString Category;
};

struct FBlueprintEventDefinition
{
    FString EventName;
    TArray<FBlueprintVariableDefinition> Parameters;
    TArray<FString> EventComments;
    FString Category;
};

struct FBlueprintGenerationRequest
{
    FString NaturalLanguageDescription;
    EBlueprintElementType ElementType;
    FString BlueprintName;
    TArray<FString> AdditionalInstructions;
    bool bGenerateComments;
    bool bIncludeErrorHandling;
    bool bOptimizePerformance;
    bool bUseAdvancedLogic;
};

struct FBlueprintGenerationResponse
{
    bool bSuccess;
    FString ErrorMessage;
    
    // Blueprint structure
    TArray<FBlueprintNodeDefinition> Nodes;
    TArray<FBlueprintConnectionDefinition> Connections;
    TArray<FBlueprintVariableDefinition> Variables;
    TArray<FBlueprintFunctionDefinition> Functions;
    TArray<FBlueprintEventDefinition> Events;
    
    // Metadata
    FString GeneratedDescription;
    TArray<FString> GeneralComments;
    float GenerationTime;
    int32 TokensUsed;
};

// Delegates
DECLARE_DELEGATE_OneParam(FOnStreamingResponseDelegate, const FString&);
DECLARE_DELEGATE_OneParam(FOnRequestCompleteDelegate, const FBlueprintGenerationResponse&);
```

## AI Model Settings Management

### Settings Structure

```cpp
// AIModelSettings.h
struct FAIModelSettings
{
    // Provider settings
    FString ProviderName;
    FString Endpoint;
    FString ApiKey;
    
    // Model settings
    FString ModelName;
    float Temperature;
    int32 MaxTokens;
    float TopP;
    FString SystemPrompt;
    
    // Advanced settings
    bool bUseStreaming;
    bool bEnableCaching;
    int32 ContextWindow;
    
    // Default constructor with reasonable defaults
    FAIModelSettings()
        : ProviderName(TEXT("Ollama"))
        , Endpoint(TEXT("http://localhost:11434/api"))
        , ApiKey(TEXT(""))
        , ModelName(TEXT("llama3"))
        , Temperature(0.7f)
        , MaxTokens(2048)
        , TopP(0.9f)
        , SystemPrompt(TEXT("You are an expert in Unreal Engine 5 Blueprint creation. Generate detailed, efficient blueprint structures following UE5 best practices."))
        , bUseStreaming(true)
        , bEnableCaching(false)
        , ContextWindow(4096)
    {
    }
};
```

### Settings Manager

```cpp
// AIModelSettingsManager.h
class FAIModelSettingsManager
{
public:
    static FAIModelSettingsManager& Get();
    
    // Settings operations
    FAIModelSettings GetCurrentSettings() const;
    void SetCurrentSettings(const FAIModelSettings& Settings);
    
    // Preset management
    TArray<FString> GetPresetNames() const;
    bool SavePreset(const FString& PresetName, const FAIModelSettings& Settings);
    bool LoadPreset(const FString& PresetName, FAIModelSettings& OutSettings);
    bool DeletePreset(const FString& PresetName);
    
    // Default settings
    void RestoreDefaults();
    
private:
    FAIModelSettingsManager();
    
    // Settings storage
    FAIModelSettings CurrentSettings;
    TMap<FString, FAIModelSettings> SavedPresets;
    
    // Settings persistence
    bool LoadSettingsFromDisk();
    bool SaveSettingsToDisk();
    FString GetSettingsFilePath() const;
};
```

## Prompt Templates

### System Prompt Templates

```cpp
// PromptTemplates.h
namespace PromptTemplates
{
    const FString DefaultSystemPrompt = TEXT(
        "You are an expert in Unreal Engine 5 Blueprint creation. "
        "Generate detailed, efficient blueprint structures following UE5 best practices. "
        "Your output should be structured, clear, and optimized for performance. "
        "Include comments to explain complex logic and provide context for future developers."
    );
    
    const FString FunctionGenerationPrompt = TEXT(
        "You are an expert in Unreal Engine 5 Blueprint creation. "
        "Generate a detailed blueprint function based on the description provided. "
        "Your response should include:\n"
        "1. Function signature with appropriate parameters and return values\n"
        "2. Complete node structure with all necessary operations\n"
        "3. Proper connections between nodes\n"
        "4. Variable types and default values\n"
        "5. Descriptive comments explaining the logic\n"
        "6. Error handling where appropriate\n"
        "Ensure the function follows UE5 best practices and is optimized for performance."
    );
    
    const FString EventGenerationPrompt = TEXT(
        "You are an expert in Unreal Engine 5 Blueprint creation. "
        "Generate a detailed blueprint event based on the description provided. "
        "Your response should include:\n"
        "1. Event definition with appropriate parameters\n"
        "2. Complete node structure for event handling\n"
        "3. Proper connections between nodes\n"
        "4. Variable types and default values\n"
        "5. Descriptive comments explaining the logic\n"
        "6. Error handling where appropriate\n"
        "Ensure the event follows UE5 best practices and is optimized for performance."
    );
    
    const FString VariableSetupPrompt = TEXT(
        "You are an expert in Unreal Engine 5 Blueprint creation. "
        "Generate a set of blueprint variables based on the description provided. "
        "Your response should include:\n"
        "1. Variable names following UE5 naming conventions\n"
        "2. Appropriate variable types\n"
        "3. Default values where applicable\n"
        "4. Variable categories for organization\n"
        "5. Descriptive tooltips explaining each variable's purpose\n"
        "6. Access specifiers (public/private)\n"
        "Ensure the variables follow UE5 best practices and are properly organized."
    );
}
```

### Request Formatting

```cpp
// AIModelRequestFormatter.h
class FAIModelRequestFormatter
{
public:
    static FString FormatOllamaRequest(const FBlueprintGenerationRequest& Request, const FAIModelSettings& Settings);
    static FString FormatLMStudioRequest(const FBlueprintGenerationRequest& Request, const FAIModelSettings& Settings);
    
private:
    static FString GetSystemPromptForRequest(const FBlueprintGenerationRequest& Request);
    static FString FormatNaturalLanguageInput(const FBlueprintGenerationRequest& Request);
    static FString GetElementTypeString(EBlueprintElementType ElementType);
};

// AIModelRequestFormatter.cpp
FString FAIModelRequestFormatter::FormatOllamaRequest(const FBlueprintGenerationRequest& Request, const FAIModelSettings& Settings)
{
    FString SystemPrompt = GetSystemPromptForRequest(Request);
    FString FormattedInput = FormatNaturalLanguageInput(Request);
    
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("model"), Settings.ModelName);
    JsonObject->SetStringField(TEXT("prompt"), FormattedInput);
    JsonObject->SetStringField(TEXT("system"), SystemPrompt);
    JsonObject->SetBoolField(TEXT("stream"), Settings.bUseStreaming);
    JsonObject->SetNumberField(TEXT("temperature"), Settings.Temperature);
    JsonObject->SetNumberField(TEXT("top_p"), Settings.TopP);
    JsonObject->SetNumberField(TEXT("max_tokens"), Settings.MaxTokens);
    
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    return OutputString;
}

FString FAIModelRequestFormatter::FormatLMStudioRequest(const FBlueprintGenerationRequest& Request, const FAIModelSettings& Settings)
{
    FString SystemPrompt = GetSystemPromptForRequest(Request);
    FString FormattedInput = FormatNaturalLanguageInput(Request);
    
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("model"), Settings.ModelName);
    
    TArray<TSharedPtr<FJsonValue>> Messages;
    
    // System message
    TSharedPtr<FJsonObject> SystemMessage = MakeShareable(new FJsonObject);
    SystemMessage->SetStringField(TEXT("role"), TEXT("system"));
    SystemMessage->SetStringField(TEXT("content"), SystemPrompt);
    Messages.Add(MakeShareable(new FJsonValueObject(SystemMessage)));
    
    // User message
    TSharedPtr<FJsonObject> UserMessage = MakeShareable(new FJsonObject);
    UserMessage->SetStringField(TEXT("role"), TEXT("user"));
    UserMessage->SetStringField(TEXT("content"), FormattedInput);
    Messages.Add(MakeShareable(new FJsonValueObject(UserMessage)));
    
    JsonObject->SetArrayField(TEXT("messages"), Messages);
    JsonObject->SetNumberField(TEXT("temperature"), Settings.Temperature);
    JsonObject->SetNumberField(TEXT("top_p"), Settings.TopP);
    JsonObject->SetNumberField(TEXT("max_tokens"), Settings.MaxTokens);
    JsonObject->SetBoolField(TEXT("stream"), Settings.bUseStreaming);
    
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    return OutputString;
}
```

## Response Parsing

```cpp
// AIModelResponseParser.h
class FAIModelResponseParser
{
public:
    static bool ParseOllamaResponse(const FString& ResponseString, FBlueprintGenerationResponse& OutResponse);
    static bool ParseLMStudioResponse(const FString& ResponseString, FBlueprintGenerationResponse& OutResponse);
    
private:
    static bool ParseBlueprintStructure(const FString& Content, FBlueprintGenerationResponse& OutResponse);
    static bool ExtractNodesFromText(const FString& Content, TArray<FBlueprintNodeDefinition>& OutNodes);
    static bool ExtractConnectionsFromText(const FString& Content, TArray<FBlueprintConnectionDefinition>& OutConnections);
    static bool ExtractVariablesFromText(const FString& Content, TArray<FBlueprintVariableDefinition>& OutVariables);
    static bool ExtractFunctionsFromText(const FString& Content, TArray<FBlueprintFunctionDefinition>& OutFunctions);
    static bool ExtractEventsFromText(const FString& Content, TArray<FBlueprintEventDefinition>& OutEvents);
};
```

## AI Model Manager

```cpp
// AIModelManager.h
class FAIModelManager
{
public:
    static FAIModelManager& Get();
    
    // Initialization
    bool Initialize();
    bool Shutdown();
    
    // Provider management
    bool SetActiveProvider(const FString& ProviderName);
    FString GetActiveProviderName() const;
    TArray<FString> GetAvailableProviders() const;
    
    // Model management
    bool SetActiveModel(const FString& ModelName);
    FString GetActiveModel() const;
    TArray<FString> GetAvailableModels() const;
    
    // Connection management
    bool TestConnection();
    bool IsConnected() const;
    
    // Request handling
    bool SendGenerationRequest(const FBlueprintGenerationRequest& Request, FBlueprintGenerationResponse& OutResponse);
    bool SendStreamingGenerationRequest(const FBlueprintGenerationRequest& Request, FOnStreamingResponseDelegate OnChunk, FOnRequestCompleteDelegate OnComplete);
    void CancelRequest();
    
    // Settings
    void ApplySettings(const FAIModelSettings& Settings);
    FAIModelSettings GetCurrentSettings() const;
    
private:
    FAIModelManager();
    
    // Provider instance
    TSharedPtr<IAIModelProvider> ActiveProvider;
    FString ActiveProviderName;
    
    // Settings
    FAIModelSettings CurrentSettings;
};
```

## Extensibility for New AI Models

### Provider Registration System

```cpp
// ProviderRegistration.h
#define REGISTER_AI_MODEL_PROVIDER(ProviderName, ProviderClass) \
    class FRegister##ProviderClass \
    { \
    public: \
        FRegister##ProviderClass() \
        { \
            FAIModelProviderFactory::RegisterProvider(ProviderName, []() -> TSharedPtr<IAIModelProvider> { \
                return MakeShareable(new ProviderClass()); \
            }); \
        } \
    }; \
    static FRegister##ProviderClass Register##ProviderClass##Instance;

// Example usage in implementation files:
// OllamaProvider.cpp
REGISTER_AI_MODEL_PROVIDER(TEXT("Ollama"), FOllamaProvider)

// LMStudioProvider.cpp
REGISTER_AI_MODEL_PROVIDER(TEXT("LMStudio"), FLMStudioProvider)
```

### Custom Provider Implementation Guide

```cpp
/*
To add support for a new AI model provider:

1. Create a new provider class that implements the IAIModelProvider interface
2. Implement all required methods for API communication
3. Register the provider using the REGISTER_AI_MODEL_PROVIDER macro
4. Add appropriate request formatting and response parsing methods
5. Update the UI to include the new provider in the dropdown

Example for adding a new provider:

// NewProvider.h
class FNewProvider : public IAIModelProvider
{
public:
    FNewProvider();
    virtual ~FNewProvider();
    
    // Implement all IAIModelProvider interface methods
    // ...
};

// NewProvider.cpp
#include "NewProvider.h"
#include "ProviderRegistration.h"

// Implementation of all methods
// ...

// Register the provider
REGISTER_AI_MODEL_PROVIDER(TEXT("NewProvider"), FNewProvider)
*/
```

## Configuration File Format

### Plugin Settings JSON

```json
{
  "providers": {
    "Ollama": {
      "endpoint": "http://localhost:11434/api",
      "api_key": ""
    },
    "LMStudio": {
      "endpoint": "http://localhost:1234/v1",
      "api_key": ""
    }
  },
  "current_provider": "Ollama",
  "current_model": "llama3",
  "settings": {
    "temperature": 0.7,
    "max_tokens": 2048,
    "top_p": 0.9,
    "use_streaming": true,
    "enable_caching": false,
    "context_window": 4096
  },
  "system_prompts": {
    "default": "You are an expert in Unreal Engine 5 Blueprint creation...",
    "function": "You are an expert in Unreal Engine 5 Blueprint creation...",
    "event": "You are an expert in Unreal Engine 5 Blueprint creation...",
    "variable": "You are an expert in Unreal Engine 5 Blueprint creation..."
  },
  "presets": {
    "Default": {
      "provider": "Ollama",
      "model": "llama3",
      "temperature": 0.7,
      "max_tokens": 2048,
      "top_p": 0.9,
      "system_prompt": "You are an expert in Unreal Engine 5 Blueprint creation..."
    },
    "Creative": {
      "provider": "Ollama",
      "model": "llama3",
      "temperature": 0.9,
      "max_tokens": 2048,
      "top_p": 0.95,
      "system_prompt": "You are an expert in Unreal Engine 5 Blueprint creation..."
    },
    "Precise": {
      "provider": "LMStudio",
      "model": "local-model",
      "temperature": 0.3,
      "max_tokens": 4096,
      "top_p": 0.8,
      "system_prompt": "You are an expert in Unreal Engine 5 Blueprint creation..."
    }
  }
}
```

## Error Handling for AI Model Integration

```cpp
// AIModelError.h
enum class EAIModelErrorType : uint8
{
    None,
    ConnectionFailed,
    AuthenticationFailed,
    InvalidRequest,
    ResponseParsingFailed,
    ModelNotFound,
    RequestTimeout,
    ServerError,
    UnknownError
};

struct FAIModelError
{
    EAIModelErrorType ErrorType;
    FString ErrorMessage;
    int32 ErrorCode;
    FString RawResponse;
    
    FAIModelError()
        : ErrorType(EAIModelErrorType::None)
        , ErrorCode(0)
    {
    }
    
    FAIModelError(EAIModelErrorType InErrorType, const FString& InErrorMessage, int32 InErrorCode = 0, const FString& InRawResponse = TEXT(""))
        : ErrorType(InErrorType)
        , ErrorMessage(InErrorMessage)
        , ErrorCode(InErrorCode)
        , RawResponse(InRawResponse)
    {
    }
    
    bool IsError() const
    {
        return ErrorType != EAIModelErrorType::None;
    }
    
    FString GetUserFriendlyMessage() const
    {
        switch (ErrorType)
        {
            case EAIModelErrorType::ConnectionFailed:
                return FString::Printf(TEXT("Failed to connect to AI model server. Please check if the server is running at the specified endpoint. Error: %s"), *ErrorMessage);
            case EAIModelErrorType::AuthenticationFailed:
                return TEXT("Authentication failed. Please check your API key.");
            case EAIModelErrorType::InvalidRequest:
                return FString::Printf(TEXT("Invalid request format. Error: %s"), *ErrorMessage);
            case EAIModelErrorType::ResponseParsingFailed:
                return TEXT("Failed to parse response from AI model server.");
            case EAIModelErrorType::ModelNotFound:
                return FString::Printf(TEXT("The specified model was not found. Error: %s"), *ErrorMessage);
            case EAIModelErrorType::RequestTimeout:
                return TEXT("Request timed out. The server took too long to respond.");
            case EAIModelErrorType::ServerError:
                return FString::Printf(TEXT("Server error occurred. Error code: %d, Message: %s"), ErrorCode, *ErrorMessage);
            case EAIModelErrorType::UnknownError:
                return FString::Printf(TEXT("An unknown error occurred. Error: %s"), *ErrorMessage);
            default:
                return TEXT("No error.");
        }
    }
    
    FString GetTechnicalDetails() const
    {
        return FString::Printf(TEXT("Error Type: %d\nError Code: %d\nError Message: %s\nRaw Response: %s"),
            static_cast<int32>(ErrorType), ErrorCode, *ErrorMessage, *RawResponse);
    }
};
```

## Conclusion

This AI Model Integration Specification provides a comprehensive framework for connecting the UE5 Blueprint Generator plugin with local AI models. The design supports both Ollama and LMStudio through a flexible provider interface, allowing for future expansion to additional AI model providers. The implementation includes robust error handling, configuration management, and extensibility features to ensure a reliable and adaptable integration.

Key features of this integration include:
- Modular provider architecture with a common interface
- Comprehensive REST API integration for multiple providers
- Flexible settings management with preset support
- Structured request and response handling
- Robust error detection and reporting
- Extensibility for future AI model providers

This specification serves as the technical foundation for implementing the AI model integration component of the UE5 Blueprint Generator plugin.
