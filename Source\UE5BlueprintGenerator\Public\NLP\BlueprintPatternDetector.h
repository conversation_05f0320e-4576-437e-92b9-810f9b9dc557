#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

/**
 * Enumeration of different blueprint pattern types
 */
UENUM(BlueprintType)
enum class EBlueprintPatternType : uint8
{
	Unknown			UMETA(DisplayName = "Unknown"),
	SimpleFunction	UMETA(DisplayName = "Simple Function"),
	EventHandler	UMETA(DisplayName = "Event Handler"),
	DataProcessor	UMETA(DisplayName = "Data Processor"),
	StateManager	UMETA(DisplayName = "State Manager"),
	TimerBased		UMETA(DisplayName = "Timer Based"),
	InputHandler	UMETA(DisplayName = "Input Handler"),
	AnimationController UMETA(DisplayName = "Animation Controller"),
	AIBehavior		UMETA(DisplayName = "AI Behavior"),
	NetworkReplication UMETA(DisplayName = "Network Replication"),
	CustomEvent		UMETA(DisplayName = "Custom Event"),
	UIController	UMETA(DisplayName = "UI Controller"),
	GameplayMechanic UMETA(DisplayName = "Gameplay Mechanic"),
	UtilityFunction	UMETA(DisplayName = "Utility Function")
};

/**
 * Structure representing a blueprint pattern with its characteristics
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FBlueprintPattern
{
	GENERATED_BODY()

	/** Type of the pattern */
	UPROPERTY(BlueprintReadOnly)
	EBlueprintPatternType PatternType;

	/** Human-readable name of the pattern */
	UPROPERTY(BlueprintReadOnly)
	FString PatternName;

	/** Description of what this pattern does */
	UPROPERTY(BlueprintReadOnly)
	FString Description;

	/** Key terms that indicate this pattern */
	UPROPERTY(BlueprintReadOnly)
	TArray<FString> KeyTerms;

	/** Complexity score (0.0 to 2.0) */
	UPROPERTY(BlueprintReadOnly)
	float Complexity;

	/** Required UE5 components or systems */
	UPROPERTY(BlueprintReadOnly)
	TArray<FString> RequiredComponents;

	/** Common node types used in this pattern */
	UPROPERTY(BlueprintReadOnly)
	TArray<FString> CommonNodes;

	/** Typical input/output parameters */
	UPROPERTY(BlueprintReadOnly)
	TArray<FString> TypicalParameters;

	FBlueprintPattern()
		: PatternType(EBlueprintPatternType::Unknown)
		, Complexity(1.0f)
	{
	}

	FBlueprintPattern(EBlueprintPatternType InType, const FString& InName, const FString& InDescription, float InComplexity = 1.0f)
		: PatternType(InType)
		, PatternName(InName)
		, Description(InDescription)
		, Complexity(InComplexity)
	{
	}
};

/**
 * Result of pattern detection analysis
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FPatternDetectionResult
{
	GENERATED_BODY()

	/** Primary detected pattern */
	UPROPERTY(BlueprintReadOnly)
	EBlueprintPatternType PrimaryPattern;

	/** Confidence in the primary pattern (0.0 to 1.0) */
	UPROPERTY(BlueprintReadOnly)
	float PrimaryConfidence;

	/** Secondary patterns detected */
	UPROPERTY(BlueprintReadOnly)
	TArray<EBlueprintPatternType> SecondaryPatterns;

	/** Confidence scores for secondary patterns */
	UPROPERTY(BlueprintReadOnly)
	TArray<float> SecondaryConfidences;

	/** Detailed pattern information */
	UPROPERTY(BlueprintReadOnly)
	FBlueprintPattern PatternDetails;

	/** Suggested blueprint structure based on pattern */
	UPROPERTY(BlueprintReadOnly)
	TArray<FString> SuggestedStructure;

	FPatternDetectionResult()
		: PrimaryPattern(EBlueprintPatternType::Unknown)
		, PrimaryConfidence(0.0f)
	{
	}
};

/**
 * Detects blueprint patterns from natural language input
 * Helps determine the most appropriate blueprint structure to generate
 */
class UE5BLUEPRINTGENERATOR_API FBlueprintPatternDetector
{
public:
	FBlueprintPatternDetector();
	~FBlueprintPatternDetector();

	// Pattern detection
	FPatternDetectionResult DetectPattern(const FString& InputText);
	EBlueprintPatternType GetPrimaryPattern(const FString& InputText);
	TArray<EBlueprintPatternType> GetSecondaryPatterns(const FString& InputText);
	float CalculatePatternConfidence(const FString& InputText, EBlueprintPatternType PatternType);

	// Pattern information
	FBlueprintPattern GetPatternDetails(EBlueprintPatternType PatternType) const;
	TArray<FString> GetPatternKeyTerms(EBlueprintPatternType PatternType) const;
	float GetPatternComplexity(EBlueprintPatternType PatternType) const;
	TArray<FString> GetSuggestedStructure(EBlueprintPatternType PatternType) const;

	// Pattern database management
	void LoadPatternDatabase();
	void AddCustomPattern(const FBlueprintPattern& Pattern);
	void RemovePattern(EBlueprintPatternType PatternType);
	TArray<EBlueprintPatternType> GetAllPatternTypes() const;

	// Analysis utilities
	bool IsPatternApplicable(const FString& InputText, EBlueprintPatternType PatternType) const;
	TArray<FString> ExtractPatternIndicators(const FString& InputText) const;
	float CalculateOverallComplexity(const FString& InputText);

	// Configuration
	void SetConfidenceThreshold(float Threshold);
	void SetMaxSecondaryPatterns(int32 MaxPatterns);
	void SetPatternWeights(const TMap<EBlueprintPatternType, float>& Weights);

private:
	// Pattern database
	TArray<FBlueprintPattern> PatternDatabase;
	TMap<EBlueprintPatternType, int32> PatternIndexMap;
	TMap<FString, TArray<EBlueprintPatternType>> KeyTermToPatternMap;

	// Configuration
	float ConfidenceThreshold;
	int32 MaxSecondaryPatterns;
	TMap<EBlueprintPatternType, float> PatternWeights;

	// Detection methods
	int32 CountPatternKeyTerms(const FString& InputText, const FBlueprintPattern& Pattern) const;
	bool ContainsPatternStructure(const FString& InputText, const FBlueprintPattern& Pattern) const;
	float CalculateTermDensity(const FString& InputText, const TArray<FString>& Terms) const;
	float CalculateSemanticSimilarity(const FString& InputText, const FBlueprintPattern& Pattern) const;

	// Pattern initialization
	void InitializeSimpleFunctionPattern();
	void InitializeEventHandlerPattern();
	void InitializeDataProcessorPattern();
	void InitializeStateManagerPattern();
	void InitializeTimerBasedPattern();
	void InitializeInputHandlerPattern();
	void InitializeAnimationControllerPattern();
	void InitializeAIBehaviorPattern();
	void InitializeNetworkReplicationPattern();
	void InitializeCustomEventPattern();
	void InitializeUIControllerPattern();
	void InitializeGameplayMechanicPattern();
	void InitializeUtilityFunctionPattern();

	// Helper methods
	void RebuildIndexMaps();
	TArray<FString> TokenizeInput(const FString& InputText) const;
	bool ContainsAnyTerm(const FString& InputText, const TArray<FString>& Terms) const;
	int32 CountTermOccurrences(const FString& InputText, const TArray<FString>& Terms) const;
	TArray<TPair<EBlueprintPatternType, float>> RankPatterns(const FString& InputText);
	FString NormalizeText(const FString& Text) const;
}; 