#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGeneration/BlueprintAssetCreator.h"
#include "BlueprintGeneration/BlueprintTypeResolver.h"
#include "BlueprintGeneration/NodeFactory.h"
#include "BlueprintGeneration/NodeTypeRegistry.h"
#include "BlueprintGeneration/NodePropertyManager.h"
#include "BlueprintGeneration/NodePositionCalculator.h"
#include "BlueprintGeneration/PinCompatibilityChecker.h"
#include "BlueprintGeneration/ConnectionManager.h"
#include "BlueprintGeneration/GraphValidator.h"
#include "NLP/StructureExtractor.h"
#include "NLP/DependencyAnalyzer.h"
#include "NLP/ComplexityEstimator.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAssemblyCoordinator, Log, All);

/**
 * Assembly phase enumeration for tracking progress
 */
UENUM(BlueprintType)
enum class EAssemblyPhase : uint8
{
    None,
    Initialization,
    BlueprintCreation,
    StructureAnalysis,
    DependencyResolution,
    NodeCreation,
    PropertyConfiguration,
    PositionCalculation,
    ConnectionEstablishment,
    Validation,
    Finalization,
    Complete,
    Failed
};

/**
 * Assembly strategy enumeration for different approaches
 */
UENUM(BlueprintType)
enum class EAssemblyStrategy : uint8
{
    Sequential,      // Create nodes sequentially based on dependency order
    Parallel,        // Create independent nodes in parallel
    Hierarchical,    // Create nodes in hierarchical layers
    Optimized,       // Use best strategy based on complexity analysis
    Custom          // Use custom strategy defined by user
};

/**
 * Assembly priority enumeration for node creation order
 */
UENUM(BlueprintType)
enum class EAssemblyPriority : uint8
{
    Critical,       // Must be created first (entry points, core functionality)
    High,           // Important nodes (main logic flow)
    Normal,         // Standard nodes (regular functionality)
    Low,            // Optional nodes (debug, comments)
    Deferred        // Can be created last (optimization, cleanup)
};

/**
 * Assembly configuration structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYCONFIG
{
    GENERATED_BODY()

    // Assembly strategy to use
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    EAssemblyStrategy Strategy = EAssemblyStrategy::Optimized;

    // Maximum number of parallel operations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly", meta = (ClampMin = "1", ClampMax = "16"))
    int32 MaxParallelOperations = 4;

    // Enable automatic error recovery
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableErrorRecovery = true;

    // Enable performance monitoring
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnablePerformanceMonitoring = true;

    // Enable detailed logging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableDetailedLogging = false;

    // Timeout for assembly operations (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly", meta = (ClampMin = "1", ClampMax = "300"))
    float AssemblyTimeout = 60.0f;

    // Enable automatic validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableAutoValidation = true;

    // Enable automatic optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableAutoOptimization = true;

    FASSEMBLYCONFIG()
    {
        Strategy = EAssemblyStrategy::Optimized;
        MaxParallelOperations = 4;
        bEnableErrorRecovery = true;
        bEnablePerformanceMonitoring = true;
        bEnableDetailedLogging = false;
        AssemblyTimeout = 60.0f;
        bEnableAutoValidation = true;
        bEnableAutoOptimization = true;
    }
};

/**
 * Assembly task structure for tracking individual operations
 */
USTRUCT(BlueprintType)
struct FASSEMBLYTASK
{
    GENERATED_BODY()

    // Unique task identifier
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    FString TaskId;

    // Task description
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    FString Description;

    // Task priority
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    EAssemblyPriority Priority = EAssemblyPriority::Normal;

    // Task dependencies (other task IDs that must complete first)
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    TArray<FString> Dependencies;

    // Estimated completion time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    float EstimatedTime = 1.0f;

    // Task completion status
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    bool bIsCompleted = false;

    // Task failure status
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    bool bHasFailed = false;

    // Error message if task failed
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    FString ErrorMessage;

    // Actual completion time
    UPROPERTY(BlueprintReadOnly, Category = "Task")
    float ActualTime = 0.0f;

    FASSEMBLYTASK()
    {
        Priority = EAssemblyPriority::Normal;
        EstimatedTime = 1.0f;
        bIsCompleted = false;
        bHasFailed = false;
        ActualTime = 0.0f;
    }
};

/**
 * Assembly progress structure for tracking overall progress
 */
USTRUCT(BlueprintType)
struct FASSEMBLYPROGRESS
{
    GENERATED_BODY()

    // Current assembly phase
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    EAssemblyPhase CurrentPhase = EAssemblyPhase::None;

    // Overall progress percentage (0.0 - 1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float OverallProgress = 0.0f;

    // Phase progress percentage (0.0 - 1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float PhaseProgress = 0.0f;

    // Total number of tasks
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    int32 TotalTasks = 0;

    // Number of completed tasks
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    int32 CompletedTasks = 0;

    // Number of failed tasks
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    int32 FailedTasks = 0;

    // Estimated total time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float EstimatedTotalTime = 0.0f;

    // Elapsed time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float ElapsedTime = 0.0f;

    // Estimated remaining time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float EstimatedRemainingTime = 0.0f;

    FASSEMBLYPROGRESS()
    {
        CurrentPhase = EAssemblyPhase::None;
        OverallProgress = 0.0f;
        PhaseProgress = 0.0f;
        TotalTasks = 0;
        CompletedTasks = 0;
        FailedTasks = 0;
        EstimatedTotalTime = 0.0f;
        ElapsedTime = 0.0f;
        EstimatedRemainingTime = 0.0f;
    }
};

/**
 * Assembly request structure containing all information needed for blueprint assembly
 */
USTRUCT(BlueprintType)
struct FASSEMBLYREQUEST
{
    GENERATED_BODY()

    // Blueprint creation request
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FBlueprintCreationRequest BlueprintRequest;

    // Extracted blueprint structure from NLP analysis
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FExtractedBlueprintStructure ExtractedStructure;

    // Dependency analysis results
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FDependencyAnalysisResult DependencyAnalysis;

    // Complexity estimation results
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FComplexityEstimationResult ComplexityEstimation;

    // Assembly configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FAssemblyConfig Config;

    // Additional context information
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    TMap<FString, FString> ContextData;

    FASSEMBLYREQUEST()
    {
        // Initialize with default values
    }
};

/**
 * Assembly result structure containing the results of blueprint assembly
 */
USTRUCT(BlueprintType)
struct FASSEMBLYRESULT
{
    GENERATED_BODY()

    // Success status
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // Created blueprint reference
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TObjectPtr<UBlueprint> CreatedBlueprint = nullptr;

    // Assembly progress information
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FAssemblyProgress Progress;

    // List of all assembly tasks
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FAssemblyTask> Tasks;

    // Error messages
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> ErrorMessages;

    // Warning messages
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> WarningMessages;

    // Performance metrics
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, float> PerformanceMetrics;

    // Validation results
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FGraphValidationResult ValidationResult;

    FASSEMBLYRESULT()
    {
        bSuccess = false;
        CreatedBlueprint = nullptr;
    }
};

/**
 * Assembly statistics structure for performance monitoring
 */
USTRUCT(BlueprintType)
struct FASSEMBLYSTATISTICS
{
    GENERATED_BODY()

    // Total number of assemblies performed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalAssemblies = 0;

    // Number of successful assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulAssemblies = 0;

    // Number of failed assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedAssemblies = 0;

    // Average assembly time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageAssemblyTime = 0.0f;

    // Fastest assembly time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float FastestAssemblyTime = 0.0f;

    // Slowest assembly time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float SlowestAssemblyTime = 0.0f;

    // Most common assembly strategy used
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    EAssemblyStrategy MostUsedStrategy = EAssemblyStrategy::Optimized;

    // Performance metrics by strategy
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EAssemblyStrategy, float> StrategyPerformance;

    FASSEMBLYSTATISTICS()
    {
        TotalAssemblies = 0;
        SuccessfulAssemblies = 0;
        FailedAssemblies = 0;
        AverageAssemblyTime = 0.0f;
        FastestAssemblyTime = 0.0f;
        SlowestAssemblyTime = 0.0f;
        MostUsedStrategy = EAssemblyStrategy::Optimized;
    }
};

/**
 * Delegate declarations for assembly events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyPhaseChanged, EAssemblyPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyProgressUpdated, const FAssemblyProgress&, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyTaskCompleted, const FAssemblyTask&, Task);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyCompleted, const FAssemblyResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssemblyError, const FString&, ErrorMessage, EAssemblyPhase, Phase);

/**
 * Assembly Coordinator - Orchestrates the entire blueprint assembly process
 * 
 * This class coordinates all the components we've built to assemble complete blueprints
 * from natural language descriptions. It manages the assembly pipeline, handles
 * dependencies, monitors progress, and ensures quality through validation.
 */
class UE5BLUEPRINTGENERATOR_API FAssemblyCoordinator
{
public:
    FAssemblyCoordinator();
    virtual ~FAssemblyCoordinator();

    // Singleton access
    static FAssemblyCoordinator& Get();

    // Core assembly operations
    bool AssembleBlueprint(const FAssemblyRequest& Request, FAssemblyResult& OutResult);
    bool IsAssemblyInProgress() const;
    void CancelAssembly();

    // Progress monitoring
    FAssemblyProgress GetCurrentProgress() const;
    TArray<FAssemblyTask> GetCurrentTasks() const;
    EAssemblyPhase GetCurrentPhase() const;

    // Configuration
    void SetAssemblyConfig(const FAssemblyConfig& Config);
    FAssemblyConfig GetAssemblyConfig() const;

    // Statistics
    FAssemblyStatistics GetStatistics() const;
    void ResetStatistics();

    // Event delegates
    FOnAssemblyPhaseChanged OnAssemblyPhaseChanged;
    FOnAssemblyProgressUpdated OnAssemblyProgressUpdated;
    FOnAssemblyTaskCompleted OnAssemblyTaskCompleted;
    FOnAssemblyCompleted OnAssemblyCompleted;
    FOnAssemblyError OnAssemblyError;

private:
    // Component references
    TSharedPtr<FBlueprintAssetCreator> BlueprintAssetCreator;
    TSharedPtr<FBlueprintTypeResolver> TypeResolver;
    TSharedPtr<FNodeFactory> NodeFactory;
    TSharedPtr<FNodeTypeRegistry> NodeTypeRegistry;
    TSharedPtr<FNodePropertyManager> PropertyManager;
    TSharedPtr<FNodePositionCalculator> PositionCalculator;
    TSharedPtr<FPinCompatibilityChecker> CompatibilityChecker;
    TSharedPtr<FConnectionManager> ConnectionManager;
    TSharedPtr<FGraphValidator> GraphValidator;

    // Assembly state
    bool bIsAssemblyInProgress;
    FAssemblyRequest CurrentRequest;
    FAssemblyResult CurrentResult;
    FAssemblyProgress CurrentProgress;
    TArray<FAssemblyTask> CurrentTasks;
    FDateTime AssemblyStartTime;

    // Configuration
    FAssemblyConfig Config;

    // Statistics
    FAssemblyStatistics Statistics;

    // Assembly pipeline methods
    bool InitializeAssembly(const FAssemblyRequest& Request);
    bool CreateBlueprintAsset();
    bool AnalyzeStructure();
    bool ResolveDependencies();
    bool CreateNodes();
    bool ConfigureProperties();
    bool CalculatePositions();
    bool EstablishConnections();
    bool ValidateBlueprint();
    bool FinalizeAssembly();

    // Task management
    void CreateAssemblyTasks();
    void UpdateTaskProgress(const FString& TaskId, bool bCompleted, bool bFailed = false, const FString& ErrorMessage = TEXT(""));
    void UpdateOverallProgress();
    FAssemblyTask* FindTask(const FString& TaskId);

    // Strategy implementation
    bool ExecuteSequentialStrategy();
    bool ExecuteParallelStrategy();
    bool ExecuteHierarchicalStrategy();
    bool ExecuteOptimizedStrategy();
    bool ExecuteCustomStrategy();

    // Error handling and recovery
    bool HandleAssemblyError(const FString& ErrorMessage, EAssemblyPhase Phase);
    bool AttemptErrorRecovery();
    void CleanupFailedAssembly();

    // Performance monitoring
    void StartPerformanceMonitoring();
    void UpdatePerformanceMetrics();
    void StopPerformanceMonitoring();

    // Utility methods
    void LogAssemblyProgress(const FString& Message);
    void BroadcastPhaseChange(EAssemblyPhase NewPhase);
    void BroadcastProgressUpdate();
    void BroadcastTaskCompletion(const FAssemblyTask& Task);
    void BroadcastAssemblyCompletion();
    void BroadcastAssemblyError(const FString& ErrorMessage, EAssemblyPhase Phase);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();

    // Validation helpers
    bool ValidateAssemblyRequest(const FAssemblyRequest& Request);
    bool ValidateAssemblyConfig(const FAssemblyConfig& Config);

    // Statistics helpers
    void UpdateStatistics(const FAssemblyResult& Result);
    void CalculatePerformanceMetrics();
}; 