#include "UI/BlueprintGeneratorUI.h"
#include "Widgets/Layout/SVerticalBox.h"
#include "Widgets/Layout/SHorizontalBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "Widgets/Layout/SSeparator.h"
#include "Widgets/Layout/SBox.h"
#include "EditorStyleSet.h"
#include "Framework/Application/SlateApplication.h"

#define LOCTEXT_NAMESPACE "BlueprintGeneratorUI"

void SBlueprintGeneratorUI::Construct(const FArguments& InArgs)
{
	// Initialize options and default values
	InitializeOptions();
	InitializeDefaultValues();

	// Initialize provider options
	ProviderOptions.Empty();
	TArray<EAIModelProvider> SupportedProviders = FAIModelConnectorFactory::GetSupportedProviders();
	for (EAIModelProvider Provider : SupportedProviders)
	{
		ProviderOptions.Add(MakeShareable(new FString(FAIModelConnectorFactory::GetProviderDisplayName(Provider))));
	}

	// Initialize model options (will be populated when provider is selected)
	ModelOptions.Empty();
	ModelOptions.Add(MakeShareable(new FString(TEXT("Loading..."))));

	// Load settings from persistent configuration
	LoadSettingsFromConfig();

	ChildSlot
	[
		SNew(SVerticalBox)
		
		// Header
		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(10, 10, 10, 5)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("HeaderText", "AI-Powered Blueprint Generator"))
			.Font(FCoreStyle::GetDefaultFontStyle("Bold", 16))
			.Justification(ETextJustify::Center)
		]

		// Input Section
		+ SVerticalBox::Slot()
		.FillHeight(0.6f)
		.Padding(10, 5)
		[
			SNew(SVerticalBox)
			
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, 5)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("InputLabel", "Describe the blueprint functionality you want to create:"))
				.Font(FCoreStyle::GetDefaultFontStyle("Regular", 10))
			]
			
			+ SVerticalBox::Slot()
			.FillHeight(1.0f)
			[
				SAssignNew(InputTextBox, SMultiLineEditableTextBox)
				.Text(FText::FromString(CurrentInputText))
				.OnTextChanged(this, &SBlueprintGeneratorUI::OnInputTextChanged)
				.OnTextCommitted(this, &SBlueprintGeneratorUI::OnInputTextCommitted)
				.AutoWrapText(true)
				.ModiferKeyForNewLine(EModifierKey::Shift)
				.HintText(LOCTEXT("InputHint", "Example: Create a function that calculates damage based on player level and weapon strength..."))
			]
		]

		// Controls Section
		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(10, 5)
		[
			SNew(SHorizontalBox)
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(0, 0, 10, 0)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("ElementTypeLabel", "Blueprint Type:"))
				.VAlign(VAlign_Center)
			]
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(0, 0, 20, 0)
			[
				SAssignNew(ElementTypeComboBox, SComboBox<TSharedPtr<FString>>)
				.OptionsSource(&ElementTypeOptions)
				.OnSelectionChanged(this, &SBlueprintGeneratorUI::OnElementTypeChanged)
				.OnGenerateWidget(this, &SBlueprintGeneratorUI::MakeElementTypeWidget)
				.Content()
				[
					SNew(STextBlock)
					.Text(this, &SBlueprintGeneratorUI::GetElementTypeText)
				]
			]
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(0, 0, 10, 0)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("ProviderLabel", "AI Provider:"))
				.VAlign(VAlign_Center)
			]
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(0, 0, 20, 0)
			[
				SAssignNew(ProviderComboBox, SComboBox<TSharedPtr<FString>>)
				.OptionsSource(&ProviderOptions)
				.OnSelectionChanged(this, &SBlueprintGeneratorUI::OnProviderChanged)
				.OnGenerateWidget(this, &SBlueprintGeneratorUI::MakeProviderWidget)
				.Content()
				[
					SNew(STextBlock)
					.Text(this, &SBlueprintGeneratorUI::GetProviderText)
				]
			]
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(0, 0, 10, 0)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("ModelLabel", "Model:"))
				.VAlign(VAlign_Center)
			]
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			[
				SAssignNew(ModelComboBox, SComboBox<TSharedPtr<FString>>)
				.OptionsSource(&ModelOptions)
				.OnSelectionChanged(this, &SBlueprintGeneratorUI::OnModelChanged)
				.OnGenerateWidget(this, &SBlueprintGeneratorUI::MakeModelWidget)
				.Content()
				[
					SNew(STextBlock)
					.Text(this, &SBlueprintGeneratorUI::GetModelText)
				]
			]
		]

		// Options Section
		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(10, 5)
		[
			SNew(SVerticalBox)
			
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, 5)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("OptionsLabel", "Generation Options:"))
				.Font(FCoreStyle::GetDefaultFontStyle("Regular", 10))
			]
			
			+ SVerticalBox::Slot()
			.AutoHeight()
			[
				SNew(SHorizontalBox)
				
				+ SHorizontalBox::Slot()
				.AutoWidth()
				.Padding(0, 0, 20, 0)
				[
					SAssignNew(GenerateCommentsCheckBox, SCheckBox)
					.IsChecked(this, &SBlueprintGeneratorUI::GetGenerateCommentsState)
					.OnCheckStateChanged(this, &SBlueprintGeneratorUI::OnGenerateCommentsChanged)
					.Content()
					[
						SNew(STextBlock)
						.Text(LOCTEXT("GenerateCommentsOption", "Generate Comments"))
					]
				]
				
				+ SHorizontalBox::Slot()
				.AutoWidth()
				.Padding(0, 0, 20, 0)
				[
					SAssignNew(IncludeErrorHandlingCheckBox, SCheckBox)
					.IsChecked(this, &SBlueprintGeneratorUI::GetIncludeErrorHandlingState)
					.OnCheckStateChanged(this, &SBlueprintGeneratorUI::OnIncludeErrorHandlingChanged)
					.Content()
					[
						SNew(STextBlock)
						.Text(LOCTEXT("ErrorHandlingOption", "Include Error Handling"))
					]
				]
				
				+ SHorizontalBox::Slot()
				.AutoWidth()
				.Padding(0, 0, 20, 0)
				[
					SAssignNew(UseAdvancedLogicCheckBox, SCheckBox)
					.IsChecked(this, &SBlueprintGeneratorUI::GetUseAdvancedLogicState)
					.OnCheckStateChanged(this, &SBlueprintGeneratorUI::OnUseAdvancedLogicChanged)
					.Content()
					[
						SNew(STextBlock)
						.Text(LOCTEXT("AdvancedLogicOption", "Advanced Logic"))
					]
				]
				
				+ SHorizontalBox::Slot()
				.AutoWidth()
				[
					SAssignNew(OptimizePerformanceCheckBox, SCheckBox)
					.IsChecked(this, &SBlueprintGeneratorUI::GetOptimizePerformanceState)
					.OnCheckStateChanged(this, &SBlueprintGeneratorUI::OnOptimizePerformanceChanged)
					.Content()
					[
						SNew(STextBlock)
						.Text(LOCTEXT("PerformanceOption", "Performance Optimization"))
					]
				]
			]
		]

		// Buttons Section
		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(10, 10)
		[
			SNew(SHorizontalBox)
			
			+ SHorizontalBox::Slot()
			.FillWidth(1.0f)
			[
				SAssignNew(GenerateButton, SButton)
				.Text(LOCTEXT("GenerateButton", "Generate Blueprint"))
				.OnClicked(this, &SBlueprintGeneratorUI::OnGenerateButtonClicked)
				.IsEnabled(this, &SBlueprintGeneratorUI::IsGenerateButtonEnabled)
				.HAlign(HAlign_Center)
			]
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(10, 0, 0, 0)
			[
				SAssignNew(ClearButton, SButton)
				.Text(LOCTEXT("ClearButton", "Clear"))
				.OnClicked(this, &SBlueprintGeneratorUI::OnClearButtonClicked)
				.HAlign(HAlign_Center)
			]
			
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(10, 0, 0, 0)
			[
				SAssignNew(SettingsButton, SButton)
				.Text(LOCTEXT("SettingsButton", "Settings..."))
				.OnClicked(this, &SBlueprintGeneratorUI::OnSettingsButtonClicked)
				.HAlign(HAlign_Center)
			]
		]
	];

	// Refresh models list for current provider
	RefreshModelsList();
}

void SBlueprintGeneratorUI::InitializeOptions()
{
	// Initialize element type options
	ElementTypeOptions.Empty();
	ElementTypeOptions.Add(MakeShareable(new FString(TEXT("Function"))));
	ElementTypeOptions.Add(MakeShareable(new FString(TEXT("Event Handler"))));
	ElementTypeOptions.Add(MakeShareable(new FString(TEXT("Variable"))));
	ElementTypeOptions.Add(MakeShareable(new FString(TEXT("Custom Event"))));
	ElementTypeOptions.Add(MakeShareable(new FString(TEXT("State Manager"))));
	ElementTypeOptions.Add(MakeShareable(new FString(TEXT("Data Processor"))));

	// Initialize model options (will be populated based on provider)
	ModelOptions.Empty();
	ModelOptions.Add(MakeShareable(new FString(TEXT("llama3"))));
	ModelOptions.Add(MakeShareable(new FString(TEXT("codellama"))));
	ModelOptions.Add(MakeShareable(new FString(TEXT("mistral"))));
}

void SBlueprintGeneratorUI::InitializeDefaultValues()
{
	CurrentInputText = TEXT("");
	
	// Set default selections
	if (ElementTypeOptions.Num() > 0)
	{
		ElementTypeComboBox->SetSelectedItem(ElementTypeOptions[0]);
	}
	
	if (ProviderOptions.Num() > 0)
	{
		ProviderComboBox->SetSelectedItem(ProviderOptions[0]);
	}
	
	if (ModelOptions.Num() > 0)
	{
		ModelComboBox->SetSelectedItem(ModelOptions[0]);
	}
}

// Event Handlers
void SBlueprintGeneratorUI::OnInputTextChanged(const FText& NewText)
{
	CurrentInputText = NewText.ToString();
	
	// Real-time feedback
	UpdateComplexityIndicator();
	UpdateSuggestedElementType();
}

void SBlueprintGeneratorUI::OnInputTextCommitted(const FText& NewText, ETextCommit::Type CommitType)
{
	CurrentInputText = NewText.ToString();
}

void SBlueprintGeneratorUI::OnElementTypeChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo)
{
	// Handle element type change
}

void SBlueprintGeneratorUI::OnProviderChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo)
{
	if (SelectedItem.IsValid())
	{
		// Find the corresponding provider enum
		TArray<EAIModelProvider> SupportedProviders = FAIModelConnectorFactory::GetSupportedProviders();
		for (EAIModelProvider Provider : SupportedProviders)
		{
			if (FAIModelConnectorFactory::GetProviderDisplayName(Provider) == *NewSelection)
			{
				CurrentProvider = Provider;
				
				// Update AI manager settings
				FAIModelManager& AIManager = FAIModelManager::Get();
				FAIModelSettings CurrentSettings = AIManager.GetCurrentSettings();
				CurrentSettings.Provider = Provider;
				
				// Update default endpoint based on provider
				switch (Provider)
				{
					case EAIModelProvider::Ollama:
						CurrentSettings.Endpoint = TEXT("http://localhost:11434/api/generate");
						CurrentSettings.ModelName = TEXT("llama3");
						break;
					case EAIModelProvider::LMStudio:
						CurrentSettings.Endpoint = TEXT("http://localhost:1234/v1/chat/completions");
						CurrentSettings.ModelName = TEXT("local-model");
						break;
					default:
						break;
				}
				
				AIManager.SetCurrentSettings(CurrentSettings);
				CurrentModelName = CurrentSettings.ModelName;
				
				// Refresh models list
				RefreshModelsList();
				
				UE_LOG(LogBlueprintGenerator, Log, TEXT("Provider changed to: %s"), *NewSelection.Get());
				break;
			}
		}
	}
}

void SBlueprintGeneratorUI::OnModelChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo)
{
	// Handle model change
}

void SBlueprintGeneratorUI::OnGenerateCommentsChanged(ECheckBoxState NewState)
{
	// Handle generate comments option change
}

void SBlueprintGeneratorUI::OnIncludeErrorHandlingChanged(ECheckBoxState NewState)
{
	// Handle error handling option change
}

void SBlueprintGeneratorUI::OnUseAdvancedLogicChanged(ECheckBoxState NewState)
{
	// Handle advanced logic option change
}

void SBlueprintGeneratorUI::OnOptimizePerformanceChanged(ECheckBoxState NewState)
{
	// Handle performance optimization option change
}

FReply SBlueprintGeneratorUI::OnGenerateButtonClicked()
{
	// TODO: Implement blueprint generation
	return FReply::Handled();
}

FReply SBlueprintGeneratorUI::OnClearButtonClicked()
{
	CurrentInputText = TEXT("");
	InputTextBox->SetText(FText::FromString(CurrentInputText));
	return FReply::Handled();
}

FReply SBlueprintGeneratorUI::OnSettingsButtonClicked()
{
	// Save current settings before opening dialog
	SaveSettingsToConfig();
	
	// Open the settings dialog
	SSettingsDialog::OpenSettingsDialog();
	
	return FReply::Handled();
}

// Widget Generation Methods
TSharedRef<SWidget> SBlueprintGeneratorUI::MakeElementTypeWidget(TSharedPtr<FString> InOption)
{
	return SNew(STextBlock).Text(FText::FromString(*InOption));
}

TSharedRef<SWidget> SBlueprintGeneratorUI::MakeProviderWidget(TSharedPtr<FString> InOption)
{
	return SNew(STextBlock).Text(FText::FromString(*InOption));
}

TSharedRef<SWidget> SBlueprintGeneratorUI::MakeModelWidget(TSharedPtr<FString> InOption)
{
	return SNew(STextBlock).Text(FText::FromString(*InOption));
}

// Text Getters
FText SBlueprintGeneratorUI::GetElementTypeText() const
{
	TSharedPtr<FString> SelectedItem = ElementTypeComboBox->GetSelectedItem();
	return SelectedItem.IsValid() ? FText::FromString(*SelectedItem) : LOCTEXT("SelectElementType", "Select Type");
}

FText SBlueprintGeneratorUI::GetProviderText() const
{
	TSharedPtr<FString> SelectedItem = ProviderComboBox->GetSelectedItem();
	return SelectedItem.IsValid() ? FText::FromString(*SelectedItem) : LOCTEXT("SelectProvider", "Select Provider");
}

FText SBlueprintGeneratorUI::GetModelText() const
{
	TSharedPtr<FString> SelectedItem = ModelComboBox->GetSelectedItem();
	return SelectedItem.IsValid() ? FText::FromString(*SelectedItem) : LOCTEXT("SelectModel", "Select Model");
}

// State Getters
bool SBlueprintGeneratorUI::IsGenerateButtonEnabled() const
{
	return !CurrentInputText.IsEmpty() && CurrentInputText.Len() > 10;
}

ECheckBoxState SBlueprintGeneratorUI::GetGenerateCommentsState() const
{
	return ECheckBoxState::Checked; // Default to checked
}

ECheckBoxState SBlueprintGeneratorUI::GetIncludeErrorHandlingState() const
{
	return ECheckBoxState::Checked; // Default to checked
}

ECheckBoxState SBlueprintGeneratorUI::GetUseAdvancedLogicState() const
{
	return ECheckBoxState::Unchecked; // Default to unchecked
}

ECheckBoxState SBlueprintGeneratorUI::GetOptimizePerformanceState() const
{
	return ECheckBoxState::Checked; // Default to checked
}

// Validation
bool SBlueprintGeneratorUI::ValidateInput() const
{
	return !CurrentInputText.IsEmpty() && CurrentInputText.Len() >= 10;
}

FText SBlueprintGeneratorUI::GetInputValidationMessage() const
{
	if (CurrentInputText.IsEmpty())
	{
		return LOCTEXT("EmptyInputError", "Please enter a description of the blueprint you want to create.");
	}
	
	if (CurrentInputText.Len() < 10)
	{
		return LOCTEXT("ShortInputError", "Please provide a more detailed description (at least 10 characters).");
	}
	
	return FText::GetEmpty();
}

// Real-time feedback (placeholder implementations)
void SBlueprintGeneratorUI::UpdateComplexityIndicator()
{
	// TODO: Implement complexity analysis
}

void SBlueprintGeneratorUI::UpdateSuggestedElementType()
{
	// TODO: Implement intent analysis and element type suggestion
}

void SBlueprintGeneratorUI::LoadSettingsFromConfig()
{
	// Get settings from the persistent configuration
	UBlueprintGeneratorSettings* Settings = UBlueprintGeneratorSettings::Get();
	if (Settings)
	{
		// Load AI model settings
		FAIModelSettings AISettings = Settings->ToAIModelSettings();
		FAIModelManager& AIManager = FAIModelManager::Get();
		AIManager.SetCurrentSettings(AISettings);

		// Load generation options
		bGenerateComments = Settings->bGenerateCommentsByDefault;
		bIncludeErrorHandling = Settings->bIncludeErrorHandlingByDefault;
		bUseAdvancedLogic = Settings->bUseAdvancedLogicByDefault;
		bOptimizePerformance = Settings->bOptimizePerformanceByDefault;

		// Set current provider selection
		CurrentProvider = Settings->AIProvider;
		CurrentModelName = Settings->ModelName;

		UE_LOG(LogBlueprintGenerator, Log, TEXT("Loaded settings from config - Provider: %s, Model: %s"), 
			*FAIModelConnectorFactory::GetProviderDisplayName(CurrentProvider), 
			*CurrentModelName);
	}
}

void SBlueprintGeneratorUI::SaveSettingsToConfig()
{
	UBlueprintGeneratorSettings* Settings = UBlueprintGeneratorSettings::Get();
	if (Settings)
	{
		// Save current AI model settings
		FAIModelManager& AIManager = FAIModelManager::Get();
		FAIModelSettings AISettings = AIManager.GetCurrentSettings();
		Settings->FromAIModelSettings(AISettings);

		// Save generation options
		Settings->bGenerateCommentsByDefault = bGenerateComments;
		Settings->bIncludeErrorHandlingByDefault = bIncludeErrorHandling;
		Settings->bUseAdvancedLogicByDefault = bUseAdvancedLogic;
		Settings->bOptimizePerformanceByDefault = bOptimizePerformance;

		UE_LOG(LogBlueprintGenerator, Log, TEXT("Saved settings to config"));
	}
}

void SBlueprintGeneratorUI::RefreshModelsList()
{
	if (bIsRefreshingModels)
	{
		return;
	}

	bIsRefreshingModels = true;
	
	// Clear current models and show loading
	ModelOptions.Empty();
	ModelOptions.Add(MakeShareable(new FString(TEXT("Loading..."))));
	
	if (ModelComboBox.IsValid())
	{
		ModelComboBox->RefreshOptions();
	}

	// Get available models from AI manager
	FAIModelManager& AIManager = FAIModelManager::Get();
	AIManager.GetAvailableModels([this](const TArray<FString>& Models)
	{
		// Update models list on the game thread
		AsyncTask(ENamedThreads::GameThread, [this, Models]()
		{
			ModelOptions.Empty();
			
			if (Models.Num() > 0)
			{
				for (const FString& Model : Models)
				{
					ModelOptions.Add(MakeShareable(new FString(Model)));
				}
			}
			else
			{
				ModelOptions.Add(MakeShareable(new FString(TEXT("No models found"))));
			}
			
			if (ModelComboBox.IsValid())
			{
				ModelComboBox->RefreshOptions();
				
				// Try to select the current model
				for (const auto& Option : ModelOptions)
				{
					if (Option.IsValid() && *Option == CurrentModelName)
					{
						ModelComboBox->SetSelectedItem(Option);
						break;
					}
				}
			}
			
			bIsRefreshingModels = false;
			UE_LOG(LogBlueprintGenerator, Log, TEXT("Models list refreshed: %d models found"), Models.Num());
		});
	});
}

#undef LOCTEXT_NAMESPACE 