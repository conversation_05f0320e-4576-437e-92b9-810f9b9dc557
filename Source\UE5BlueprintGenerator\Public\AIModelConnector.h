#pragma once

#include "CoreMinimal.h"
#include "Http.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "AIModelConnector.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAIModelConnector, Log, All);

/**
 * Enumeration for AI model providers
 */
UENUM(BlueprintType)
enum class EAIModelProvider : uint8
{
	None,
	Ollama,
	LMStudio
};

/**
 * Structure for AI model settings
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FAIModelSettings
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	EAIModelProvider Provider = EAIModelProvider::Ollama;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FString Endpoint = TEXT("http://localhost:11434/api/generate");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FString ModelName = TEXT("llama3");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FString APIKey = TEXT("");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model", meta = (ClampMin = "0.0", ClampMax = "2.0"))
	float Temperature = 0.7f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model", meta = (ClampMin = "1", ClampMax = "8192"))
	int32 MaxTokens = 2048;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float TopP = 0.9f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	int32 ContextWindow = 4096;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	bool bUseStreaming = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	bool bEnableCaching = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FString SystemPrompt = TEXT("You are an expert in Unreal Engine 5 Blueprint creation. Generate detailed, efficient blueprint structures following UE5 best practices.");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	int32 TimeoutSeconds = 30;
};

/**
 * Structure for AI model request
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FAIModelRequest
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FString Prompt;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FAIModelSettings Settings;

	FAIModelRequest()
	{
		Prompt = TEXT("");
	}
};

/**
 * Structure for AI model response
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FAIModelResponse
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FString Response;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	bool bSuccess = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	FString ErrorMessage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	int32 TokensUsed = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Model")
	float ResponseTime = 0.0f;

	FAIModelResponse()
	{
		Response = TEXT("");
		bSuccess = false;
		ErrorMessage = TEXT("");
		TokensUsed = 0;
		ResponseTime = 0.0f;
	}
};

/**
 * Abstract base class for AI model connectors
 */
class UE5BLUEPRINTGENERATOR_API IAIModelConnector
{
public:
	virtual ~IAIModelConnector() = default;

	/**
	 * Send a request to the AI model
	 */
	virtual bool SendRequest(const FAIModelRequest& Request, TFunction<void(const FAIModelResponse&)> OnResponse) = 0;

	/**
	 * Test connection to the AI model
	 */
	virtual bool TestConnection(const FAIModelSettings& Settings, TFunction<void(bool, const FString&)> OnResponse) = 0;

	/**
	 * Get available models for this provider
	 */
	virtual bool GetAvailableModels(const FAIModelSettings& Settings, TFunction<void(const TArray<FString>&)> OnResponse) = 0;

	/**
	 * Cancel any pending requests
	 */
	virtual void CancelPendingRequests() = 0;

	/**
	 * Get the provider type for this connector
	 */
	virtual EAIModelProvider GetProviderType() const = 0;
};

/**
 * Factory class for creating AI model connectors
 */
class UE5BLUEPRINTGENERATOR_API FAIModelConnectorFactory
{
public:
	static TSharedPtr<IAIModelConnector> CreateConnector(EAIModelProvider Provider);
	static TArray<EAIModelProvider> GetSupportedProviders();
	static FString GetProviderDisplayName(EAIModelProvider Provider);
};

/**
 * Manager class for AI model connections
 */
class UE5BLUEPRINTGENERATOR_API FAIModelManager
{
public:
	static FAIModelManager& Get();

	bool Initialize();
	void Shutdown();
	void SetCurrentSettings(const FAIModelSettings& Settings);
	const FAIModelSettings& GetCurrentSettings() const;
	bool SendGenerationRequest(const FAIModelRequest& Request, TFunction<void(const FAIModelResponse&)> OnResponse);
	bool TestCurrentConnection(TFunction<void(bool, const FString&)> OnResponse);
	bool GetAvailableModels(TFunction<void(const TArray<FString>&)> OnResponse);
	void CancelAllRequests();
	bool IsReady() const;

private:
	FAIModelManager() = default;

	FAIModelSettings CurrentSettings;
	TSharedPtr<IAIModelConnector> CurrentConnector;
	bool bIsInitialized = false;

	void UpdateConnector();
}; 