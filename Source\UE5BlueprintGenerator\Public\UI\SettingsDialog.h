#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "AIModelConnector.h"

class SButton;
class SComboBox;
class SEditableTextBox;
class SCheckBox;
class SSpinBox;
class SMultiLineEditableTextBox;
class UBlueprintGeneratorSettings;

/**
 * Settings dialog widget for the Blueprint Generator plugin
 * Provides a comprehensive interface for configuring all plugin settings
 */
class UE5BLUEPRINTGENERATOR_API SSettingsDialog : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SSettingsDialog) {}
	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs);

	// Dialog management
	static TSharedRef<SWindow> CreateSettingsWindow();
	static void OpenSettingsDialog();

private:
	// UI construction methods
	TSharedRef<SWidget> CreateAIModelSection();
	TSharedRef<SWidget> CreateGenerationOptionsSection();
	TSharedRef<SWidget> CreateUIPreferencesSection();
	TSharedRef<SWidget> CreateAdvancedSection();
	TSharedRef<SWidget> CreateButtonsSection();

	// Event handlers
	FReply OnOKClicked();
	FReply OnCancelClicked();
	FReply OnApplyClicked();
	FReply OnResetToDefaultsClicked();
	FReply OnTestConnectionClicked();
	FReply OnRefreshModelsClicked();

	// Provider selection
	void OnProviderSelectionChanged(TSharedPtr<FString> SelectedItem, ESelectInfo::Type SelectInfo);
	TSharedRef<SWidget> MakeProviderWidget(TSharedPtr<FString> InOption);
	FText GetProviderText() const;

	// Model selection
	void OnModelSelectionChanged(TSharedPtr<FString> SelectedItem, ESelectInfo::Type SelectInfo);
	TSharedRef<SWidget> MakeModelWidget(TSharedPtr<FString> InOption);
	FText GetModelText() const;
	void RefreshModelsList();

	// Text input handlers
	void OnEndpointTextChanged(const FText& NewText);
	void OnAPIKeyTextChanged(const FText& NewText);
	void OnModelNameTextChanged(const FText& NewText);
	void OnSystemPromptTextChanged(const FText& NewText);

	// Numeric input handlers
	void OnTemperatureChanged(float NewValue);
	void OnMaxTokensChanged(int32 NewValue);
	void OnTopPChanged(float NewValue);
	void OnContextWindowChanged(int32 NewValue);
	void OnTimeoutChanged(int32 NewValue);
	void OnMaxHistoryEntriesChanged(int32 NewValue);

	// Checkbox handlers
	void OnUseStreamingChanged(ECheckBoxState NewState);
	void OnEnableCachingChanged(ECheckBoxState NewState);
	void OnGenerateCommentsByDefaultChanged(ECheckBoxState NewState);
	void OnIncludeErrorHandlingByDefaultChanged(ECheckBoxState NewState);
	void OnUseAdvancedLogicByDefaultChanged(ECheckBoxState NewState);
	void OnOptimizePerformanceByDefaultChanged(ECheckBoxState NewState);
	void OnRememberWindowSizeChanged(ECheckBoxState NewState);
	void OnAutoSaveInputChanged(ECheckBoxState NewState);
	void OnShowComplexityIndicatorChanged(ECheckBoxState NewState);
	void OnShowRealtimeSuggestionsChanged(ECheckBoxState NewState);
	void OnEnableDebugLoggingChanged(ECheckBoxState NewState);
	void OnAutoRefreshModelsOnStartupChanged(ECheckBoxState NewState);

	// Validation
	bool ValidateCurrentSettings(FString& OutErrorMessage);
	void ShowValidationError(const FString& ErrorMessage);

	// Settings management
	void LoadCurrentSettings();
	void SaveCurrentSettings();
	void ApplySettings();
	bool HasUnsavedChanges() const;

private:
	// UI widgets
	TSharedPtr<SComboBox<TSharedPtr<FString>>> ProviderComboBox;
	TSharedPtr<SComboBox<TSharedPtr<FString>>> ModelComboBox;
	TSharedPtr<SEditableTextBox> EndpointTextBox;
	TSharedPtr<SEditableTextBox> APIKeyTextBox;
	TSharedPtr<SEditableTextBox> ModelNameTextBox;
	TSharedPtr<SMultiLineEditableTextBox> SystemPromptTextBox;
	
	TSharedPtr<SSpinBox<float>> TemperatureSpinBox;
	TSharedPtr<SSpinBox<int32>> MaxTokensSpinBox;
	TSharedPtr<SSpinBox<float>> TopPSpinBox;
	TSharedPtr<SSpinBox<int32>> ContextWindowSpinBox;
	TSharedPtr<SSpinBox<int32>> TimeoutSpinBox;
	TSharedPtr<SSpinBox<int32>> MaxHistoryEntriesSpinBox;

	TSharedPtr<SCheckBox> UseStreamingCheckBox;
	TSharedPtr<SCheckBox> EnableCachingCheckBox;
	TSharedPtr<SCheckBox> GenerateCommentsByDefaultCheckBox;
	TSharedPtr<SCheckBox> IncludeErrorHandlingByDefaultCheckBox;
	TSharedPtr<SCheckBox> UseAdvancedLogicByDefaultCheckBox;
	TSharedPtr<SCheckBox> OptimizePerformanceByDefaultCheckBox;
	TSharedPtr<SCheckBox> RememberWindowSizeCheckBox;
	TSharedPtr<SCheckBox> AutoSaveInputCheckBox;
	TSharedPtr<SCheckBox> ShowComplexityIndicatorCheckBox;
	TSharedPtr<SCheckBox> ShowRealtimeSuggestionsCheckBox;
	TSharedPtr<SCheckBox> EnableDebugLoggingCheckBox;
	TSharedPtr<SCheckBox> AutoRefreshModelsOnStartupCheckBox;

	TSharedPtr<SButton> OKButton;
	TSharedPtr<SButton> CancelButton;
	TSharedPtr<SButton> ApplyButton;
	TSharedPtr<SButton> TestConnectionButton;
	TSharedPtr<SButton> RefreshModelsButton;

	// Data
	TArray<TSharedPtr<FString>> ProviderOptions;
	TArray<TSharedPtr<FString>> ModelOptions;
	
	// Current settings (working copy)
	EAIModelProvider CurrentProvider;
	FString CurrentModelName;
	FString CurrentEndpoint;
	FString CurrentAPIKey;
	FString CurrentSystemPrompt;
	float CurrentTemperature;
	int32 CurrentMaxTokens;
	float CurrentTopP;
	int32 CurrentContextWindow;
	int32 CurrentTimeout;
	bool bCurrentUseStreaming;
	bool bCurrentEnableCaching;
	bool bCurrentGenerateCommentsByDefault;
	bool bCurrentIncludeErrorHandlingByDefault;
	bool bCurrentUseAdvancedLogicByDefault;
	bool bCurrentOptimizePerformanceByDefault;
	bool bCurrentRememberWindowSize;
	bool bCurrentAutoSaveInput;
	bool bCurrentShowComplexityIndicator;
	bool bCurrentShowRealtimeSuggestions;
	bool bCurrentEnableDebugLogging;
	int32 CurrentMaxHistoryEntries;
	bool bCurrentAutoRefreshModelsOnStartup;

	// Original settings (for change detection)
	FAIModelSettings OriginalSettings;
	
	// State
	bool bIsRefreshingModels;
	bool bIsTestingConnection;
}; 