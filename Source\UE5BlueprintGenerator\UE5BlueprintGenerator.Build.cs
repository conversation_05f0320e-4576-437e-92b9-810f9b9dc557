using UnrealBuildTool;

public class UE5BlueprintGenerator : ModuleRules
{
	public UE5BlueprintGenerator(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicIncludePaths.AddRange(
			new string[] {
				// ... add public include paths required here ...
			}
		);
				
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
		);
			
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"UnrealEd",
				"Slate",
				"SlateCore",
				"EditorStyle",
				"ToolMenus",
				"HTTP",
				"Json",
				"JsonUtilities",
				"BlueprintGraph",
				"KismetCompiler",
				"ToolWidgets",
				"PropertyEditor",
				"EditorWidgets",
                "GraphEditor",
                "DeveloperSettings",
                
    
                "GraphEditor"
            }
		);
			
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"BlueprintGraph",
				"KismetCompiler",
                "Kismet",
                "ToolWidgets",
				"PropertyEditor",
				"EditorWidgets",
                "GraphEditor",
                "InputCore",
				"ApplicationCore",
				"DesktopPlatform",
				"Projects",
				"AssetTools",
				"ContentBrowser",
				"WorkspaceMenuStructure",
				"LevelEditor",
				"EditorSubsystem",
				"DeveloperSettings",
                "EditorScriptingUtilities",
     
                "GraphEditor",
                "Kismet",
                "KismetWidgets"
          
            }
		);
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
		);
	}
} 