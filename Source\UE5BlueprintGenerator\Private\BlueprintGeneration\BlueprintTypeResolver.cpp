#include "BlueprintGeneration/BlueprintTypeResolver.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerController.h"
#include "AIController.h"
#include "Components/ActorComponent.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/CameraComponent.h"
#include "Components/LightComponent.h"
#include "Components/AudioComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Components/CapsuleComponent.h"

DEFINE_LOG_CATEGORY(LogBlueprintTypeResolver);

FBlueprintTypeResolver::FBlueprintTypeResolver()
    : VeryLowConfidenceThreshold(0.2f)
    , LowConfidenceThreshold(0.4f)
    , MediumConfidenceThreshold(0.6f)
    , HighConfidenceThreshold(0.8f)
    , bVerboseLogging(false)
    , bTrackStatistics(true)
{
    // Initialize mapping rules
    InitializePatternTypeMapping();
    InitializeIntentTypeMapping();
    InitializeParentClassCompatibility();
    InitializeComponentSupport();

    UE_LOG(LogBlueprintTypeResolver, Log, TEXT("BlueprintTypeResolver initialized"));
}

FBlueprintTypeResolver::~FBlueprintTypeResolver()
{
    UE_LOG(LogBlueprintTypeResolver, Log, TEXT("BlueprintTypeResolver destroyed"));
}

FBLUEPRINTTYPERESOLUTION FBlueprintTypeResolver::ResolveType(
    EBlueprintPatternType PatternType,
    const FIntentAnalysisResult& Intent,
    const FExtractedBlueprintStructure& Structure,
    const TArray<FTechnicalTerm>& TechnicalTerms)
{
    FBLUEPRINTTYPERESOLUTION Resolution;

    // Calculate confidence scores for each blueprint type
    TMap<EBlueprintType, float> TypeConfidences;
    
    for (EBlueprintType Type : GetSupportedBlueprintTypes())
    {
        float PatternConfidence = CalculatePatternConfidence(Type, PatternType, 1.0f);
        float IntentConfidence = CalculateIntentConfidence(Type, Intent);
        float StructureConfidence = CalculateStructureConfidence(Type, Structure);
        float TermConfidence = CalculateTermConfidence(Type, TechnicalTerms);

        // Weighted combination of confidence scores
        float CombinedConfidence = (PatternConfidence * 0.4f) + 
                                 (IntentConfidence * 0.3f) + 
                                 (StructureConfidence * 0.2f) + 
                                 (TermConfidence * 0.1f);

        TypeConfidences.Add(Type, CombinedConfidence);
    }

    // Find the type with highest confidence
    EBlueprintType BestType = EBlueprintType::Actor;
    float BestConfidence = 0.0f;
    
    for (const auto& Pair : TypeConfidences)
    {
        if (Pair.Value > BestConfidence)
        {
            BestType = Pair.Key;
            BestConfidence = Pair.Value;
        }
    }

    // Set up resolution result
    Resolution.ResolvedType = BestType;
    Resolution.ConfidenceScore = BestConfidence;
    Resolution.ConfidenceLevel = ScoreToConfidenceLevel(BestConfidence);

    // Add alternative types
    for (const auto& Pair : TypeConfidences)
    {
        if (Pair.Key != BestType && Pair.Value > 0.3f)
        {
            Resolution.AlternativeTypes.Add(Pair.Key, Pair.Value);
        }
    }

    // Validate compatibility
    FCOMPATIBILITYVALIDATION Compatibility = ValidateCompatibility(BestType, PatternType, Intent, Structure);
    Resolution.bIsPatternCompatible = Compatibility.bIsPatternCompatible;
    Resolution.bIsIntentCompatible = Compatibility.bIsIntentCompatible;

    // Add reasoning
    Resolution.ResolutionReasons.Add(FString::Printf(TEXT("Pattern type: %s"), 
        *UEnum::GetValueAsString(PatternType)));
    Resolution.ResolutionReasons.Add(FString::Printf(TEXT("Primary intent: %s"), 
        *UEnum::GetValueAsString(Intent.PrimaryIntent)));
    Resolution.ResolutionReasons.Add(FString::Printf(TEXT("Confidence score: %.2f"), BestConfidence));

    // Add warnings if confidence is low
    if (BestConfidence < MediumConfidenceThreshold)
    {
        Resolution.ResolutionWarnings.Add(TEXT("Low confidence in type resolution. Consider providing more specific input."));
    }

    // Resolve parent class
    FPARENTCLASSRESOLUTION ParentClassResolution = ResolveParentClass(BestType, Intent, Structure, TechnicalTerms);
    Resolution.ResolvedParentClass = ParentClassResolution.ResolvedClass;

    // Update statistics
    if (bTrackStatistics)
    {
        UpdateResolutionStatistics(BestType, BestConfidence);
    }

    LogTypeResolution(FString::Printf(TEXT("Resolved type: %s with confidence %.2f"), 
        *BlueprintTypeToString(BestType), BestConfidence));

    return Resolution;
}

FBLUEPRINTTYPERESOLUTION FBlueprintTypeResolver::ResolveTypeFromPattern(EBlueprintPatternType PatternType, float PatternConfidence)
{
    FBLUEPRINTTYPERESOLUTION Resolution;

    // Get pattern-specific type mapping
    if (PatternTypeMapping.Contains(PatternType))
    {
        const TMap<EBlueprintType, float>& TypeWeights = PatternTypeMapping[PatternType];
        
        EBlueprintType BestType = EBlueprintType::Actor;
        float BestScore = 0.0f;

        for (const auto& Pair : TypeWeights)
        {
            float Score = Pair.Value * PatternConfidence;
            if (Score > BestScore)
            {
                BestType = Pair.Key;
                BestScore = Score;
            }
        }

        Resolution.ResolvedType = BestType;
        Resolution.ConfidenceScore = BestScore;
        Resolution.ConfidenceLevel = ScoreToConfidenceLevel(BestScore);
        Resolution.bIsPatternCompatible = true;

        // Add alternatives
        for (const auto& Pair : TypeWeights)
        {
            if (Pair.Key != BestType)
            {
                Resolution.AlternativeTypes.Add(Pair.Key, Pair.Value * PatternConfidence);
            }
        }

        Resolution.ResolutionReasons.Add(FString::Printf(TEXT("Based on pattern: %s"), 
            *UEnum::GetValueAsString(PatternType)));
    }

    return Resolution;
}

FPARENTCLASSRESOLUTION FBlueprintTypeResolver::ResolveParentClass(
    EBlueprintType BlueprintType,
    const FIntentAnalysisResult& Intent,
    const FExtractedBlueprintStructure& Structure,
    const TArray<FTechnicalTerm>& TechnicalTerms)
{
    FPARENTCLASSRESOLUTION Resolution;

    // Get compatible parent classes for the blueprint type
    TArray<UClass*> CompatibleClasses = GetCompatibleParentClasses(BlueprintType);
    
    if (CompatibleClasses.Num() == 0)
    {
        Resolution.ResolvedClass = GetDefaultParentClass(BlueprintType);
        Resolution.ConfidenceScore = 1.0f;
        Resolution.bIsTypeCompatible = true;
        Resolution.bSupportsDetectedFeatures = true;
        Resolution.ResolutionReasons.Add(TEXT("Using default parent class"));
        return Resolution;
    }

    // Calculate confidence for each compatible class
    TMap<UClass*, float> ClassConfidences;
    
    for (UClass* Class : CompatibleClasses)
    {
        float ComponentConfidence = CalculateParentClassComponentConfidence(Class, Structure.Components);
        float FunctionalityConfidence = CalculateParentClassFunctionalityConfidence(Class, Structure.Functions, Structure.Events);
        
        float CombinedConfidence = (ComponentConfidence * 0.6f) + (FunctionalityConfidence * 0.4f);
        ClassConfidences.Add(Class, CombinedConfidence);
    }

    // Find best parent class
    UClass* BestClass = GetDefaultParentClass(BlueprintType);
    float BestConfidence = 0.5f; // Default gets medium confidence

    for (const auto& Pair : ClassConfidences)
    {
        if (Pair.Value > BestConfidence)
        {
            BestClass = Pair.Key;
            BestConfidence = Pair.Value;
        }
    }

    Resolution.ResolvedClass = BestClass;
    Resolution.ConfidenceScore = BestConfidence;
    Resolution.bIsTypeCompatible = IsParentClassCompatible(BlueprintType, BestClass);
    Resolution.bSupportsDetectedFeatures = true; // Assume true for now

    // Add alternatives
    for (const auto& Pair : ClassConfidences)
    {
        if (Pair.Key != BestClass && Pair.Value > 0.3f)
        {
            Resolution.AlternativeClasses.Add(Pair.Key, Pair.Value);
        }
    }

    Resolution.ResolutionReasons.Add(FString::Printf(TEXT("Selected based on component and functionality analysis")));

    return Resolution;
}

bool FBlueprintTypeResolver::IsPatternCompatible(EBlueprintType BlueprintType, EBlueprintPatternType PatternType)
{
    if (!PatternTypeMapping.Contains(PatternType))
    {
        return false;
    }

    const TMap<EBlueprintType, float>& TypeWeights = PatternTypeMapping[PatternType];
    return TypeWeights.Contains(BlueprintType) && TypeWeights[BlueprintType] > 0.1f;
}

void FBlueprintTypeResolver::InitializePatternTypeMapping()
{
    // SimpleFunction pattern
    PatternTypeMapping.Add(EBlueprintPatternType::SimpleFunction, {
        { EBlueprintType::FunctionLibrary, 0.9f },
        { EBlueprintType::Actor, 0.6f },
        { EBlueprintType::Component, 0.5f }
    });

    // EventHandler pattern
    PatternTypeMapping.Add(EBlueprintPatternType::EventHandler, {
        { EBlueprintType::Actor, 0.9f },
        { EBlueprintType::Component, 0.7f },
        { EBlueprintType::GameMode, 0.6f }
    });

    // StateManager pattern
    PatternTypeMapping.Add(EBlueprintPatternType::StateManager, {
        { EBlueprintType::Actor, 0.8f },
        { EBlueprintType::GameMode, 0.7f },
        { EBlueprintType::PlayerController, 0.6f }
    });

    // InputHandler pattern
    PatternTypeMapping.Add(EBlueprintPatternType::InputHandler, {
        { EBlueprintType::PlayerController, 0.9f },
        { EBlueprintType::Actor, 0.6f }
    });

    // AIBehavior pattern
    PatternTypeMapping.Add(EBlueprintPatternType::AIBehavior, {
        { EBlueprintType::AIController, 0.9f },
        { EBlueprintType::Actor, 0.7f }
    });

    // Add more pattern mappings...
}

void FBlueprintTypeResolver::InitializeIntentTypeMapping()
{
    // Create intent
    IntentTypeMapping.Add(EUserIntent::Create, {
        { EBlueprintType::Actor, 0.8f },
        { EBlueprintType::Component, 0.7f },
        { EBlueprintType::FunctionLibrary, 0.6f }
    });

    // Modify intent
    IntentTypeMapping.Add(EUserIntent::Modify, {
        { EBlueprintType::Actor, 0.7f },
        { EBlueprintType::Component, 0.6f }
    });

    // Add more intent mappings...
}

void FBlueprintTypeResolver::InitializeParentClassCompatibility()
{
    // Actor blueprint compatible classes
    ParentClassCompatibility.Add(EBlueprintType::Actor, {
        AActor::StaticClass(),
        APawn::StaticClass(),
        ACharacter::StaticClass()
    });

    // Component blueprint compatible classes
    ParentClassCompatibility.Add(EBlueprintType::Component, {
        UActorComponent::StaticClass(),
        USceneComponent::StaticClass(),
        UStaticMeshComponent::StaticClass(),
        USkeletalMeshComponent::StaticClass()
    });

    // GameMode blueprint compatible classes
    ParentClassCompatibility.Add(EBlueprintType::GameMode, {
        AGameModeBase::StaticClass()
    });

    // PlayerController blueprint compatible classes
    ParentClassCompatibility.Add(EBlueprintType::PlayerController, {
        APlayerController::StaticClass()
    });

    // AIController blueprint compatible classes
    ParentClassCompatibility.Add(EBlueprintType::AIController, {
        AAIController::StaticClass()
    });
}

void FBlueprintTypeResolver::InitializeComponentSupport()
{
    // Actor blueprints support all component types
    ComponentSupport.Add(EBlueprintType::Actor, {
        TEXT("StaticMeshComponent"),
        TEXT("SkeletalMeshComponent"),
        TEXT("CameraComponent"),
        TEXT("LightComponent"),
        TEXT("AudioComponent"),
        TEXT("BoxComponent"),
        TEXT("SphereComponent"),
        TEXT("CapsuleComponent")
    });

    // Component blueprints have limited component support
    ComponentSupport.Add(EBlueprintType::Component, {
        TEXT("StaticMeshComponent"),
        TEXT("SkeletalMeshComponent")
    });
}

float FBlueprintTypeResolver::CalculatePatternConfidence(EBlueprintType BlueprintType, EBlueprintPatternType PatternType, float PatternConfidence)
{
    if (!PatternTypeMapping.Contains(PatternType))
    {
        return 0.0f;
    }

    const TMap<EBlueprintType, float>& TypeWeights = PatternTypeMapping[PatternType];
    if (!TypeWeights.Contains(BlueprintType))
    {
        return 0.0f;
    }

    return TypeWeights[BlueprintType] * PatternConfidence;
}

float FBlueprintTypeResolver::CalculateIntentConfidence(EBlueprintType BlueprintType, const FIntentAnalysisResult& Intent)
{
    float Confidence = 0.0f;

    // Check primary intent
    if (IntentTypeMapping.Contains(Intent.PrimaryIntent))
    {
        const TMap<EBlueprintType, float>& TypeWeights = IntentTypeMapping[Intent.PrimaryIntent];
        if (TypeWeights.Contains(BlueprintType))
        {
            Confidence += TypeWeights[BlueprintType] * Intent.Confidence * 0.7f;
        }
    }

    // Check secondary intents
    for (EUserIntent SecondaryIntent : Intent.SecondaryIntents)
    {
        if (IntentTypeMapping.Contains(SecondaryIntent))
        {
            const TMap<EBlueprintType, float>& TypeWeights = IntentTypeMapping[SecondaryIntent];
            if (TypeWeights.Contains(BlueprintType))
            {
                Confidence += TypeWeights[BlueprintType] * 0.3f;
            }
        }
    }

    return FMath::Clamp(Confidence, 0.0f, 1.0f);
}

ETypeResolutionConfidence FBlueprintTypeResolver::ScoreToConfidenceLevel(float ConfidenceScore)
{
    if (ConfidenceScore >= HighConfidenceThreshold)
        return ETypeResolutionConfidence::VeryHigh;
    else if (ConfidenceScore >= MediumConfidenceThreshold)
        return ETypeResolutionConfidence::High;
    else if (ConfidenceScore >= LowConfidenceThreshold)
        return ETypeResolutionConfidence::Medium;
    else if (ConfidenceScore >= VeryLowConfidenceThreshold)
        return ETypeResolutionConfidence::Low;
    else
        return ETypeResolutionConfidence::VeryLow;
}

TArray<EBlueprintType> FBlueprintTypeResolver::GetSupportedBlueprintTypes()
{
    return {
        EBlueprintType::Actor,
        EBlueprintType::Component,
        EBlueprintType::FunctionLibrary,
        EBlueprintType::Interface,
        EBlueprintType::GameMode,
        EBlueprintType::PlayerController,
        EBlueprintType::AIController
    };
}

UClass* FBlueprintTypeResolver::GetDefaultParentClass(EBlueprintType BlueprintType)
{
    switch (BlueprintType)
    {
        case EBlueprintType::Actor:
            return AActor::StaticClass();
        case EBlueprintType::Component:
            return UActorComponent::StaticClass();
        case EBlueprintType::GameMode:
            return AGameModeBase::StaticClass();
        case EBlueprintType::PlayerController:
            return APlayerController::StaticClass();
        case EBlueprintType::AIController:
            return AAIController::StaticClass();
        default:
            return AActor::StaticClass();
    }
}

TArray<UClass*> FBlueprintTypeResolver::GetCompatibleParentClasses(EBlueprintType BlueprintType)
{
    if (ParentClassCompatibility.Contains(BlueprintType))
    {
        return ParentClassCompatibility[BlueprintType];
    }
    
    return { GetDefaultParentClass(BlueprintType) };
}

void FBlueprintTypeResolver::LogTypeResolution(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogBlueprintTypeResolver, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogBlueprintTypeResolver, Log, TEXT("%s"), *Message);
    }
}

void FBlueprintTypeResolver::UpdateResolutionStatistics(EBlueprintType ResolvedType, float ConfidenceScore)
{
    // Update resolution counts
    if (TypeResolutionCounts.Contains(ResolvedType))
    {
        TypeResolutionCounts[ResolvedType]++;
    }
    else
    {
        TypeResolutionCounts.Add(ResolvedType, 1);
    }

    // Update average confidence scores
    if (AverageConfidenceScores.Contains(ResolvedType))
    {
        float CurrentAverage = AverageConfidenceScores[ResolvedType];
        int32 Count = TypeResolutionCounts[ResolvedType];
        float NewAverage = ((CurrentAverage * (Count - 1)) + ConfidenceScore) / Count;
        AverageConfidenceScores[ResolvedType] = NewAverage;
    }
    else
    {
        AverageConfidenceScores.Add(ResolvedType, ConfidenceScore);
    }
}