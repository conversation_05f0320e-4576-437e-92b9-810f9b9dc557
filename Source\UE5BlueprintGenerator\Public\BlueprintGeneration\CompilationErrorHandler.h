#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGeneration/CompilationManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogCompilationErrorHandler, Log, All);

/**
 * Error classification enumeration
 */
UENUM(BlueprintType)
enum class ECompilationErrorClass : uint8
{
    Unknown,                // Unknown error type
    Syntax,                 // Syntax errors
    Semantic,               // Semantic errors
    Type,                   // Type mismatch errors
    Connection,             // Connection errors
    Property,               // Property configuration errors
    Dependency,             // Dependency errors
    Performance,            // Performance-related errors
    Validation,             // Validation errors
    Runtime,                // Runtime errors
    Memory,                 // Memory-related errors
    Network,                // Network/replication errors
    Custom                  // Custom error types
};

/**
 * Error recovery strategy enumeration
 */
UENUM(BlueprintType)
enum class EErrorRecoveryStrategy : uint8
{
    None,                   // No recovery strategy
    AutoFix,                // Automatic fix attempt
    UserPrompt,             // Prompt user for action
    Fallback,               // Use fallback implementation
    Skip,                   // Skip problematic element
    Retry,                  // Retry operation
    Rollback,               // Rollback to previous state
    Alternative,            // Use alternative approach
    Custom                  // Custom recovery strategy
};

/**
 * Error handling mode enumeration
 */
UENUM(BlueprintType)
enum class EErrorHandlingMode : uint8
{
    Strict,                 // Strict error handling (fail on any error)
    Tolerant,               // Tolerant error handling (continue with warnings)
    Aggressive,             // Aggressive auto-fixing
    Interactive,            // Interactive error resolution
    Silent,                 // Silent error handling
    Custom                  // Custom error handling mode
};

/**
 * Error context structure
 */
USTRUCT(BlueprintType)
struct FERRORCONTEXT
{
    GENERATED_BODY()

    // Source blueprint
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UBlueprint> Blueprint = nullptr;

    // Source graph
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UEdGraph> Graph = nullptr;

    // Source node
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UK2Node> Node = nullptr;

    // Compilation phase
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CompilationPhase;

    // Error location
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString ErrorLocation;

    // Context data
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, FString> ContextData;

    // Stack trace
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TArray<FString> StackTrace;

    FERRORCONTEXT()
    {
        CompilationPhase = TEXT("Unknown");
        ErrorLocation = TEXT("Unknown");
    }
};

/**
 * Error analysis result structure
 */
USTRUCT(BlueprintType)
struct FERRORANALYSISRESULT
{
    GENERATED_BODY()

    // Error classification
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    ECompilationErrorClass ErrorClass = ECompilationErrorClass::Unknown;

    // Error confidence (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float ClassificationConfidence = 0.0f;

    // Root cause analysis
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    FString RootCause;

    // Contributing factors
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> ContributingFactors;

    // Recommended recovery strategy
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    EErrorRecoveryStrategy RecommendedStrategy = EErrorRecoveryStrategy::None;

    // Alternative strategies
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<EErrorRecoveryStrategy> AlternativeStrategies;

    // Auto-fix feasibility
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    bool bCanAutoFix = false;

    // Auto-fix confidence (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float AutoFixConfidence = 0.0f;

    // Suggested fixes
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> SuggestedFixes;

    // Prevention suggestions
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> PreventionSuggestions;

    FERRORANALYSISRESULT()
    {
        ErrorClass = ECompilationErrorClass::Unknown;
        ClassificationConfidence = 0.0f;
        RecommendedStrategy = EErrorRecoveryStrategy::None;
        bCanAutoFix = false;
        AutoFixConfidence = 0.0f;
    }
};

/**
 * Error recovery result structure
 */
USTRUCT(BlueprintType)
struct FERRORRECOVERYRESULT
{
    GENERATED_BODY()

    // Recovery success
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    bool bRecoverySuccessful = false;

    // Strategy used
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    EErrorRecoveryStrategy StrategyUsed = EErrorRecoveryStrategy::None;

    // Recovery description
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    FString RecoveryDescription;

    // Recovery time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    float RecoveryTime = 0.0f;

    // Changes made
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    TArray<FString> ChangesMade;

    // Side effects
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    TArray<FString> SideEffects;

    // Rollback information
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    FString RollbackInfo;

    // Recovery confidence (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    float RecoveryConfidence = 0.0f;

    FERRORRECOVERYRESULT()
    {
        bRecoverySuccessful = false;
        StrategyUsed = EErrorRecoveryStrategy::None;
        RecoveryTime = 0.0f;
        RecoveryConfidence = 0.0f;
    }
};

/**
 * Error handling configuration structure
 */
USTRUCT(BlueprintType)
struct FERRORHANDLINGCONFIG
{
    GENERATED_BODY()

    // Error handling mode
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    EErrorHandlingMode HandlingMode = EErrorHandlingMode::Tolerant;

    // Enable auto-fix
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableAutoFix = true;

    // Enable error recovery
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorRecovery = true;

    // Enable error analysis
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorAnalysis = true;

    // Enable error logging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorLogging = true;

    // Enable error statistics
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorStatistics = true;

    // Maximum auto-fix attempts
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto-Fix", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MaxAutoFixAttempts = 3;

    // Auto-fix confidence threshold (0.0-1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto-Fix", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AutoFixConfidenceThreshold = 0.7f;

    // Maximum recovery attempts
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MaxRecoveryAttempts = 5;

    // Recovery timeout (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery", meta = (ClampMin = "1", ClampMax = "300"))
    float RecoveryTimeout = 30.0f;

    // Error severity threshold
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    ECompilationErrorSeverity SeverityThreshold = ECompilationErrorSeverity::Warning;

    // Ignored error classes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    TArray<ECompilationErrorClass> IgnoredErrorClasses;

    // Custom error patterns
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
    TMap<FString, FString> CustomErrorPatterns;

    // Custom recovery strategies
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
    TMap<FString, FString> CustomRecoveryStrategies;

    FERRORHANDLINGCONFIG()
    {
        HandlingMode = EErrorHandlingMode::Tolerant;
        bEnableAutoFix = true;
        bEnableErrorRecovery = true;
        bEnableErrorAnalysis = true;
        bEnableErrorLogging = true;
        bEnableErrorStatistics = true;
        MaxAutoFixAttempts = 3;
        AutoFixConfidenceThreshold = 0.7f;
        MaxRecoveryAttempts = 5;
        RecoveryTimeout = 30.0f;
        SeverityThreshold = ECompilationErrorSeverity::Warning;
    }
};

/**
 * Error handling statistics structure
 */
USTRUCT(BlueprintType)
struct FERRORHANDLINGSTATISTICS
{
    GENERATED_BODY()

    // Total errors handled
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalErrorsHandled = 0;

    // Errors by class
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<ECompilationErrorClass, int32> ErrorsByClass;

    // Errors by severity
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<ECompilationErrorSeverity, int32> ErrorsBySeverity;

    // Auto-fixes attempted
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 AutoFixesAttempted = 0;

    // Auto-fixes successful
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 AutoFixesSuccessful = 0;

    // Recovery attempts
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 RecoveryAttempts = 0;

    // Recovery successes
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 RecoverySuccesses = 0;

    // Average recovery time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageRecoveryTime = 0.0f;

    // Strategy usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EErrorRecoveryStrategy, int32> StrategyUsage;

    // Most common errors
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<FString, int32> MostCommonErrors;

    FERRORHANDLINGSTATISTICS()
    {
        TotalErrorsHandled = 0;
        AutoFixesAttempted = 0;
        AutoFixesSuccessful = 0;
        RecoveryAttempts = 0;
        RecoverySuccesses = 0;
        AverageRecoveryTime = 0.0f;
    }
};

/**
 * Delegate declarations for error handling events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnErrorDetected, const FCOMPILATIONERROR&, Error, const FERRORCONTEXT&, Context);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnErrorAnalyzed, const FCOMPILATIONERROR&, Error, const FERRORANALYSISRESULT&, Analysis);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAutoFixAttempted, const FCOMPILATIONERROR&, Error, const FString&, FixDescription);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAutoFixCompleted, const FCOMPILATIONERROR&, Error, const FERRORRECOVERYRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRecoveryAttempted, const FCOMPILATIONERROR&, Error, EErrorRecoveryStrategy, Strategy);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRecoveryCompleted, const FCOMPILATIONERROR&, Error, const FERRORRECOVERYRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnErrorResolved, const FCOMPILATIONERROR&, Error);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnErrorUnresolved, const FCOMPILATIONERROR&, Error);

/**
 * Compilation Error Handler - Specialized error handling and recovery system
 * 
 * This class provides comprehensive error handling capabilities for blueprint
 * compilation, including error classification, analysis, auto-fixing, and
 * recovery strategies. It ensures robust compilation with intelligent error
 * resolution and detailed error reporting.
 */
class UE5BLUEPRINTGENERATOR_API FCompilationErrorHandler
{
public:
    FCompilationErrorHandler();
    virtual ~FCompilationErrorHandler();

    // Core error handling operations
    bool HandleError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool HandleErrors(const TArray<FCOMPILATIONERROR>& Errors, const FERRORCONTEXT& Context, TArray<FERRORRECOVERYRESULT>& OutResults);

    // Error analysis
    FERRORANALYSISRESULT AnalyzeError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    ECompilationErrorClass ClassifyError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    float CalculateClassificationConfidence(const FCOMPILATIONERROR& Error, ECompilationErrorClass ErrorClass);

    // Auto-fix operations
    bool CanAutoFix(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    bool AttemptAutoFix(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    TArray<FString> GetAutoFixSuggestions(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);

    // Recovery operations
    bool AttemptRecovery(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, EErrorRecoveryStrategy Strategy, FERRORRECOVERYRESULT& OutResult);
    EErrorRecoveryStrategy SelectRecoveryStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    TArray<EErrorRecoveryStrategy> GetAvailableStrategies(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);

    // Error prevention
    TArray<FString> GetPreventionSuggestions(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    bool CanPreventError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);

    // Configuration management
    void SetErrorHandlingConfig(const FERRORHANDLINGCONFIG& Config);
    FERRORHANDLINGCONFIG GetErrorHandlingConfig() const;

    // Statistics and monitoring
    FERRORHANDLINGSTATISTICS GetStatistics() const;
    void ResetStatistics();

    // Error pattern management
    void AddCustomErrorPattern(const FString& Pattern, const FString& Description);
    void RemoveCustomErrorPattern(const FString& Pattern);
    bool MatchesCustomPattern(const FCOMPILATIONERROR& Error, FString& OutPattern);

    // Recovery strategy management
    void RegisterCustomRecoveryStrategy(const FString& StrategyName, TFunction<bool(const FCOMPILATIONERROR&, const FERRORCONTEXT&, FERRORRECOVERYRESULT&)> StrategyFunction);
    void UnregisterCustomRecoveryStrategy(const FString& StrategyName);

    // Event delegates
    FOnErrorDetected OnErrorDetected;
    FOnErrorAnalyzed OnErrorAnalyzed;
    FOnAutoFixAttempted OnAutoFixAttempted;
    FOnAutoFixCompleted OnAutoFixCompleted;
    FOnRecoveryAttempted OnRecoveryAttempted;
    FOnRecoveryCompleted OnRecoveryCompleted;
    FOnErrorResolved OnErrorResolved;
    FOnErrorUnresolved OnErrorUnresolved;

private:
    // Configuration
    FERRORHANDLINGCONFIG Config;

    // Statistics
    FERRORHANDLINGSTATISTICS Statistics;

    // Error classification patterns
    TMap<ECompilationErrorClass, TArray<FString>> ClassificationPatterns;

    // Auto-fix handlers
    TMap<ECompilationErrorClass, TFunction<bool(const FCOMPILATIONERROR&, const FERRORCONTEXT&, FERRORRECOVERYRESULT&)>> AutoFixHandlers;

    // Recovery strategy handlers
    TMap<EErrorRecoveryStrategy, TFunction<bool(const FCOMPILATIONERROR&, const FERRORCONTEXT&, FERRORRECOVERYRESULT&)>> RecoveryHandlers;

    // Custom recovery strategies
    TMap<FString, TFunction<bool(const FCOMPILATIONERROR&, const FERRORCONTEXT&, FERRORRECOVERYRESULT&)>> CustomRecoveryStrategies;

    // Error analysis methods
    FString AnalyzeRootCause(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    TArray<FString> IdentifyContributingFactors(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    float CalculateAutoFixConfidence(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);

    // Classification methods
    bool MatchesClassificationPattern(const FCOMPILATIONERROR& Error, ECompilationErrorClass ErrorClass);
    ECompilationErrorClass ClassifyBySyntax(const FCOMPILATIONERROR& Error);
    ECompilationErrorClass ClassifyBySemantic(const FCOMPILATIONERROR& Error);
    ECompilationErrorClass ClassifyByContext(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);

    // Auto-fix implementation methods
    bool AutoFixSyntaxError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool AutoFixSemanticError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool AutoFixTypeError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool AutoFixConnectionError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool AutoFixPropertyError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool AutoFixDependencyError(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);

    // Recovery strategy implementation methods
    bool ExecuteAutoFixStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool ExecuteUserPromptStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool ExecuteFallbackStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool ExecuteSkipStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool ExecuteRetryStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool ExecuteRollbackStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);
    bool ExecuteAlternativeStrategy(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context, FERRORRECOVERYRESULT& OutResult);

    // Utility methods
    FERRORCONTEXT CreateErrorContext(UBlueprint* Blueprint, UEdGraph* Graph, UK2Node* Node, const FString& Phase);
    bool ShouldIgnoreError(const FCOMPILATIONERROR& Error);
    bool IsErrorRecoverable(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    void UpdateStatistics(const FCOMPILATIONERROR& Error, const FERRORRECOVERYRESULT& Result);

    // Event broadcasting
    void BroadcastErrorDetected(const FCOMPILATIONERROR& Error, const FERRORCONTEXT& Context);
    void BroadcastErrorAnalyzed(const FCOMPILATIONERROR& Error, const FERRORANALYSISRESULT& Analysis);
    void BroadcastAutoFixAttempted(const FCOMPILATIONERROR& Error, const FString& FixDescription);
    void BroadcastAutoFixCompleted(const FCOMPILATIONERROR& Error, const FERRORRECOVERYRESULT& Result);
    void BroadcastRecoveryAttempted(const FCOMPILATIONERROR& Error, EErrorRecoveryStrategy Strategy);
    void BroadcastRecoveryCompleted(const FCOMPILATIONERROR& Error, const FERRORRECOVERYRESULT& Result);
    void BroadcastErrorResolved(const FCOMPILATIONERROR& Error);
    void BroadcastErrorUnresolved(const FCOMPILATIONERROR& Error);

    // Component initialization
    void InitializeComponents();
    void InitializeClassificationPatterns();
    void InitializeAutoFixHandlers();
    void InitializeRecoveryHandlers();
    void ShutdownComponents();
}; 