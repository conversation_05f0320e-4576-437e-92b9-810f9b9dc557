#include "NLP/BlueprintPatternDetector.h"
#include "UE5BlueprintGeneratorModule.h"
#include "Misc/Char.h"
#include "Misc/CString.h"

DEFINE_LOG_CATEGORY_EXTERN(LogBlueprintPatternDetector, Log, All);
DEFINE_LOG_CATEGORY(LogBlueprintPatternDetector);

FBlueprintPatternDetector::FBlueprintPatternDetector()
	: ConfidenceThreshold(0.3f)
	, MaxSecondaryPatterns(3)
{
	LoadPatternDatabase();
	UE_LOG(LogBlueprintPatternDetector, Log, TEXT("Blueprint pattern detector initialized with %d patterns"), PatternDatabase.Num());
}

FBlueprintPatternDetector::~FBlueprintPatternDetector()
{
	UE_LOG(LogBlueprintPatternDetector, Log, TEXT("Blueprint pattern detector destroyed"));
}

FPatternDetectionResult FBlueprintPatternDetector::DetectPattern(const FString& InputText)
{
	FPatternDetectionResult Result;
	
	if (InputText.IsEmpty())
	{
		return Result;
	}
	
	// Rank all patterns by confidence
	TArray<TPair<EBlueprintPatternType, float>> RankedPatterns = RankPatterns(InputText);
	
	if (RankedPatterns.Num() > 0)
	{
		// Set primary pattern
		Result.PrimaryPattern = RankedPatterns[0].Key;
		Result.PrimaryConfidence = RankedPatterns[0].Value;
		Result.PatternDetails = GetPatternDetails(Result.PrimaryPattern);
		
		// Set secondary patterns
		for (int32 i = 1; i < FMath::Min(RankedPatterns.Num(), MaxSecondaryPatterns + 1); i++)
		{
			if (RankedPatterns[i].Value >= ConfidenceThreshold)
			{
				Result.SecondaryPatterns.Add(RankedPatterns[i].Key);
				Result.SecondaryConfidences.Add(RankedPatterns[i].Value);
			}
		}
		
		// Generate suggested structure
		Result.SuggestedStructure = GetSuggestedStructure(Result.PrimaryPattern);
	}
	
	UE_LOG(LogBlueprintPatternDetector, VeryVerbose, TEXT("Detected pattern: %s (confidence: %.2f)"), 
		*Result.PatternDetails.PatternName, Result.PrimaryConfidence);
	
	return Result;
}

EBlueprintPatternType FBlueprintPatternDetector::GetPrimaryPattern(const FString& InputText)
{
	FPatternDetectionResult Result = DetectPattern(InputText);
	return Result.PrimaryPattern;
}

TArray<EBlueprintPatternType> FBlueprintPatternDetector::GetSecondaryPatterns(const FString& InputText)
{
	FPatternDetectionResult Result = DetectPattern(InputText);
	return Result.SecondaryPatterns;
}

float FBlueprintPatternDetector::CalculatePatternConfidence(const FString& InputText, EBlueprintPatternType PatternType)
{
	if (InputText.IsEmpty())
	{
		return 0.0f;
	}
	
	FBlueprintPattern Pattern = GetPatternDetails(PatternType);
	if (Pattern.PatternType == EBlueprintPatternType::Unknown)
	{
		return 0.0f;
	}
	
	float Confidence = 0.0f;
	
	// Factor 1: Key term matching (40% weight)
	int32 TermMatches = CountPatternKeyTerms(InputText, Pattern);
	float TermFactor = Pattern.KeyTerms.Num() > 0 ? (float)TermMatches / Pattern.KeyTerms.Num() : 0.0f;
	Confidence += TermFactor * 0.4f;
	
	// Factor 2: Structural indicators (30% weight)
	bool HasStructure = ContainsPatternStructure(InputText, Pattern);
	float StructureFactor = HasStructure ? 1.0f : 0.0f;
	Confidence += StructureFactor * 0.3f;
	
	// Factor 3: Term density (20% weight)
	float DensityFactor = CalculateTermDensity(InputText, Pattern.KeyTerms);
	Confidence += DensityFactor * 0.2f;
	
	// Factor 4: Semantic similarity (10% weight)
	float SemanticFactor = CalculateSemanticSimilarity(InputText, Pattern);
	Confidence += SemanticFactor * 0.1f;
	
	// Apply pattern weight
	if (PatternWeights.Contains(PatternType))
	{
		Confidence *= PatternWeights[PatternType];
	}
	
	return FMath::Clamp(Confidence, 0.0f, 1.0f);
}

FBlueprintPattern FBlueprintPatternDetector::GetPatternDetails(EBlueprintPatternType PatternType) const
{
	if (const int32* IndexPtr = PatternIndexMap.Find(PatternType))
	{
		int32 Index = *IndexPtr;
		if (Index < PatternDatabase.Num())
		{
			return PatternDatabase[Index];
		}
	}
	
	return FBlueprintPattern(); // Return empty pattern if not found
}

TArray<FString> FBlueprintPatternDetector::GetPatternKeyTerms(EBlueprintPatternType PatternType) const
{
	FBlueprintPattern Pattern = GetPatternDetails(PatternType);
	return Pattern.KeyTerms;
}

float FBlueprintPatternDetector::GetPatternComplexity(EBlueprintPatternType PatternType) const
{
	FBlueprintPattern Pattern = GetPatternDetails(PatternType);
	return Pattern.Complexity;
}

TArray<FString> FBlueprintPatternDetector::GetSuggestedStructure(EBlueprintPatternType PatternType) const
{
	TArray<FString> Structure;
	
	switch (PatternType)
	{
		case EBlueprintPatternType::SimpleFunction:
			Structure.Add(TEXT("Function with input parameters"));
			Structure.Add(TEXT("Processing logic"));
			Structure.Add(TEXT("Return value"));
			break;
			
		case EBlueprintPatternType::EventHandler:
			Structure.Add(TEXT("Event node (Begin Play, Overlap, etc.)"));
			Structure.Add(TEXT("Conditional logic (Branch)"));
			Structure.Add(TEXT("Action execution"));
			Structure.Add(TEXT("Optional cleanup"));
			break;
			
		case EBlueprintPatternType::DataProcessor:
			Structure.Add(TEXT("Input data validation"));
			Structure.Add(TEXT("ForEach loop or array processing"));
			Structure.Add(TEXT("Data transformation logic"));
			Structure.Add(TEXT("Output array or processed data"));
			break;
			
		case EBlueprintPatternType::StateManager:
			Structure.Add(TEXT("State variable (enum or integer)"));
			Structure.Add(TEXT("Switch node for state handling"));
			Structure.Add(TEXT("State transition logic"));
			Structure.Add(TEXT("State change events"));
			break;
			
		case EBlueprintPatternType::TimerBased:
			Structure.Add(TEXT("Timer setup (Set Timer)"));
			Structure.Add(TEXT("Timer callback function"));
			Structure.Add(TEXT("Timer management (clear, pause)"));
			Structure.Add(TEXT("Optional repeat logic"));
			break;
			
		case EBlueprintPatternType::InputHandler:
			Structure.Add(TEXT("Input event nodes"));
			Structure.Add(TEXT("Input validation"));
			Structure.Add(TEXT("Action mapping"));
			Structure.Add(TEXT("Response execution"));
			break;
			
		case EBlueprintPatternType::AnimationController:
			Structure.Add(TEXT("Animation Blueprint reference"));
			Structure.Add(TEXT("Animation state management"));
			Structure.Add(TEXT("Blend space or montage control"));
			Structure.Add(TEXT("Animation event handling"));
			break;
			
		case EBlueprintPatternType::AIBehavior:
			Structure.Add(TEXT("AI Controller setup"));
			Structure.Add(TEXT("Behavior tree or state machine"));
			Structure.Add(TEXT("Decision making logic"));
			Structure.Add(TEXT("Action execution"));
			break;
			
		case EBlueprintPatternType::NetworkReplication:
			Structure.Add(TEXT("Replicated variables"));
			Structure.Add(TEXT("Server/Client validation"));
			Structure.Add(TEXT("RPC functions"));
			Structure.Add(TEXT("Network authority checks"));
			break;
			
		case EBlueprintPatternType::CustomEvent:
			Structure.Add(TEXT("Custom Event definition"));
			Structure.Add(TEXT("Event parameters"));
			Structure.Add(TEXT("Event dispatcher binding"));
			Structure.Add(TEXT("Event execution logic"));
			break;
			
		case EBlueprintPatternType::UIController:
			Structure.Add(TEXT("Widget references"));
			Structure.Add(TEXT("UI event handling"));
			Structure.Add(TEXT("Data binding"));
			Structure.Add(TEXT("UI state management"));
			break;
			
		case EBlueprintPatternType::GameplayMechanic:
			Structure.Add(TEXT("Gameplay variables"));
			Structure.Add(TEXT("Mechanic logic"));
			Structure.Add(TEXT("Player interaction"));
			Structure.Add(TEXT("Feedback systems"));
			break;
			
		case EBlueprintPatternType::UtilityFunction:
			Structure.Add(TEXT("Pure function definition"));
			Structure.Add(TEXT("Input validation"));
			Structure.Add(TEXT("Utility calculation"));
			Structure.Add(TEXT("Return result"));
			break;
			
		default:
			Structure.Add(TEXT("Basic blueprint structure"));
			break;
	}
	
	return Structure;
}

void FBlueprintPatternDetector::LoadPatternDatabase()
{
	PatternDatabase.Empty();
	PatternIndexMap.Empty();
	KeyTermToPatternMap.Empty();
	
	// Initialize all pattern types
	InitializeSimpleFunctionPattern();
	InitializeEventHandlerPattern();
	InitializeDataProcessorPattern();
	InitializeStateManagerPattern();
	InitializeTimerBasedPattern();
	InitializeInputHandlerPattern();
	InitializeAnimationControllerPattern();
	InitializeAIBehaviorPattern();
	InitializeNetworkReplicationPattern();
	InitializeCustomEventPattern();
	InitializeUIControllerPattern();
	InitializeGameplayMechanicPattern();
	InitializeUtilityFunctionPattern();
	
	// Rebuild index maps
	RebuildIndexMaps();
	
	UE_LOG(LogBlueprintPatternDetector, Log, TEXT("Loaded %d blueprint patterns"), PatternDatabase.Num());
}

void FBlueprintPatternDetector::AddCustomPattern(const FBlueprintPattern& Pattern)
{
	// Check if pattern already exists
	if (PatternIndexMap.Contains(Pattern.PatternType))
	{
		UE_LOG(LogBlueprintPatternDetector, Warning, TEXT("Pattern type already exists: %s"), *Pattern.PatternName);
		return;
	}
	
	PatternDatabase.Add(Pattern);
	RebuildIndexMaps();
	
	UE_LOG(LogBlueprintPatternDetector, Log, TEXT("Added custom pattern: %s"), *Pattern.PatternName);
}

void FBlueprintPatternDetector::RemovePattern(EBlueprintPatternType PatternType)
{
	if (const int32* IndexPtr = PatternIndexMap.Find(PatternType))
	{
		int32 Index = *IndexPtr;
		if (Index < PatternDatabase.Num())
		{
			FString PatternName = PatternDatabase[Index].PatternName;
			PatternDatabase.RemoveAt(Index);
			RebuildIndexMaps();
			UE_LOG(LogBlueprintPatternDetector, Log, TEXT("Removed pattern: %s"), *PatternName);
		}
	}
}

TArray<EBlueprintPatternType> FBlueprintPatternDetector::GetAllPatternTypes() const
{
	TArray<EBlueprintPatternType> PatternTypes;
	PatternIndexMap.GetKeys(PatternTypes);
	return PatternTypes;
}

bool FBlueprintPatternDetector::IsPatternApplicable(const FString& InputText, EBlueprintPatternType PatternType) const
{
	float Confidence = const_cast<FBlueprintPatternDetector*>(this)->CalculatePatternConfidence(InputText, PatternType);
	return Confidence >= ConfidenceThreshold;
}

TArray<FString> FBlueprintPatternDetector::ExtractPatternIndicators(const FString& InputText) const
{
	TArray<FString> Indicators;
	FString LowerInput = InputText.ToLower();
	
	// Extract indicators from all patterns
	for (const FBlueprintPattern& Pattern : PatternDatabase)
	{
		for (const FString& KeyTerm : Pattern.KeyTerms)
		{
			if (LowerInput.Contains(KeyTerm.ToLower()))
			{
				Indicators.AddUnique(KeyTerm);
			}
		}
	}
	
	return Indicators;
}

float FBlueprintPatternDetector::CalculateOverallComplexity(const FString& InputText)
{
	if (InputText.IsEmpty())
	{
		return 0.0f;
	}
	
	float TotalComplexity = 0.0f;
	float TotalWeight = 0.0f;
	
	// Calculate weighted average complexity based on pattern confidences
	for (const FBlueprintPattern& Pattern : PatternDatabase)
	{
		float Confidence = CalculatePatternConfidence(InputText, Pattern.PatternType);
		if (Confidence > 0.0f)
		{
			TotalComplexity += Pattern.Complexity * Confidence;
			TotalWeight += Confidence;
		}
	}
	
	return TotalWeight > 0.0f ? TotalComplexity / TotalWeight : 1.0f;
}

void FBlueprintPatternDetector::SetConfidenceThreshold(float Threshold)
{
	ConfidenceThreshold = FMath::Clamp(Threshold, 0.0f, 1.0f);
}

void FBlueprintPatternDetector::SetMaxSecondaryPatterns(int32 MaxPatterns)
{
	MaxSecondaryPatterns = FMath::Max(0, MaxPatterns);
}

void FBlueprintPatternDetector::SetPatternWeights(const TMap<EBlueprintPatternType, float>& Weights)
{
	PatternWeights = Weights;
}

int32 FBlueprintPatternDetector::CountPatternKeyTerms(const FString& InputText, const FBlueprintPattern& Pattern) const
{
	if (InputText.IsEmpty() || Pattern.KeyTerms.Num() == 0)
	{
		return 0;
	}
	
	FString LowerInput = InputText.ToLower();
	int32 MatchCount = 0;
	
	for (const FString& KeyTerm : Pattern.KeyTerms)
	{
		if (LowerInput.Contains(KeyTerm.ToLower()))
		{
			MatchCount++;
		}
	}
	
	return MatchCount;
}

bool FBlueprintPatternDetector::ContainsPatternStructure(const FString& InputText, const FBlueprintPattern& Pattern) const
{
	FString LowerInput = InputText.ToLower();
	
	// Check for structural indicators based on pattern type
	switch (Pattern.PatternType)
	{
		case EBlueprintPatternType::SimpleFunction:
			return LowerInput.Contains(TEXT("function")) || LowerInput.Contains(TEXT("calculate")) || LowerInput.Contains(TEXT("return"));
			
		case EBlueprintPatternType::EventHandler:
			return LowerInput.Contains(TEXT("when")) || LowerInput.Contains(TEXT("event")) || LowerInput.Contains(TEXT("trigger"));
			
		case EBlueprintPatternType::DataProcessor:
			return LowerInput.Contains(TEXT("array")) || LowerInput.Contains(TEXT("process")) || LowerInput.Contains(TEXT("filter"));
			
		case EBlueprintPatternType::StateManager:
			return LowerInput.Contains(TEXT("state")) || LowerInput.Contains(TEXT("switch")) || LowerInput.Contains(TEXT("transition"));
			
		case EBlueprintPatternType::TimerBased:
			return LowerInput.Contains(TEXT("timer")) || LowerInput.Contains(TEXT("delay")) || LowerInput.Contains(TEXT("after"));
			
		case EBlueprintPatternType::InputHandler:
			return LowerInput.Contains(TEXT("input")) || LowerInput.Contains(TEXT("key")) || LowerInput.Contains(TEXT("press"));
			
		case EBlueprintPatternType::AnimationController:
			return LowerInput.Contains(TEXT("animation")) || LowerInput.Contains(TEXT("anim")) || LowerInput.Contains(TEXT("play"));
			
		case EBlueprintPatternType::AIBehavior:
			return LowerInput.Contains(TEXT("ai")) || LowerInput.Contains(TEXT("behavior")) || LowerInput.Contains(TEXT("decision"));
			
		case EBlueprintPatternType::NetworkReplication:
			return LowerInput.Contains(TEXT("network")) || LowerInput.Contains(TEXT("multiplayer")) || LowerInput.Contains(TEXT("server"));
			
		case EBlueprintPatternType::CustomEvent:
			return LowerInput.Contains(TEXT("custom event")) || LowerInput.Contains(TEXT("dispatch")) || LowerInput.Contains(TEXT("bind"));
			
		case EBlueprintPatternType::UIController:
			return LowerInput.Contains(TEXT("ui")) || LowerInput.Contains(TEXT("widget")) || LowerInput.Contains(TEXT("interface"));
			
		case EBlueprintPatternType::GameplayMechanic:
			return LowerInput.Contains(TEXT("gameplay")) || LowerInput.Contains(TEXT("mechanic")) || LowerInput.Contains(TEXT("game"));
			
		case EBlueprintPatternType::UtilityFunction:
			return LowerInput.Contains(TEXT("utility")) || LowerInput.Contains(TEXT("helper")) || LowerInput.Contains(TEXT("tool"));
			
		default:
			return false;
	}
}

float FBlueprintPatternDetector::CalculateTermDensity(const FString& InputText, const TArray<FString>& Terms) const
{
	if (InputText.IsEmpty() || Terms.Num() == 0)
	{
		return 0.0f;
	}
	
	TArray<FString> Words = TokenizeInput(InputText);
	if (Words.Num() == 0)
	{
		return 0.0f;
	}
	
	int32 TermCount = CountTermOccurrences(InputText, Terms);
	return (float)TermCount / Words.Num();
}

float FBlueprintPatternDetector::CalculateSemanticSimilarity(const FString& InputText, const FBlueprintPattern& Pattern) const
{
	// Simple semantic similarity based on common words and context
	TArray<FString> InputWords = TokenizeInput(InputText);
	TArray<FString> PatternWords;
	
	// Combine pattern description and key terms
	PatternWords.Append(TokenizeInput(Pattern.Description));
	for (const FString& KeyTerm : Pattern.KeyTerms)
	{
		PatternWords.Append(TokenizeInput(KeyTerm));
	}
	
	if (InputWords.Num() == 0 || PatternWords.Num() == 0)
	{
		return 0.0f;
	}
	
	// Count common words
	int32 CommonWords = 0;
	for (const FString& InputWord : InputWords)
	{
		for (const FString& PatternWord : PatternWords)
		{
			if (InputWord.ToLower() == PatternWord.ToLower())
			{
				CommonWords++;
				break;
			}
		}
	}
	
	// Calculate similarity as ratio of common words
	float Similarity = (float)CommonWords / FMath::Max(InputWords.Num(), PatternWords.Num());
	return FMath::Clamp(Similarity, 0.0f, 1.0f);
}

void FBlueprintPatternDetector::InitializeSimpleFunctionPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::SimpleFunction, TEXT("Simple Function"), 
		TEXT("A basic function that performs a specific task with input and output parameters"), 1.0f);
	
	Pattern.KeyTerms = {
		TEXT("function"), TEXT("calculate"), TEXT("compute"), TEXT("return"), 
		TEXT("input"), TEXT("output"), TEXT("parameter"), TEXT("result")
	};
	
	Pattern.RequiredComponents = { TEXT("None") };
	Pattern.CommonNodes = { TEXT("Function Entry"), TEXT("Return Node"), TEXT("Math Operations"), TEXT("Variables") };
	Pattern.TypicalParameters = { TEXT("Input Parameters"), TEXT("Return Value") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeEventHandlerPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::EventHandler, TEXT("Event Handler"), 
		TEXT("A blueprint structure that responds to specific events like Begin Play, Hit, or custom events"), 1.2f);
	
	Pattern.KeyTerms = {
		TEXT("event"), TEXT("handle"), TEXT("respond"), TEXT("trigger"), TEXT("when"), 
		TEXT("on"), TEXT("begin play"), TEXT("overlap"), TEXT("hit"), TEXT("collision")
	};
	
	Pattern.RequiredComponents = { TEXT("Event System") };
	Pattern.CommonNodes = { TEXT("Event Nodes"), TEXT("Branch"), TEXT("Sequence"), TEXT("Custom Events") };
	Pattern.TypicalParameters = { TEXT("Event Parameters"), TEXT("Actor References") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeDataProcessorPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::DataProcessor, TEXT("Data Processor"), 
		TEXT("A blueprint structure that processes, filters, or transforms data or arrays"), 1.5f);
	
	Pattern.KeyTerms = {
		TEXT("process"), TEXT("filter"), TEXT("transform"), TEXT("array"), TEXT("data"), 
		TEXT("sort"), TEXT("map"), TEXT("reduce"), TEXT("foreach"), TEXT("loop")
	};
	
	Pattern.RequiredComponents = { TEXT("None") };
	Pattern.CommonNodes = { TEXT("ForEach Loop"), TEXT("Array Operations"), TEXT("Branch"), TEXT("Math Operations") };
	Pattern.TypicalParameters = { TEXT("Input Array"), TEXT("Filter Criteria"), TEXT("Output Array") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeStateManagerPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::StateManager, TEXT("State Manager"), 
		TEXT("A blueprint structure that manages different states and transitions between them"), 1.8f);
	
	Pattern.KeyTerms = {
		TEXT("state"), TEXT("transition"), TEXT("switch"), TEXT("change"), TEXT("condition"), 
		TEXT("current state"), TEXT("previous state"), TEXT("state machine")
	};
	
	Pattern.RequiredComponents = { TEXT("State Variables") };
	Pattern.CommonNodes = { TEXT("Switch"), TEXT("Branch"), TEXT("Enum Variables"), TEXT("Custom Events") };
	Pattern.TypicalParameters = { TEXT("Current State"), TEXT("Target State"), TEXT("Transition Conditions") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeTimerBasedPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::TimerBased, TEXT("Timer Based"), 
		TEXT("A blueprint structure that uses timers to trigger events or actions at specific intervals"), 1.3f);
	
	Pattern.KeyTerms = {
		TEXT("timer"), TEXT("delay"), TEXT("interval"), TEXT("periodic"), TEXT("schedule"), 
		TEXT("after"), TEXT("seconds"), TEXT("repeat"), TEXT("countdown")
	};
	
	Pattern.RequiredComponents = { TEXT("Timer System") };
	Pattern.CommonNodes = { TEXT("Set Timer"), TEXT("Clear Timer"), TEXT("Delay"), TEXT("Timeline") };
	Pattern.TypicalParameters = { TEXT("Duration"), TEXT("Loop"), TEXT("Timer Handle") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeInputHandlerPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::InputHandler, TEXT("Input Handler"), 
		TEXT("A blueprint structure that processes player input and triggers appropriate actions"), 1.4f);
	
	Pattern.KeyTerms = {
		TEXT("input"), TEXT("key"), TEXT("press"), TEXT("release"), TEXT("button"), 
		TEXT("mouse"), TEXT("keyboard"), TEXT("controller"), TEXT("action"), TEXT("axis")
	};
	
	Pattern.RequiredComponents = { TEXT("Input Component") };
	Pattern.CommonNodes = { TEXT("Input Events"), TEXT("Branch"), TEXT("Action Mappings"), TEXT("Axis Mappings") };
	Pattern.TypicalParameters = { TEXT("Input Key"), TEXT("Action Name"), TEXT("Axis Value") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeAnimationControllerPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::AnimationController, TEXT("Animation Controller"), 
		TEXT("A blueprint structure that manages animation states, blending, and transitions"), 1.7f);
	
	Pattern.KeyTerms = {
		TEXT("animation"), TEXT("anim"), TEXT("blend"), TEXT("montage"), TEXT("sequence"), 
		TEXT("state machine"), TEXT("transition"), TEXT("play"), TEXT("stop")
	};
	
	Pattern.RequiredComponents = { TEXT("Skeletal Mesh Component"), TEXT("Animation Blueprint") };
	Pattern.CommonNodes = { TEXT("Play Animation"), TEXT("Animation Blueprint"), TEXT("Blend Spaces"), TEXT("State Machines") };
	Pattern.TypicalParameters = { TEXT("Animation Asset"), TEXT("Blend Weight"), TEXT("Play Rate") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeAIBehaviorPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::AIBehavior, TEXT("AI Behavior"), 
		TEXT("A blueprint structure that defines AI behavior, decision making, or pathfinding"), 1.9f);
	
	Pattern.KeyTerms = {
		TEXT("ai"), TEXT("behavior"), TEXT("decision"), TEXT("pathfinding"), TEXT("navigation"), 
		TEXT("enemy"), TEXT("npc"), TEXT("blackboard"), TEXT("behavior tree")
	};
	
	Pattern.RequiredComponents = { TEXT("AI Controller"), TEXT("Behavior Tree"), TEXT("Blackboard") };
	Pattern.CommonNodes = { TEXT("AI MoveTo"), TEXT("Behavior Tree"), TEXT("Blackboard"), TEXT("Pawn Sensing") };
	Pattern.TypicalParameters = { TEXT("Target Location"), TEXT("Blackboard Key"), TEXT("Behavior") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeNetworkReplicationPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::NetworkReplication, TEXT("Network Replication"), 
		TEXT("A blueprint structure that handles network replication of variables and events"), 2.0f);
	
	Pattern.KeyTerms = {
		TEXT("network"), TEXT("replicate"), TEXT("multiplayer"), TEXT("server"), TEXT("client"), 
		TEXT("authority"), TEXT("rpc"), TEXT("reliable"), TEXT("unreliable")
	};
	
	Pattern.RequiredComponents = { TEXT("Network System") };
	Pattern.CommonNodes = { TEXT("RPC Events"), TEXT("Replicated Variables"), TEXT("Authority Checks"), TEXT("Network Conditions") };
	Pattern.TypicalParameters = { TEXT("Replicated Variables"), TEXT("RPC Parameters"), TEXT("Network Role") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeCustomEventPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::CustomEvent, TEXT("Custom Event"), 
		TEXT("A blueprint structure that defines and dispatches custom events with parameters"), 1.5f);
	
	Pattern.KeyTerms = {
		TEXT("custom event"), TEXT("dispatch"), TEXT("event"), TEXT("bind"), TEXT("callback"), 
		TEXT("delegate"), TEXT("event dispatcher"), TEXT("trigger")
	};
	
	Pattern.RequiredComponents = { TEXT("Event System") };
	Pattern.CommonNodes = { TEXT("Custom Event"), TEXT("Event Dispatcher"), TEXT("Bind Event"), TEXT("Call Event") };
	Pattern.TypicalParameters = { TEXT("Event Parameters"), TEXT("Delegate Binding") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeUIControllerPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::UIController, TEXT("UI Controller"), 
		TEXT("A blueprint structure that manages user interface elements and interactions"), 1.6f);
	
	Pattern.KeyTerms = {
		TEXT("ui"), TEXT("widget"), TEXT("interface"), TEXT("button"), TEXT("menu"), 
		TEXT("hud"), TEXT("screen"), TEXT("panel"), TEXT("dialog")
	};
	
	Pattern.RequiredComponents = { TEXT("Widget Component"), TEXT("User Widget") };
	Pattern.CommonNodes = { TEXT("Create Widget"), TEXT("Add to Viewport"), TEXT("Widget Interactions"), TEXT("Bind Events") };
	Pattern.TypicalParameters = { TEXT("Widget Reference"), TEXT("UI Data"), TEXT("Interaction Events") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeGameplayMechanicPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::GameplayMechanic, TEXT("Gameplay Mechanic"), 
		TEXT("A blueprint structure that implements core gameplay mechanics and systems"), 1.7f);
	
	Pattern.KeyTerms = {
		TEXT("gameplay"), TEXT("mechanic"), TEXT("game"), TEXT("player"), TEXT("score"), 
		TEXT("health"), TEXT("damage"), TEXT("pickup"), TEXT("power"), TEXT("ability")
	};
	
	Pattern.RequiredComponents = { TEXT("Game Systems") };
	Pattern.CommonNodes = { TEXT("Gameplay Events"), TEXT("Player Interactions"), TEXT("Game State"), TEXT("Scoring Systems") };
	Pattern.TypicalParameters = { TEXT("Player Reference"), TEXT("Game Values"), TEXT("Mechanic Parameters") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::InitializeUtilityFunctionPattern()
{
	FBlueprintPattern Pattern(EBlueprintPatternType::UtilityFunction, TEXT("Utility Function"), 
		TEXT("A pure function that provides utility calculations or helper functionality"), 0.8f);
	
	Pattern.KeyTerms = {
		TEXT("utility"), TEXT("helper"), TEXT("tool"), TEXT("pure"), TEXT("static"), 
		TEXT("math"), TEXT("convert"), TEXT("format"), TEXT("validate")
	};
	
	Pattern.RequiredComponents = { TEXT("None") };
	Pattern.CommonNodes = { TEXT("Pure Functions"), TEXT("Math Operations"), TEXT("String Operations"), TEXT("Conversion Nodes") };
	Pattern.TypicalParameters = { TEXT("Input Values"), TEXT("Utility Result") };
	
	PatternDatabase.Add(Pattern);
}

void FBlueprintPatternDetector::RebuildIndexMaps()
{
	PatternIndexMap.Empty();
	KeyTermToPatternMap.Empty();
	
	for (int32 i = 0; i < PatternDatabase.Num(); i++)
	{
		const FBlueprintPattern& Pattern = PatternDatabase[i];
		
		// Add to pattern index map
		PatternIndexMap.Add(Pattern.PatternType, i);
		
		// Add to key term map
		for (const FString& KeyTerm : Pattern.KeyTerms)
		{
			FString NormalizedTerm = KeyTerm.ToLower();
			if (!KeyTermToPatternMap.Contains(NormalizedTerm))
			{
				KeyTermToPatternMap.Add(NormalizedTerm, TArray<EBlueprintPatternType>());
			}
			KeyTermToPatternMap[NormalizedTerm].AddUnique(Pattern.PatternType);
		}
	}
}

TArray<FString> FBlueprintPatternDetector::TokenizeInput(const FString& InputText) const
{
	TArray<FString> Tokens;
	
	if (InputText.IsEmpty())
	{
		return Tokens;
	}
	
	FString CurrentToken;
	for (int32 i = 0; i < InputText.Len(); i++)
	{
		TCHAR Char = InputText[i];
		
		if (FChar::IsAlnum(Char) || Char == '\'' || Char == '-')
		{
			CurrentToken.AppendChar(Char);
		}
		else
		{
			if (!CurrentToken.IsEmpty())
			{
				Tokens.Add(CurrentToken);
			}
			CurrentToken.Empty();
		}
	}
	
	// Add the last token if any
	if (!CurrentToken.IsEmpty())
	{
		Tokens.Add(CurrentToken);
	}
	
	return Tokens;
}

bool FBlueprintPatternDetector::ContainsAnyTerm(const FString& InputText, const TArray<FString>& Terms) const
{
	FString LowerInput = InputText.ToLower();
	
	for (const FString& Term : Terms)
	{
		if (LowerInput.Contains(Term.ToLower()))
		{
			return true;
		}
	}
	
	return false;
}

int32 FBlueprintPatternDetector::CountTermOccurrences(const FString& InputText, const TArray<FString>& Terms) const
{
	FString LowerInput = InputText.ToLower();
	int32 Count = 0;
	
	for (const FString& Term : Terms)
	{
		FString LowerTerm = Term.ToLower();
		int32 SearchIndex = 0;
		
		while ((SearchIndex = LowerInput.Find(LowerTerm, SearchIndex)) != INDEX_NONE)
		{
			Count++;
			SearchIndex += LowerTerm.Len();
		}
	}
	
	return Count;
}

TArray<TPair<EBlueprintPatternType, float>> FBlueprintPatternDetector::RankPatterns(const FString& InputText)
{
	TArray<TPair<EBlueprintPatternType, float>> RankedPatterns;
	
	// Calculate confidence for each pattern
	for (const FBlueprintPattern& Pattern : PatternDatabase)
	{
		float Confidence = CalculatePatternConfidence(InputText, Pattern.PatternType);
		if (Confidence > 0.0f)
		{
			RankedPatterns.Add(TPair<EBlueprintPatternType, float>(Pattern.PatternType, Confidence));
		}
	}
	
	// Sort by confidence (highest first)
	RankedPatterns.Sort([](const TPair<EBlueprintPatternType, float>& A, const TPair<EBlueprintPatternType, float>& B) {
		return A.Value > B.Value;
	});
	
	return RankedPatterns;
}

FString FBlueprintPatternDetector::NormalizeText(const FString& Text) const
{
	return Text.ToLower().TrimStartAndEnd();
} 