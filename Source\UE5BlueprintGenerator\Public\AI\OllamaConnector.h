#pragma once

#include "CoreMinimal.h"
#include "AIModelConnector.h"
#include "Http.h"
#include "Dom/JsonObject.h"

/**
 * Ollama-specific AI model connector implementation
 * Handles communication with Ollama API endpoints
 */
class UE5BLUEPRINTGENERATOR_API FOllamaConnector : public IAIModelConnector
{
public:
	FOllamaConnector();
	virtual ~FOllamaConnector() override;

	// IAIModelConnector interface
	virtual bool SendRequest(const FAIModelRequest& Request, TFunction<void(const FAIModelResponse&)> OnResponse) override;
	virtual bool TestConnection(const FAIModelSettings& Settings, TFunction<void(bool, const FString&)> OnResponse) override;
	virtual bool GetAvailableModels(const FAIModelSettings& Settings, TFunction<void(const TArray<FString>&)> OnResponse) override;
	virtual void CancelPendingRequests() override;
	virtual EAIModelProvider GetProviderType() const override { return EAIModelProvider::Ollama; }

private:
	// HTTP request handling
	TArray<TSharedPtr<IHttpRequest, ESPMode::ThreadSafe>> PendingRequests;
	
	// Request creation methods
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateGenerateRequest(const FAIModelRequest& Request);
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateTestRequest(const FAIModelSettings& Settings);
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateModelsRequest(const FAIModelSettings& Settings);
	
	// Response handling methods
	void HandleGenerateResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const FAIModelResponse&)> OnResponse);
	void HandleTestResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(bool, const FString&)> OnResponse);
	void HandleModelsResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const TArray<FString>&)> OnResponse);
	
	// JSON processing methods
	TSharedPtr<FJsonObject> CreateGenerateRequestJson(const FAIModelRequest& Request);
	FAIModelResponse ParseGenerateResponse(const FString& ResponseContent);
	TArray<FString> ParseModelsResponse(const FString& ResponseContent);
	
	// Utility methods
	FString GetGenerateEndpoint(const FAIModelSettings& Settings);
	FString GetModelsEndpoint(const FAIModelSettings& Settings);
	FString GetTagsEndpoint(const FAIModelSettings& Settings);
	void RemoveCompletedRequest(FHttpRequestPtr Request);
	
	// Error handling
	FAIModelResponse CreateErrorResponse(const FString& ErrorMessage);
	FString GetHttpErrorMessage(int32 ResponseCode);
}; 