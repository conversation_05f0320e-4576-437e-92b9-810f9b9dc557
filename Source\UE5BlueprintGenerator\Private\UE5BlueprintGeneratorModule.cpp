#include "UE5BlueprintGeneratorModule.h"
#include "UI/BlueprintGeneratorUI.h"
#include "AIModelConnector.h"
#include "Widgets/Docking/SDockTab.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "ToolMenus.h"
#include "LevelEditor.h"
#include "WorkspaceMenuStructure.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"

#define LOCTEXT_NAMESPACE "FUE5BlueprintGeneratorModule"

DEFINE_LOG_CATEGORY(LogBlueprintGenerator);

const FName FUE5BlueprintGeneratorModule::BlueprintGeneratorTabName("BlueprintGenerator");

void FUE5BlueprintGeneratorModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	UE_LOG(LogBlueprintGenerator, Log, TEXT("UE5 Blueprint Generator module starting up"));

	// Initialize AI Model Manager
	FAIModelManager& AIManager = FAIModelManager::Get();
	if (!AIManager.Initialize())
	{
		UE_LOG(LogBlueprintGenerator, Error, TEXT("Failed to initialize AI Model Manager"));
	}

	// Register tab spawners
	RegisterTabSpawners();

	// Register menu extensions
	RegisterMenuExtensions();

	UE_LOG(LogBlueprintGenerator, Log, TEXT("UE5 Blueprint Generator module started successfully"));
}

void FUE5BlueprintGeneratorModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module. For modules that support dynamic reloading,
	// we call this function before unloading the module.
	UE_LOG(LogBlueprintGenerator, Log, TEXT("UE5 Blueprint Generator module shutting down"));

	// Unregister menu extensions
	UnregisterMenuExtensions();

	// Unregister tab spawners
	UnregisterTabSpawners();

	// Shutdown AI Model Manager
	FAIModelManager& AIManager = FAIModelManager::Get();
	AIManager.Shutdown();

	UE_LOG(LogBlueprintGenerator, Log, TEXT("UE5 Blueprint Generator module shut down successfully"));
}

void FUE5BlueprintGeneratorModule::RegisterTabSpawners()
{
	FGlobalTabmanager::Get()->RegisterNomadTabSpawner(BlueprintGeneratorTabName, FOnSpawnTab::CreateRaw(this, &FUE5BlueprintGeneratorModule::OnSpawnBlueprintGeneratorTab))
		.SetDisplayName(LOCTEXT("FBlueprintGeneratorTabTitle", "Blueprint Generator"))
		.SetMenuType(ETabSpawnerMenuType::Hidden)
		.SetGroup(WorkspaceMenu::GetMenuStructure().GetDeveloperToolsCategory());
}

void FUE5BlueprintGeneratorModule::UnregisterTabSpawners()
{
	FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(BlueprintGeneratorTabName);
}

TSharedRef<SDockTab> FUE5BlueprintGeneratorModule::OnSpawnBlueprintGeneratorTab(const FSpawnTabArgs& Args)
{
	return SNew(SDockTab)
		.TabRole(ETabRole::NomadTab)
		[
			// Create the main Blueprint Generator UI
			SNew(SBlueprintGeneratorUI)
		];
}

void FUE5BlueprintGeneratorModule::RegisterMenuExtensions()
{
	// Register the Blueprint Generator menu entry in the Tools menu
	UToolMenus::RegisterStartupCallback(FSimpleMulticastDelegate::FDelegate::CreateRaw(this, &FUE5BlueprintGeneratorModule::RegisterMenus));
}

void FUE5BlueprintGeneratorModule::UnregisterMenuExtensions()
{
	// Menu extensions are automatically cleaned up when the module shuts down
}

void FUE5BlueprintGeneratorModule::RegisterMenus()
{
	// Owner will be used for cleanup in call to UToolMenus::UnregisterOwner
	FToolMenuOwnerScoped OwnerScoped(this);

	{
		UToolMenu* Menu = UToolMenus::Get()->ExtendMenu("LevelEditor.MainMenu.Tools");
		{
			FToolMenuSection& Section = Menu->FindOrAddSection("Programming");
			Section.AddMenuEntryWithCommandList(
				FToolMenuEntry::InitMenuEntry(
					"OpenBlueprintGenerator",
					LOCTEXT("OpenBlueprintGenerator", "Blueprint Generator"),
					LOCTEXT("OpenBlueprintGeneratorTooltip", "Open the AI-powered Blueprint Generator"),
					FSlateIcon(),
					FUIAction(FExecuteAction::CreateRaw(this, &FUE5BlueprintGeneratorModule::OpenBlueprintGeneratorTab))
				),
				nullptr
			);
		}
	}
}

void FUE5BlueprintGeneratorModule::OpenBlueprintGeneratorTab()
{
	FGlobalTabmanager::Get()->TryInvokeTab(BlueprintGeneratorTabName);
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FUE5BlueprintGeneratorModule, UE5BlueprintGenerator) 