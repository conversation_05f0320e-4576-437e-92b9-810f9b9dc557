#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGeneration/NodeFactory.h"
#include "BlueprintGeneration/NodeTypeRegistry.h"
#include "BlueprintGeneration/NodePropertyManager.h"
#include "BlueprintGeneration/NodePositionCalculator.h"
#include "BlueprintGeneration/BlueprintAssembler.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNodeAssemblyEngine, Log, All);

/**
 * Node assembly strategy enumeration
 */
UENUM(BlueprintType)
enum class ENodeAssemblyStrategy : uint8
{
    Sequential,         // Create nodes sequentially in dependency order
    Parallel,          // Create independent nodes in parallel
    Hierarchical,      // Create nodes in hierarchical layers
    TypeGrouped,       // Group nodes by type for batch creation
    PriorityBased,     // Create nodes based on priority levels
    Optimized         // Use intelligent strategy selection
};

/**
 * Node assembly mode enumeration
 */
UENUM(BlueprintType)
enum class ENodeAssemblyMode : uint8
{
    CreateOnly,        // Only create nodes without configuration
    CreateAndConfigure, // Create nodes and configure properties
    FullAssembly,      // Complete assembly including connections
    Incremental       // Incremental assembly with checkpoints
};

/**
 * Node assembly priority enumeration
 */
UENUM(BlueprintType)
enum class ENodeAssemblyPriority : uint8
{
    Critical,          // Critical nodes that must be created first
    High,             // High priority nodes
    Normal,           // Normal priority nodes
    Low,              // Low priority nodes
    Deferred         // Deferred nodes created last
};

/**
 * Node assembly status enumeration
 */
UENUM(BlueprintType)
enum class ENodeAssemblyStatus : uint8
{
    Pending,          // Node assembly is pending
    InProgress,       // Node assembly is in progress
    Completed,        // Node assembly completed successfully
    Failed,           // Node assembly failed
    Skipped,          // Node assembly was skipped
    Cancelled        // Node assembly was cancelled
};

/**
 * Node assembly instruction structure
 */
USTRUCT(BlueprintType)
struct FBPGENNODEASSEMBLYINSTRUCTION
{
    GENERATED_BODY()

    // Instruction identifier
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString InstructionId;

    // Node type to create
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString NodeType;

    // Node name/label
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString NodeName;

    // Assembly priority
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    ENodeAssemblyPriority Priority = ENodeAssemblyPriority::Normal;

    // Node position
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FVector2D Position = FVector2D::ZeroVector;

    // Node properties to configure
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TMap<FString, FString> Properties;

    // Dependencies (other instruction IDs that must complete first)
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TArray<FString> Dependencies;

    // Connection specifications
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TArray<FString> Connections;

    // Assembly timeout (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    float AssemblyTimeout = 30.0f;

    // Can this instruction be assembled in parallel?
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bCanAssembleInParallel = true;

    // Is this instruction optional?
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bIsOptional = false;

    // Retry count for failed assembly
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    int32 RetryCount = 3;

    FBPGENNODEASSEMBLYINSTRUCTION()
    {
        Priority = ENodeAssemblyPriority::Normal;
        Position = FVector2D::ZeroVector;
        AssemblyTimeout = 30.0f;
        bCanAssembleInParallel = true;
        bIsOptional = false;
        RetryCount = 3;
    }
};

/**
 * Node assembly result structure
 */
USTRUCT(BlueprintType)
struct FNODEASSEMBLYRESULT
{
    GENERATED_BODY()

    // Assembly success status
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // Instruction ID that was assembled
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString InstructionId;

    // Created node reference
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TWeakObjectPtr<UK2Node> CreatedNode;

    // Assembly status
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    ENodeAssemblyStatus Status = ENodeAssemblyStatus::Pending;

    // Assembly time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float AssemblyTime = 0.0f;

    // Error message (if failed)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    // Warning messages
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Warnings;

    // Properties that were configured
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> ConfiguredProperties;

    // Connections that were established
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> EstablishedConnections;

    // Retry attempts made
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 RetryAttempts = 0;

    FNODEASSEMBLYRESULT()
    {
        bSuccess = false;
        Status = ENodeAssemblyStatus::Pending;
        AssemblyTime = 0.0f;
        RetryAttempts = 0;
    }
};

/**
 * Node assembly batch structure
 */
USTRUCT(BlueprintType)
struct FNODEASSEMBLYBATCH
{
    GENERATED_BODY()

    // Batch identifier
    UPROPERTY(BlueprintReadOnly, Category = "Batch")
    FString BatchId;

    // Instructions in this batch
    UPROPERTY(BlueprintReadOnly, Category = "Batch")
    TArray<FBPGENNODEASSEMBLYINSTRUCTION> Instructions;

    // Batch priority
    UPROPERTY(BlueprintReadOnly, Category = "Batch")
    ENodeAssemblyPriority BatchPriority = ENodeAssemblyPriority::Normal;

    // Can instructions in this batch be assembled in parallel?
    UPROPERTY(BlueprintReadOnly, Category = "Batch")
    bool bCanAssembleInParallel = true;

    // Batch timeout (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Batch")
    float BatchTimeout = 120.0f;

    // Dependencies (other batch IDs that must complete first)
    UPROPERTY(BlueprintReadOnly, Category = "Batch")
    TArray<FString> Dependencies;

    FNODEASSEMBLYBATCH()
    {
        BatchPriority = ENodeAssemblyPriority::Normal;
        bCanAssembleInParallel = true;
        BatchTimeout = 120.0f;
    }
};

/**
 * Node assembly configuration structure
 */
USTRUCT(BlueprintType)
struct FNODEASSEMBLYCONFIG
{
    GENERATED_BODY()

    // Assembly strategy
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    ENodeAssemblyStrategy Strategy = ENodeAssemblyStrategy::Optimized;

    // Assembly mode
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    ENodeAssemblyMode Mode = ENodeAssemblyMode::FullAssembly;

    // Enable parallel assembly
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableParallelAssembly = true;

    // Maximum parallel operations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly", meta = (ClampMin = "1", ClampMax = "16"))
    int32 MaxParallelOperations = 4;

    // Assembly timeout (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    float AssemblyTimeout = 300.0f;

    // Enable automatic retry on failure
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableAutoRetry = true;

    // Maximum retry attempts
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MaxRetryAttempts = 3;

    // Enable assembly validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableValidation = true;

    // Enable assembly optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableOptimization = true;

    // Enable detailed logging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableDetailedLogging = false;

    // Enable performance monitoring
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnablePerformanceMonitoring = true;

    FNODEASSEMBLYCONFIG()
    {
        Strategy = ENodeAssemblyStrategy::Optimized;
        Mode = ENodeAssemblyMode::FullAssembly;
        bEnableParallelAssembly = true;
        MaxParallelOperations = 4;
        AssemblyTimeout = 300.0f;
        bEnableAutoRetry = true;
        MaxRetryAttempts = 3;
        bEnableValidation = true;
        bEnableOptimization = true;
        bEnableDetailedLogging = false;
        bEnablePerformanceMonitoring = true;
    }
};

/**
 * Node assembly context structure
 */
USTRUCT(BlueprintType)
struct FNODEASSEMBLYCONTEXT
{
    GENERATED_BODY()

    // Target blueprint
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TWeakObjectPtr<UBlueprint> Blueprint;

    // Target graph
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TWeakObjectPtr<UEdGraph> Graph;

    // Assembly configuration
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FNODEASSEMBLYCONFIG Config;

    // Created nodes (instruction ID -> node)
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, TWeakObjectPtr<UK2Node>> CreatedNodes;

    // Assembly start time
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FDateTime StartTime;

    // Current assembly phase
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CurrentPhase;

    // Assembly progress (0.0 - 1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    float Progress = 0.0f;

    FNODEASSEMBLYCONTEXT()
    {
        StartTime = FDateTime::Now();
        Progress = 0.0f;
    }
};

/**
 * Node assembly statistics structure
 */
USTRUCT(BlueprintType)
struct FNODEASSEMBLYSTATISTICS
{
    GENERATED_BODY()

    // Total nodes assembled
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalNodesAssembled = 0;

    // Successful assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulAssemblies = 0;

    // Failed assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedAssemblies = 0;

    // Average assembly time per node
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageAssemblyTime = 0.0f;

    // Total assembly time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float TotalAssemblyTime = 0.0f;

    // Most common node types
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<FString, int32> NodeTypeFrequency;

    // Assembly strategy usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<ENodeAssemblyStrategy, int32> StrategyUsage;

    // Retry statistics
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalRetryAttempts = 0;

    FNODEASSEMBLYSTATISTICS()
    {
        TotalNodesAssembled = 0;
        SuccessfulAssemblies = 0;
        FailedAssemblies = 0;
        AverageAssemblyTime = 0.0f;
        TotalAssemblyTime = 0.0f;
        TotalRetryAttempts = 0;
    }
};

/**
 * Delegate declarations for node assembly events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnNodeAssemblyStarted, const FString&, InstructionId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnNodeAssemblyProgress, const FString&, InstructionId, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnNodeAssemblyCompleted, const FNODEASSEMBLYRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnNodeAssemblyFailed, const FString&, InstructionId, const FString&, ErrorMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBatchAssemblyStarted, const FString&, BatchId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBatchAssemblyCompleted, const FString&, BatchId);

/**
 * Node Assembly Engine - Core node assembly engine for creating and configuring blueprint nodes
 * 
 * This class provides comprehensive node assembly capabilities for blueprint generation,
 * including node creation, property configuration, dependency resolution, and connection establishment.
 * It supports multiple assembly strategies and modes for optimal performance and flexibility.
 */
class UE5BLUEPRINTGENERATOR_API FNodeAssemblyEngine
{
public:
    FNodeAssemblyEngine();
    virtual ~FNodeAssemblyEngine();

    // Core assembly operations
    bool AssembleNode(const FBPGENNODEASSEMBLYINSTRUCTION& Instruction, FNODEASSEMBLYCONTEXT& Context, FNODEASSEMBLYRESULT& OutResult);
    bool AssembleNodes(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);
    bool AssembleBatch(const FNODEASSEMBLYBATCH& Batch, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);
    bool AssembleBatches(const TArray<FNODEASSEMBLYBATCH>& Batches, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);

    // Assembly planning
    bool CreateAssemblyPlan(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, TArray<FNODEASSEMBLYBATCH>& OutBatches);
    bool OptimizeAssemblyPlan(TArray<FNODEASSEMBLYBATCH>& Batches);
    bool ValidateAssemblyPlan(const TArray<FNODEASSEMBLYBATCH>& Batches);

    // Strategy management
    void SetAssemblyStrategy(ENodeAssemblyStrategy Strategy);
    ENodeAssemblyStrategy GetAssemblyStrategy() const;
    ENodeAssemblyStrategy SelectOptimalStrategy(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions);

    // Configuration
    void SetAssemblyConfig(const FNODEASSEMBLYCONFIG& Config);
    FNODEASSEMBLYCONFIG GetAssemblyConfig() const;

    // Context management
    FNODEASSEMBLYCONTEXT CreateAssemblyContext(UBlueprint* Blueprint, UEdGraph* Graph);
    bool ValidateAssemblyContext(const FNODEASSEMBLYCONTEXT& Context);

    // Statistics
    FNODEASSEMBLYSTATISTICS GetStatistics() const;
    void ResetStatistics();

    // Utility operations
    bool CanAssembleInParallel(const FBPGENNODEASSEMBLYINSTRUCTION& Instruction);
    bool HasDependencies(const FBPGENNODEASSEMBLYINSTRUCTION& Instruction);
    bool AreDependenciesSatisfied(const FBPGENNODEASSEMBLYINSTRUCTION& Instruction, const FNODEASSEMBLYCONTEXT& Context);

    // Event delegates
    FOnNodeAssemblyStarted OnNodeAssemblyStarted;
    FOnNodeAssemblyProgress OnNodeAssemblyProgress;
    FOnNodeAssemblyCompleted OnNodeAssemblyCompleted;
    FOnNodeAssemblyFailed OnNodeAssemblyFailed;
    FOnBatchAssemblyStarted OnBatchAssemblyStarted;
    FOnBatchAssemblyCompleted OnBatchAssemblyCompleted;

private:
    // Component references
    TSharedPtr<FNodeFactory> NodeFactory;
    TSharedPtr<FNodeTypeRegistry> NodeTypeRegistry;
    TSharedPtr<FNodePropertyManager> PropertyManager;
    TSharedPtr<FNodePositionCalculator> PositionCalculator;

    // Assembly configuration
    FNODEASSEMBLYCONFIG Config;

    // Assembly statistics
    FNODEASSEMBLYSTATISTICS Statistics;

    // Current assembly state
    bool bIsAssemblyInProgress;
    FString CurrentAssemblyId;
    FDateTime AssemblyStartTime;

    // Assembly strategy implementations
    bool AssembleSequential(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);
    bool AssembleParallel(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);
    bool AssembleHierarchical(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);
    bool AssembleTypeGrouped(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);
    bool AssemblePriorityBased(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, FNODEASSEMBLYCONTEXT& Context, TArray<FNODEASSEMBLYRESULT>& OutResults);

    // Node creation and configuration
    UK2Node* CreateNode(const FBPGENNODEASSEMBLYINSTRUCTION& Instruction, FNODEASSEMBLYCONTEXT& Context);
    bool ConfigureNodeProperties(UK2Node* Node, const FBPGENNODEASSEMBLYINSTRUCTION& Instruction, FNODEASSEMBLYCONTEXT& Context);
    bool EstablishNodeConnections(UK2Node* Node, const FBPGENNODEASSEMBLYINSTRUCTION& Instruction, FNODEASSEMBLYCONTEXT& Context);
    bool PositionNode(UK2Node* Node, const FBPGENNODEASSEMBLYINSTRUCTION& Instruction, FNODEASSEMBLYCONTEXT& Context);

    // Dependency management
    bool ResolveDependencies(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, TArray<FString>& OutExecutionOrder);
    bool SortByDependencies(TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions);
    bool DetectCircularDependencies(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions);

    // Batch management
    bool CreateBatchesFromInstructions(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, TArray<FNODEASSEMBLYBATCH>& OutBatches);
    bool GroupInstructionsByType(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, TMap<FString, TArray<FBPGENNODEASSEMBLYINSTRUCTION>>& OutGroups);
    bool GroupInstructionsByPriority(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions, TMap<ENodeAssemblyPriority, TArray<FBPGENNODEASSEMBLYINSTRUCTION>>& OutGroups);

    // Validation and optimization
    bool ValidateInstruction(const FBPGENNODEASSEMBLYINSTRUCTION& Instruction);
    bool OptimizeInstructionOrder(TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions);
    bool CalculateOptimalPositions(TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions);

    // Error handling and retry
    bool RetryFailedAssembly(const FBPGENNODEASSEMBLYINSTRUCTION& Instruction, FNODEASSEMBLYCONTEXT& Context, FNODEASSEMBLYRESULT& OutResult);
    bool HandleAssemblyError(const FString& InstructionId, const FString& ErrorMessage, FNODEASSEMBLYCONTEXT& Context);

    // Performance monitoring
    void StartAssemblyTimer(const FString& InstructionId);
    void StopAssemblyTimer(const FString& InstructionId, FNODEASSEMBLYRESULT& Result);
    void UpdateStatistics(const FNODEASSEMBLYRESULT& Result);

    // Event broadcasting
    void BroadcastAssemblyStarted(const FString& InstructionId);
    void BroadcastAssemblyProgress(const FString& InstructionId, float Progress);
    void BroadcastAssemblyCompleted(const FNODEASSEMBLYRESULT& Result);
    void BroadcastAssemblyFailed(const FString& InstructionId, const FString& ErrorMessage);
    void BroadcastBatchStarted(const FString& BatchId);
    void BroadcastBatchCompleted(const FString& BatchId);

    // Utility methods
    FString GenerateAssemblyId();
    bool IsValidNodeType(const FString& NodeType);
    bool IsValidPosition(const FVector2D& Position);
    float CalculateAssemblyComplexity(const TArray<FBPGENNODEASSEMBLYINSTRUCTION>& Instructions);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();
}; 