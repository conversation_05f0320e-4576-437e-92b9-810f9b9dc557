#include "NLP/TextInputHandler.h"
#include "UE5BlueprintGeneratorModule.h"
#include "Misc/Char.h"
#include "Misc/CString.h"

DEFINE_LOG_CATEGORY_EXTERN(LogTextInputHandler, Log, All);
DEFINE_LOG_CATEGORY(LogTextInputHandler);

FTextInputHandler::FTextInputHandler()
	: bIsInputValid(false)
	, MinimumLength(10)
	, MaximumLength(4000)
{
	UE_LOG(LogTextInputHandler, Log, TEXT("Text input handler initialized"));
}

FTextInputHandler::~FTextInputHandler()
{
	UE_LOG(LogTextInputHandler, Log, TEXT("Text input handler destroyed"));
}

void FTextInputHandler::SetInputText(const FString& InputText)
{
	RawInputText = InputText;
	ProcessInput();
	UE_LOG(LogTextInput<PERSON><PERSON><PERSON>, VeryV<PERSON><PERSON><PERSON>, TEXT("Input text set: %d characters"), InputText.Len());
}

FString FTextInputHandler::GetInputText() const
{
	return RawInputText;
}

void FTextInputHandler::ClearInput()
{
	RawInputText.Empty();
	ProcessedInputText.Empty();
	SanitizedInputText.Empty();
	bIsInputValid = false;
	ValidationMessage.Empty();
	UE_LOG(LogTextInputHandler, Log, TEXT("Input cleared"));
}

bool FTextInputHandler::IsInputValid() const
{
	return bIsInputValid;
}

FString FTextInputHandler::GetValidationMessage() const
{
	return ValidationMessage;
}

int32 FTextInputHandler::GetInputLength() const
{
	return RawInputText.Len();
}

bool FTextInputHandler::HasMinimumLength() const
{
	return RawInputText.Len() >= MinimumLength;
}

FString FTextInputHandler::GetProcessedInput() const
{
	return ProcessedInputText;
}

FString FTextInputHandler::GetSanitizedInput() const
{
	return SanitizedInputText;
}

bool FTextInputHandler::ContainsTechnicalTerms() const
{
	return ContainsUE5Keywords(RawInputText);
}

bool FTextInputHandler::HasComplexStructure() const
{
	// Check for complex sentence structures
	TArray<FString> Sentences = ExtractSentences(RawInputText);
	
	// Consider complex if:
	// - More than 3 sentences
	// - Contains conditional words (if, when, while, etc.)
	// - Contains multiple clauses
	
	if (Sentences.Num() > 3)
	{
		return true;
	}
	
	// Check for conditional/complex keywords
	TArray<FString> ComplexKeywords = {
		TEXT("if"), TEXT("when"), TEXT("while"), TEXT("unless"), TEXT("although"),
		TEXT("because"), TEXT("since"), TEXT("after"), TEXT("before"), TEXT("during"),
		TEXT("and"), TEXT("or"), TEXT("but"), TEXT("however"), TEXT("therefore")
	};
	
	FString LowerInput = RawInputText.ToLower();
	for (const FString& Keyword : ComplexKeywords)
	{
		if (LowerInput.Contains(Keyword))
		{
			return true;
		}
	}
	
	return false;
}

float FTextInputHandler::EstimateComplexity() const
{
	if (RawInputText.IsEmpty())
	{
		return 0.0f;
	}
	
	float Complexity = 0.0f;
	
	// Base complexity from length
	float LengthFactor = FMath::Clamp(RawInputText.Len() / 100.0f, 0.1f, 2.0f);
	Complexity += LengthFactor * 0.3f;
	
	// Complexity from sentence count
	TArray<FString> Sentences = ExtractSentences(RawInputText);
	float SentenceFactor = FMath::Clamp(Sentences.Num() / 3.0f, 0.1f, 2.0f);
	Complexity += SentenceFactor * 0.2f;
	
	// Complexity from technical terms
	if (ContainsTechnicalTerms())
	{
		Complexity += 0.3f;
	}
	
	// Complexity from structure
	if (HasComplexStructure())
	{
		Complexity += 0.2f;
	}
	
	return FMath::Clamp(Complexity, 0.0f, 2.0f);
}

void FTextInputHandler::SetMinimumLength(int32 MinLength)
{
	MinimumLength = FMath::Max(1, MinLength);
	ProcessInput(); // Re-validate with new length
}

void FTextInputHandler::SetMaximumLength(int32 MaxLength)
{
	MaximumLength = FMath::Max(MinimumLength, MaxLength);
	ProcessInput(); // Re-validate with new length
}

int32 FTextInputHandler::GetMinimumLength() const
{
	return MinimumLength;
}

int32 FTextInputHandler::GetMaximumLength() const
{
	return MaximumLength;
}

void FTextInputHandler::ProcessInput()
{
	// First sanitize the input
	SanitizeInput();
	
	// Then validate
	ValidateInput();
	
	// Finally process if valid
	if (bIsInputValid)
	{
		ProcessedInputText = SanitizedInputText;
	}
	else
	{
		ProcessedInputText.Empty();
	}
}

void FTextInputHandler::ValidateInput()
{
	bIsInputValid = true;
	ValidationMessage.Empty();
	
	// Check length
	if (!CheckLength())
	{
		return;
	}
	
	// Check content
	if (!CheckContent())
	{
		return;
	}
	
	// Check characters
	if (!CheckCharacters())
	{
		return;
	}
	
	UE_LOG(LogTextInputHandler, VeryVerbose, TEXT("Input validation passed"));
}

void FTextInputHandler::SanitizeInput()
{
	if (RawInputText.IsEmpty())
	{
		SanitizedInputText.Empty();
		return;
	}
	
	FString Result = RawInputText;
	
	// Remove special characters that could cause issues
	Result = RemoveSpecialCharacters(Result);
	
	// Normalize whitespace
	Result = NormalizeWhitespace(Result);
	
	// Truncate if too long
	Result = TruncateIfTooLong(Result);
	
	SanitizedInputText = Result;
	UE_LOG(LogTextInputHandler, VeryVerbose, TEXT("Input sanitized: %d -> %d characters"), RawInputText.Len(), SanitizedInputText.Len());
}

bool FTextInputHandler::CheckLength() const
{
	int32 Length = SanitizedInputText.Len();
	
	if (Length < MinimumLength)
	{
		ValidationMessage = FString::Printf(TEXT("Input too short. Minimum %d characters required."), MinimumLength);
		bIsInputValid = false;
		return false;
	}
	
	if (Length > MaximumLength)
	{
		ValidationMessage = FString::Printf(TEXT("Input too long. Maximum %d characters allowed."), MaximumLength);
		bIsInputValid = false;
		return false;
	}
	
	return true;
}

bool FTextInputHandler::CheckContent() const
{
	if (SanitizedInputText.IsEmpty())
	{
		ValidationMessage = TEXT("Input cannot be empty.");
		bIsInputValid = false;
		return false;
	}
	
	// Check if input is only whitespace
	FString TrimmedInput = SanitizedInputText;
	TrimmedInput.TrimStartAndEndInline();
	if (TrimmedInput.IsEmpty())
	{
		ValidationMessage = TEXT("Input cannot contain only whitespace.");
		bIsInputValid = false;
		return false;
	}
	
	// Check for minimum word count
	TArray<FString> Words = ExtractWords(SanitizedInputText);
	if (Words.Num() < 3)
	{
		ValidationMessage = TEXT("Input must contain at least 3 words.");
		bIsInputValid = false;
		return false;
	}
	
	return true;
}

bool FTextInputHandler::CheckCharacters() const
{
	// Check for potentially problematic characters
	for (int32 i = 0; i < SanitizedInputText.Len(); i++)
	{
		TCHAR Char = SanitizedInputText[i];
		
		// Allow alphanumeric, common punctuation, and whitespace
		if (!FChar::IsAlnum(Char) && 
			!FChar::IsWhitespace(Char) &&
			Char != '.' && Char != ',' && Char != '!' && Char != '?' &&
			Char != ':' && Char != ';' && Char != '\'' && Char != '"' &&
			Char != '(' && Char != ')' && Char != '[' && Char != ']' &&
			Char != '{' && Char != '}' && Char != '-' && Char != '_')
		{
			ValidationMessage = FString::Printf(TEXT("Input contains unsupported character: '%c'"), Char);
			bIsInputValid = false;
			return false;
		}
	}
	
	return true;
}

FString FTextInputHandler::RemoveSpecialCharacters(const FString& Input) const
{
	FString Result;
	Result.Reserve(Input.Len());
	
	for (int32 i = 0; i < Input.Len(); i++)
	{
		TCHAR Char = Input[i];
		
		// Keep alphanumeric, whitespace, and basic punctuation
		if (FChar::IsAlnum(Char) || 
			FChar::IsWhitespace(Char) ||
			Char == '.' || Char == ',' || Char == '!' || Char == '?' ||
			Char == ':' || Char == ';' || Char == '\'' || Char == '"' ||
			Char == '(' || Char == ')' || Char == '[' || Char == ']' ||
			Char == '{' || Char == '}' || Char == '-' || Char == '_')
		{
			Result.AppendChar(Char);
		}
	}
	
	return Result;
}

FString FTextInputHandler::NormalizeWhitespace(const FString& Input) const
{
	FString Result = Input;
	
	// Replace tabs with spaces
	Result = Result.Replace(TEXT("\t"), TEXT(" "));
	
	// Replace multiple spaces with single space
	while (Result.Contains(TEXT("  ")))
	{
		Result = Result.Replace(TEXT("  "), TEXT(" "));
	}
	
	// Replace multiple newlines with single newline
	while (Result.Contains(TEXT("\n\n\n")))
	{
		Result = Result.Replace(TEXT("\n\n\n"), TEXT("\n\n"));
	}
	
	// Trim start and end
	Result.TrimStartAndEndInline();
	
	return Result;
}

FString FTextInputHandler::TruncateIfTooLong(const FString& Input) const
{
	if (Input.Len() <= MaximumLength)
	{
		return Input;
	}
	
	// Truncate at word boundary if possible
	FString Truncated = Input.Left(MaximumLength);
	int32 LastSpaceIndex = Truncated.FindLastChar(' ');
	
	if (LastSpaceIndex > MaximumLength * 0.8f) // Only truncate at word boundary if it's not too far back
	{
		Truncated = Truncated.Left(LastSpaceIndex);
	}
	
	return Truncated;
}

TArray<FString> FTextInputHandler::ExtractWords(const FString& Input) const
{
	TArray<FString> Words;
	
	FString CurrentWord;
	for (int32 i = 0; i < Input.Len(); i++)
	{
		TCHAR Char = Input[i];
		
		if (FChar::IsAlnum(Char) || Char == '\'' || Char == '-')
		{
			CurrentWord.AppendChar(Char);
		}
		else
		{
			if (!CurrentWord.IsEmpty())
			{
				Words.Add(CurrentWord);
				CurrentWord.Empty();
			}
		}
	}
	
	// Add the last word if any
	if (!CurrentWord.IsEmpty())
	{
		Words.Add(CurrentWord);
	}
	
	return Words;
}

TArray<FString> FTextInputHandler::ExtractSentences(const FString& Input) const
{
	TArray<FString> Sentences;
	
	FString CurrentSentence;
	for (int32 i = 0; i < Input.Len(); i++)
	{
		TCHAR Char = Input[i];
		CurrentSentence.AppendChar(Char);
		
		if (Char == '.' || Char == '!' || Char == '?')
		{
			// Check if this is end of sentence (not abbreviation)
			if (i == Input.Len() - 1 || FChar::IsWhitespace(Input[i + 1]))
			{
				CurrentSentence.TrimStartAndEndInline();
				if (!CurrentSentence.IsEmpty())
				{
					Sentences.Add(CurrentSentence);
					CurrentSentence.Empty();
				}
			}
		}
	}
	
	// Add the last sentence if any
	CurrentSentence.TrimStartAndEndInline();
	if (!CurrentSentence.IsEmpty())
	{
		Sentences.Add(CurrentSentence);
	}
	
	return Sentences;
}

bool FTextInputHandler::ContainsUE5Keywords(const FString& Input) const
{
	// Common UE5/Blueprint keywords
	TArray<FString> UE5Keywords = {
		// Blueprint terms
		TEXT("blueprint"), TEXT("function"), TEXT("event"), TEXT("variable"), TEXT("component"),
		TEXT("actor"), TEXT("pawn"), TEXT("character"), TEXT("controller"), TEXT("gamemode"),
		TEXT("widget"), TEXT("animation"), TEXT("timeline"), TEXT("sequence"), TEXT("branch"),
		TEXT("loop"), TEXT("cast"), TEXT("interface"), TEXT("macro"), TEXT("delegate"),
		
		// Data types
		TEXT("boolean"), TEXT("integer"), TEXT("float"), TEXT("string"), TEXT("vector"),
		TEXT("rotator"), TEXT("transform"), TEXT("array"), TEXT("map"), TEXT("set"),
		
		// Common functions
		TEXT("begin play"), TEXT("tick"), TEXT("overlap"), TEXT("collision"), TEXT("input"),
		TEXT("movement"), TEXT("physics"), TEXT("render"), TEXT("material"), TEXT("texture"),
		
		// UE5 specific
		TEXT("unreal"), TEXT("engine"), TEXT("level"), TEXT("world"), TEXT("scene"),
		TEXT("mesh"), TEXT("skeletal"), TEXT("static"), TEXT("particle"), TEXT("sound")
	};
	
	FString LowerInput = Input.ToLower();
	for (const FString& Keyword : UE5Keywords)
	{
		if (LowerInput.Contains(Keyword.ToLower()))
		{
			return true;
		}
	}
	
	return false;
} 