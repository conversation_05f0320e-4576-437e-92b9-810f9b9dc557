#include "Settings/BlueprintGeneratorSettings.h"
#include "AIModelConnector.h"
#include "UE5BlueprintGeneratorModule.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"

#define LOCTEXT_NAMESPACE "BlueprintGeneratorSettings"

UBlueprintGeneratorSettings::UBlueprintGeneratorSettings()
{
	CategoryName = TEXT("Plugins");
	SectionName = TEXT("Blueprint Generator");
}

FName UBlueprintGeneratorSettings::GetCategoryName() const
{
	return FName(TEXT("Plugins"));
}

FText UBlueprintGeneratorSettings::GetSectionText() const
{
	return LOCTEXT("SettingsDisplayName", "Blueprint Generator");
}

FText UBlueprintGeneratorSettings::GetSectionDescription() const
{
	return LOCTEXT("SettingsDescription", "Configure the AI-powered Blueprint Generator plugin settings");
}

UBlueprintGeneratorSettings* UBlueprintGeneratorSettings::Get()
{
	return GetMutableDefault<UBlueprintGeneratorSettings>();
}

FAIModelSettings UBlueprintGeneratorSettings::ToAIModelSettings() const
{
	FAIModelSettings Settings;
	Settings.Provider = AIProvider;
	Settings.Endpoint = APIEndpoint;
	Settings.ModelName = ModelName;
	Settings.APIKey = APIKey;
	Settings.Temperature = Temperature;
	Settings.MaxTokens = MaxTokens;
	Settings.TopP = TopP;
	Settings.ContextWindow = ContextWindow;
	Settings.bUseStreaming = bUseStreaming;
	Settings.bEnableCaching = bEnableCaching;
	Settings.SystemPrompt = SystemPrompt;
	Settings.TimeoutSeconds = TimeoutSeconds;
	return Settings;
}

void UBlueprintGeneratorSettings::FromAIModelSettings(const FAIModelSettings& Settings)
{
	AIProvider = Settings.Provider;
	APIEndpoint = Settings.Endpoint;
	ModelName = Settings.ModelName;
	APIKey = Settings.APIKey;
	Temperature = Settings.Temperature;
	MaxTokens = Settings.MaxTokens;
	TopP = Settings.TopP;
	ContextWindow = Settings.ContextWindow;
	bUseStreaming = Settings.bUseStreaming;
	bEnableCaching = Settings.bEnableCaching;
	SystemPrompt = Settings.SystemPrompt;
	TimeoutSeconds = Settings.TimeoutSeconds;
	
	// Save changes
	SaveConfig();
}

bool UBlueprintGeneratorSettings::ValidateSettings(FString& OutErrorMessage) const
{
	// Validate endpoint
	if (!IsValidEndpoint(APIEndpoint))
	{
		OutErrorMessage = TEXT("Invalid API endpoint. Please provide a valid URL.");
		return false;
	}

	// Validate model name
	if (!IsValidModelName(ModelName))
	{
		OutErrorMessage = TEXT("Invalid model name. Model name cannot be empty.");
		return false;
	}

	// Validate temperature range
	if (Temperature < 0.0f || Temperature > 2.0f)
	{
		OutErrorMessage = TEXT("Temperature must be between 0.0 and 2.0.");
		return false;
	}

	// Validate max tokens
	if (MaxTokens < 1 || MaxTokens > 8192)
	{
		OutErrorMessage = TEXT("Max tokens must be between 1 and 8192.");
		return false;
	}

	// Validate top_p range
	if (TopP < 0.0f || TopP > 1.0f)
	{
		OutErrorMessage = TEXT("Top P must be between 0.0 and 1.0.");
		return false;
	}

	// Validate timeout
	if (TimeoutSeconds < 5 || TimeoutSeconds > 300)
	{
		OutErrorMessage = TEXT("Timeout must be between 5 and 300 seconds.");
		return false;
	}

	return true;
}

void UBlueprintGeneratorSettings::ResetToDefaults()
{
	// Reset AI model settings
	AIProvider = EAIModelProvider::Ollama;
	ModelName = TEXT("llama3");
	APIEndpoint = TEXT("http://localhost:11434/api/generate");
	APIKey = TEXT("");
	Temperature = 0.7f;
	MaxTokens = 2048;
	TopP = 0.9f;
	ContextWindow = 4096;
	bUseStreaming = false;
	bEnableCaching = true;
	TimeoutSeconds = 30;
	SystemPrompt = TEXT("You are an expert in Unreal Engine 5 Blueprint creation. Generate detailed, efficient blueprint structures following UE5 best practices.");

	// Reset generation options
	bGenerateCommentsByDefault = true;
	bIncludeErrorHandlingByDefault = true;
	bUseAdvancedLogicByDefault = false;
	bOptimizePerformanceByDefault = true;

	// Reset UI preferences
	bRememberWindowSize = true;
	bAutoSaveInput = true;
	bShowComplexityIndicator = true;
	bShowRealtimeSuggestions = true;

	// Reset advanced settings
	bEnableDebugLogging = false;
	MaxHistoryEntries = 100;
	bAutoRefreshModelsOnStartup = true;

	// Save changes
	SaveConfig();

	UE_LOG(LogBlueprintGenerator, Log, TEXT("Blueprint Generator settings reset to defaults"));
}

void UBlueprintGeneratorSettings::TestConnection()
{
	FAIModelManager& AIManager = FAIModelManager::Get();
	
	// Update AI manager with current settings
	FAIModelSettings CurrentSettings = ToAIModelSettings();
	AIManager.SetCurrentSettings(CurrentSettings);

	// Test connection
	AIManager.TestCurrentConnection([](bool bSuccess, const FString& Message)
	{
		FText NotificationText;
		if (bSuccess)
		{
			NotificationText = FText::Format(LOCTEXT("ConnectionTestSuccess", "Connection test successful: {0}"), FText::FromString(Message));
			UE_LOG(LogBlueprintGenerator, Log, TEXT("Connection test successful: %s"), *Message);
		}
		else
		{
			NotificationText = FText::Format(LOCTEXT("ConnectionTestFailed", "Connection test failed: {0}"), FText::FromString(Message));
			UE_LOG(LogBlueprintGenerator, Warning, TEXT("Connection test failed: %s"), *Message);
		}

		// Show notification
		FNotificationInfo Info(NotificationText);
		Info.ExpireDuration = 5.0f;
		Info.bFireAndForget = true;
		Info.Image = bSuccess ? FCoreStyle::Get().GetBrush("Icons.SuccessWithColor") : FCoreStyle::Get().GetBrush("Icons.ErrorWithColor");
		
		FSlateNotificationManager::Get().AddNotification(Info);
	});
}

void UBlueprintGeneratorSettings::RefreshAvailableModels()
{
	FAIModelManager& AIManager = FAIModelManager::Get();
	
	// Update AI manager with current settings
	FAIModelSettings CurrentSettings = ToAIModelSettings();
	AIManager.SetCurrentSettings(CurrentSettings);

	// Get available models
	AIManager.GetAvailableModels([](const TArray<FString>& Models)
	{
		FText NotificationText;
		if (Models.Num() > 0)
		{
			NotificationText = FText::Format(LOCTEXT("ModelsRefreshed", "Found {0} available models"), FText::AsNumber(Models.Num()));
			UE_LOG(LogBlueprintGenerator, Log, TEXT("Refreshed models: found %d models"), Models.Num());
			
			for (const FString& Model : Models)
			{
				UE_LOG(LogBlueprintGenerator, VeryVerbose, TEXT("Available model: %s"), *Model);
			}
		}
		else
		{
			NotificationText = LOCTEXT("NoModelsFound", "No models found. Check your connection and settings.");
			UE_LOG(LogBlueprintGenerator, Warning, TEXT("No models found during refresh"));
		}

		// Show notification
		FNotificationInfo Info(NotificationText);
		Info.ExpireDuration = 5.0f;
		Info.bFireAndForget = true;
		Info.Image = Models.Num() > 0 ? FCoreStyle::Get().GetBrush("Icons.SuccessWithColor") : FCoreStyle::Get().GetBrush("Icons.WarningWithColor");
		
		FSlateNotificationManager::Get().AddNotification(Info);
	});
}

void UBlueprintGeneratorSettings::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property)
	{
		const FName PropertyName = PropertyChangedEvent.Property->GetFName();
		
		// Validate settings when changed
		FString ErrorMessage;
		if (!ValidateSettings(ErrorMessage))
		{
			UE_LOG(LogBlueprintGenerator, Warning, TEXT("Settings validation failed: %s"), *ErrorMessage);
		}

		// Update AI manager if AI-related settings changed
		if (PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, AIProvider) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, ModelName) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, APIEndpoint) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, APIKey) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, Temperature) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, MaxTokens) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, TopP) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, ContextWindow) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, bUseStreaming) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, bEnableCaching) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, SystemPrompt) ||
			PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, TimeoutSeconds))
		{
			// Update AI manager with new settings
			FAIModelManager& AIManager = FAIModelManager::Get();
			FAIModelSettings NewSettings = ToAIModelSettings();
			AIManager.SetCurrentSettings(NewSettings);
			
			UE_LOG(LogBlueprintGenerator, Log, TEXT("AI model settings updated: %s"), *PropertyName.ToString());
		}

		// Handle provider change - update default endpoint
		if (PropertyName == GET_MEMBER_NAME_CHECKED(UBlueprintGeneratorSettings, AIProvider))
		{
			switch (AIProvider)
			{
				case EAIModelProvider::Ollama:
					APIEndpoint = TEXT("http://localhost:11434/api/generate");
					ModelName = TEXT("llama3");
					break;
				case EAIModelProvider::LMStudio:
					APIEndpoint = TEXT("http://localhost:1234/v1/chat/completions");
					ModelName = TEXT("local-model");
					break;
				default:
					break;
			}
		}
	}
}

bool UBlueprintGeneratorSettings::IsValidEndpoint(const FString& Endpoint) const
{
	if (Endpoint.IsEmpty())
	{
		return false;
	}

	// Basic URL validation
	if (!Endpoint.StartsWith(TEXT("http://")) && !Endpoint.StartsWith(TEXT("https://")))
	{
		return false;
	}

	// Check for valid characters (basic validation)
	for (const TCHAR& Char : Endpoint)
	{
		if (!FChar::IsAlnum(Char) && Char != ':' && Char != '/' && Char != '.' && Char != '-' && Char != '_')
		{
			return false;
		}
	}

	return true;
}

bool UBlueprintGeneratorSettings::IsValidModelName(const FString& ModelName) const
{
	return !ModelName.IsEmpty() && ModelName.Len() > 0;
}

#undef LOCTEXT_NAMESPACE 