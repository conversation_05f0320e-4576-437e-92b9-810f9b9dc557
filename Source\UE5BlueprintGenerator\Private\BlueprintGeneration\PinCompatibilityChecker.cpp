#include "BlueprintGeneration/PinCompatibilityChecker.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "EdGraph/EdGraph.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

DEFINE_LOG_CATEGORY(LogPinCompatibilityChecker);

FPinCompatibilityChecker::FPinCompatibilityChecker()
    : bIsInitialized(false)
    , bVerboseLogging(false)
    , bTrackStatistics(true)
    , TotalCompatibilityChecks(0)
    , TotalBatchOperations(0)
    , TotalValidationOperations(0)
    , LastAnalysisTimeMs(0.0f)
{
    UE_LOG(LogPinCompatibilityChecker, Log, TEXT("PinCompatibilityChecker created"));
}

FPinCompatibilityChecker::~FPinCompatibilityChecker()
{
    Shutdown();
    UE_LOG(LogPinCompatibilityChecker, Log, TEXT("PinCompatibilityChecker destroyed"));
}

bool FPinCompatibilityChecker::Initialize()
{
    if (bIsInitialized)
    {
        UE_LOG(LogPinCompatibilityChecker, Warning, TEXT("PinCompatibilityChecker already initialized"));
        return true;
    }

    UE_LOG(LogPinCompatibilityChecker, Log, TEXT("Initializing PinCompatibilityChecker..."));

    // Clear existing data
    CompatibilityRules.Empty();
    ConversionNodeMappings.Empty();
    CompatibilityStatistics.Empty();

    // Initialize compatibility rules and mappings
    InitializeCompatibilityRules();
    InitializeConversionMappings();

    bIsInitialized = true;
    
    UE_LOG(LogPinCompatibilityChecker, Log, TEXT("PinCompatibilityChecker initialized successfully"));
    LogCompatibilityActivity(TEXT("Pin compatibility checker initialized"));

    return true;
}

void FPinCompatibilityChecker::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    UE_LOG(LogPinCompatibilityChecker, Log, TEXT("Shutting down PinCompatibilityChecker..."));

    // Clear all data
    CompatibilityRules.Empty();
    ConversionNodeMappings.Empty();
    CompatibilityStatistics.Empty();

    bIsInitialized = false;
    
    UE_LOG(LogPinCompatibilityChecker, Log, TEXT("PinCompatibilityChecker shutdown complete"));
}

FPININFO FPinCompatibilityChecker::GetPinInfo(UEdGraphPin* Pin)
{
    FPININFO PinInfo;
    
    if (!bIsInitialized || !Pin)
    {
        UE_LOG(LogPinCompatibilityChecker, Error, TEXT("Cannot get pin info - checker not initialized or invalid pin"));
        return PinInfo;
    }

    return AnalyzePinType(Pin);
}

FPININFO FPinCompatibilityChecker::AnalyzePinType(UEdGraphPin* Pin)
{
    FPININFO PinInfo;
    
    if (!Pin)
    {
        return PinInfo;
    }

    // Basic pin information
    PinInfo.Pin = Pin;
    PinInfo.PinName = Pin->PinName.ToString();
    PinInfo.DisplayName = Pin->PinFriendlyName.IsEmpty() ? PinInfo.PinName : Pin->PinFriendlyName.ToString();
    PinInfo.PinCategory = Pin->PinType.PinCategory.ToString();
    PinInfo.PinSubCategory = Pin->PinType.PinSubCategory.ToString();
    
    if (Pin->PinType.PinSubCategoryObject.IsValid())
    {
        PinInfo.PinTypeObject = Pin->PinType.PinSubCategoryObject->GetName();
    }

    // Pin direction
    PinInfo.bIsInput = (Pin->Direction == EGPD_Input);
    PinInfo.bIsOutput = (Pin->Direction == EGPD_Output);

    // Pin characteristics
    PinInfo.bIsArray = Pin->PinType.IsArray();
    PinInfo.bIsReference = Pin->PinType.bIsReference;
    PinInfo.bIsOptional = Pin->bOptionalPin;
    PinInfo.bHasDefaultValue = !Pin->DefaultValue.IsEmpty();
    PinInfo.DefaultValue = Pin->DefaultValue;

    // Connection type analysis
    PinInfo.ConnectionType = AnalyzePinConnectionType(Pin);

    // Description and metadata
    PinInfo.Description = Pin->PinToolTip;
    
    // Additional metadata
    PinInfo.Metadata.Add(TEXT("Hidden"), Pin->bHidden ? TEXT("true") : TEXT("false"));
    PinInfo.Metadata.Add(TEXT("NotConnectable"), Pin->bNotConnectable ? TEXT("true") : TEXT("false"));
    PinInfo.Metadata.Add(TEXT("Advanced"), Pin->bAdvancedView ? TEXT("true") : TEXT("false"));

    LogCompatibilityActivity(FString::Printf(TEXT("Analyzed pin '%s' of type '%s'"), 
        *PinInfo.PinName, *PinInfo.PinCategory));

    return PinInfo;
}

TArray<FPININFO> FPinCompatibilityChecker::GetNodePins(UK2Node* Node)
{
    TArray<FPININFO> PinInfos;
    
    if (!bIsInitialized || !Node)
    {
        UE_LOG(LogPinCompatibilityChecker, Error, TEXT("Cannot get node pins - checker not initialized or invalid node"));
        return PinInfos;
    }

    for (UEdGraphPin* Pin : Node->Pins)
    {
        if (Pin)
        {
            FPININFO PinInfo = AnalyzePinType(Pin);
            PinInfos.Add(PinInfo);
        }
    }

    LogCompatibilityActivity(FString::Printf(TEXT("Retrieved %d pins from node '%s'"), 
        PinInfos.Num(), Node ? *Node->GetName() : TEXT("Unknown")));

    return PinInfos;
}

TArray<FPININFO> FPinCompatibilityChecker::GetInputPins(UK2Node* Node)
{
    TArray<FPININFO> InputPins;
    
    if (!bIsInitialized || !Node)
    {
        return InputPins;
    }

    for (UEdGraphPin* Pin : Node->Pins)
    {
        if (Pin && Pin->Direction == EGPD_Input)
        {
            FPININFO PinInfo = AnalyzePinType(Pin);
            InputPins.Add(PinInfo);
        }
    }

    return InputPins;
}

TArray<FPININFO> FPinCompatibilityChecker::GetOutputPins(UK2Node* Node)
{
    TArray<FPININFO> OutputPins;
    
    if (!bIsInitialized || !Node)
    {
        return OutputPins;
    }

    for (UEdGraphPin* Pin : Node->Pins)
    {
        if (Pin && Pin->Direction == EGPD_Output)
        {
            FPININFO PinInfo = AnalyzePinType(Pin);
            OutputPins.Add(PinInfo);
        }
    }

    return OutputPins;
}

FPINCOMPATIBILITYRESULT FPinCompatibilityChecker::CheckPinCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    FPINCOMPATIBILITYRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || !SourcePin || !TargetPin)
    {
        Result.ErrorMessages.Add(TEXT("Invalid pins or checker not initialized"));
        Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Check basic direction compatibility
    if (SourcePin->Direction == TargetPin->Direction)
    {
        Result.bIsCompatible = false;
        Result.CompatibilityLevel = EPinCompatibilityLevel::Incompatible;
        Result.ErrorMessages.Add(TEXT("Pins have the same direction - cannot connect"));
        Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Ensure source is output and target is input
    if (SourcePin->Direction != EGPD_Output || TargetPin->Direction != EGPD_Input)
    {
        Result.bIsCompatible = false;
        Result.CompatibilityLevel = EPinCompatibilityLevel::Incompatible;
        Result.ErrorMessages.Add(TEXT("Invalid pin direction combination"));
        Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Check if pins are already connected
    if (SourcePin->LinkedTo.Contains(TargetPin))
    {
        Result.bIsCompatible = true;
        Result.CompatibilityLevel = EPinCompatibilityLevel::PerfectMatch;
        Result.ConfidenceScore = 1.0f;
        Result.CompatibilityExplanation = TEXT("Pins are already connected");
        Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Check execution pin compatibility
    if (IsExecutionPin(SourcePin) && IsExecutionPin(TargetPin))
    {
        Result.bIsCompatible = true;
        Result.CompatibilityLevel = EPinCompatibilityLevel::PerfectMatch;
        Result.ConfidenceScore = 1.0f;
        Result.ConversionType = EPinConversionType::None;
        Result.CompatibilityExplanation = TEXT("Both pins are execution pins - perfect match");
        Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        TotalCompatibilityChecks++;
        return Result;
    }

    // Check if one is execution and other is data
    if (IsExecutionPin(SourcePin) != IsExecutionPin(TargetPin))
    {
        Result.bIsCompatible = false;
        Result.CompatibilityLevel = EPinCompatibilityLevel::Incompatible;
        Result.ErrorMessages.Add(TEXT("Cannot connect execution pin to data pin"));
        Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Check wildcard compatibility
    if (IsWildcardPin(SourcePin) || IsWildcardPin(TargetPin))
    {
        Result.bIsCompatible = true;
        Result.CompatibilityLevel = EPinCompatibilityLevel::Compatible;
        Result.ConfidenceScore = 0.9f;
        Result.ConversionType = EPinConversionType::None;
        Result.CompatibilityExplanation = TEXT("Wildcard pin can connect to any type");
        Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        TotalCompatibilityChecks++;
        return Result;
    }

    // Check basic type compatibility
    bool bBasicCompatible = CheckBasicTypeCompatibility(SourcePin->PinType.PinCategory.ToString(), TargetPin->PinType.PinCategory.ToString());
    
    if (bBasicCompatible)
    {
        // Check for perfect match
        if (SourcePin->PinType.PinCategory == TargetPin->PinType.PinCategory &&
            SourcePin->PinType.PinSubCategory == TargetPin->PinType.PinSubCategory &&
            SourcePin->PinType.PinSubCategoryObject == TargetPin->PinType.PinSubCategoryObject &&
            SourcePin->PinType.bIsArray == TargetPin->PinType.bIsArray &&
            SourcePin->PinType.bIsReference == TargetPin->PinType.bIsReference)
        {
            Result.bIsCompatible = true;
            Result.CompatibilityLevel = EPinCompatibilityLevel::PerfectMatch;
            Result.ConfidenceScore = 1.0f;
            Result.ConversionType = EPinConversionType::None;
            Result.CompatibilityExplanation = TEXT("Pin types match exactly");
        }
        else
        {
            Result.bIsCompatible = true;
            Result.CompatibilityLevel = EPinCompatibilityLevel::Compatible;
            Result.ConfidenceScore = 0.8f;
            Result.ConversionType = EPinConversionType::Implicit;
            Result.CompatibilityExplanation = TEXT("Pin types are compatible with implicit conversion");
        }
    }
    else
    {
        // Check for possible conversion
        EPinConversionType ConversionType = DetermineConversionType(SourcePin, TargetPin);
        
        if (ConversionType != EPinConversionType::None)
        {
            Result.bIsCompatible = true;
            Result.CompatibilityLevel = EPinCompatibilityLevel::RequiresConversion;
            Result.ConfidenceScore = 0.6f;
            Result.ConversionType = ConversionType;
            Result.SuggestedConversionNode = GetSuggestedConversionNode(SourcePin, TargetPin);
            Result.CompatibilityExplanation = FString::Printf(TEXT("Pins can be connected with %s conversion"), 
                *UEnum::GetValueAsString(ConversionType));
        }
        else
        {
            Result.bIsCompatible = false;
            Result.CompatibilityLevel = EPinCompatibilityLevel::Incompatible;
            Result.ConfidenceScore = 0.0f;
            Result.ConversionType = EPinConversionType::None;
            Result.ErrorMessages.Add(TEXT("Pin types are incompatible"));
        }
    }

    // Check array compatibility
    if (Result.bIsCompatible && (SourcePin->PinType.IsArray() || TargetPin->PinType.IsArray()))
    {
        bool bArrayCompatible = CheckArrayCompatibility(SourcePin, TargetPin);
        if (!bArrayCompatible)
        {
            Result.CompatibilityLevel = EPinCompatibilityLevel::RequiresConversion;
            Result.ConfidenceScore *= 0.8f;
            Result.WarningMessages.Add(TEXT("Array conversion may be required"));
        }
    }

    // Check object type compatibility for object pins
    if (Result.bIsCompatible && (SourcePin->PinType.PinCategory == UEdGraphSchema_K2::PC_Object || 
                                 TargetPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Object))
    {
        bool bObjectCompatible = CheckObjectTypeCompatibility(SourcePin, TargetPin);
        if (!bObjectCompatible)
        {
            Result.CompatibilityLevel = EPinCompatibilityLevel::RequiresConversion;
            Result.ConfidenceScore *= 0.7f;
            Result.ConversionType = EPinConversionType::Cast;
            Result.WarningMessages.Add(TEXT("Object cast may be required"));
        }
    }

    Result.AnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    LastAnalysisTimeMs = Result.AnalysisTimeMs;
    TotalCompatibilityChecks++;

    LogCompatibilityActivity(FString::Printf(TEXT("Checked compatibility between '%s' and '%s': %s"), 
        *SourcePin->PinName.ToString(), *TargetPin->PinName.ToString(), 
        Result.bIsCompatible ? TEXT("Compatible") : TEXT("Incompatible")));

    UpdateCompatibilityStatistics(TEXT("CheckCompatibility"), SourcePin->PinType.PinCategory.ToString());

    return Result;
}

bool FPinCompatibilityChecker::CanPinsConnect(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    FPINCOMPATIBILITYRESULT Result = CheckPinCompatibility(SourcePin, TargetPin);
    return Result.bIsCompatible;
}

bool FPinCompatibilityChecker::RequiresTypeConversion(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    FPINCOMPATIBILITYRESULT Result = CheckPinCompatibility(SourcePin, TargetPin);
    return Result.bIsCompatible && Result.ConversionType != EPinConversionType::None;
}

FString FPinCompatibilityChecker::GetSuggestedConversionNode(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!SourcePin || !TargetPin)
    {
        return FString();
    }

    FString SourceType = SourcePin->PinType.PinCategory.ToString();
    FString TargetType = TargetPin->PinType.PinCategory.ToString();
    FString ConversionKey = FString::Printf(TEXT("%s_to_%s"), *SourceType, *TargetType);

    if (ConversionNodeMappings.Contains(ConversionKey))
    {
        return ConversionNodeMappings[ConversionKey];
    }

    // Check for common conversions
    if (SourceType == TEXT("int") && TargetType == TEXT("float"))
    {
        return TEXT("Convert Integer to Float");
    }
    else if (SourceType == TEXT("float") && TargetType == TEXT("int"))
    {
        return TEXT("Convert Float to Integer");
    }
    else if (SourceType == TEXT("bool") && TargetType == TEXT("string"))
    {
        return TEXT("Convert Boolean to String");
    }
    else if (SourceType == TEXT("object") && TargetType == TEXT("object"))
    {
        return TEXT("Cast To");
    }

    return FString();
}

FBATCHCOMPATIBILITYRESULT FPinCompatibilityChecker::CheckBatchCompatibility(const FBATCHCOMPATIBILITYREQUEST& Request)
{
    FBATCHCOMPATIBILITYRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized)
    {
        Result.ErrorMessages.Add(TEXT("Pin compatibility checker not initialized"));
        Result.TotalAnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    Result.Results.Reserve(Request.PinPairs.Num());

    for (const auto& PinPair : Request.PinPairs)
    {
        UEdGraphPin* SourcePin = PinPair.Key;
        UEdGraphPin* TargetPin = PinPair.Value;

        if (!SourcePin || !TargetPin)
        {
            FPINCOMPATIBILITYRESULT PairResult;
            PairResult.ErrorMessages.Add(TEXT("Invalid pin pair"));
            Result.Results.Add(PairResult);
            Result.IncompatiblePairs++;
            continue;
        }

        FPINCOMPATIBILITYRESULT PairResult = CheckPinCompatibility(SourcePin, TargetPin);
        
        // Limit analysis time if requested
        if (Request.MaxAnalysisTimeMs > 0.0f && PairResult.AnalysisTimeMs > Request.MaxAnalysisTimeMs)
        {
            PairResult.WarningMessages.Add(FString::Printf(TEXT("Analysis time exceeded limit (%.2fms > %.2fms)"), 
                PairResult.AnalysisTimeMs, Request.MaxAnalysisTimeMs));
        }

        Result.Results.Add(PairResult);

        // Update counters
        if (PairResult.bIsCompatible)
        {
            if (PairResult.ConversionType != EPinConversionType::None)
            {
                Result.ConversionRequiredPairs++;
            }
            else
            {
                Result.CompatiblePairs++;
            }
        }
        else
        {
            Result.IncompatiblePairs++;
        }
    }

    Result.bSuccess = (Result.Results.Num() == Request.PinPairs.Num());
    Result.TotalAnalysisTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    TotalBatchOperations++;

    LogCompatibilityActivity(FString::Printf(TEXT("Batch compatibility check: %d pairs, %d compatible, %d incompatible, %d require conversion"), 
        Request.PinPairs.Num(), Result.CompatiblePairs, Result.IncompatiblePairs, Result.ConversionRequiredPairs));

    UpdateCompatibilityStatistics(TEXT("BatchCheck"));

    return Result;
}

TArray<UEdGraphPin*> FPinCompatibilityChecker::FindCompatiblePins(UEdGraphPin* SourcePin, const TArray<UEdGraphPin*>& CandidatePins)
{
    TArray<UEdGraphPin*> CompatiblePins;
    
    if (!bIsInitialized || !SourcePin)
    {
        return CompatiblePins;
    }

    for (UEdGraphPin* CandidatePin : CandidatePins)
    {
        if (CandidatePin && CanPinsConnect(SourcePin, CandidatePin))
        {
            CompatiblePins.Add(CandidatePin);
        }
    }

    LogCompatibilityActivity(FString::Printf(TEXT("Found %d compatible pins out of %d candidates for pin '%s'"), 
        CompatiblePins.Num(), CandidatePins.Num(), *SourcePin->PinName.ToString()));

    return CompatiblePins;
}

UEdGraphPin* FPinCompatibilityChecker::FindBestMatchingPin(UEdGraphPin* SourcePin, const TArray<UEdGraphPin*>& CandidatePins)
{
    if (!bIsInitialized || !SourcePin || CandidatePins.Num() == 0)
    {
        return nullptr;
    }

    UEdGraphPin* BestMatch = nullptr;
    float BestScore = 0.0f;

    for (UEdGraphPin* CandidatePin : CandidatePins)
    {
        if (!CandidatePin)
        {
            continue;
        }

        FPINCOMPATIBILITYRESULT Result = CheckPinCompatibility(SourcePin, CandidatePin);
        
        if (Result.bIsCompatible && Result.ConfidenceScore > BestScore)
        {
            BestMatch = CandidatePin;
            BestScore = Result.ConfidenceScore;
        }
    }

    if (BestMatch)
    {
        LogCompatibilityActivity(FString::Printf(TEXT("Found best matching pin '%s' with score %.2f for pin '%s'"), 
            *BestMatch->PinName.ToString(), BestScore, *SourcePin->PinName.ToString()));
    }

    return BestMatch;
}

bool FPinCompatibilityChecker::ValidatePinConnection(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FPINCOMPATIBILITYRESULT Result = CheckPinCompatibility(SourcePin, TargetPin);
    TotalValidationOperations++;
    
    UpdateCompatibilityStatistics(TEXT("ValidateConnection"));
    
    return Result.bIsCompatible;
}

TArray<FString> FPinCompatibilityChecker::ValidateGraphConnections(UEdGraph* Graph)
{
    TArray<FString> ValidationErrors;
    
    if (!bIsInitialized || !Graph)
    {
        ValidationErrors.Add(TEXT("Invalid graph or checker not initialized"));
        return ValidationErrors;
    }

    int32 TotalConnections = 0;
    int32 InvalidConnections = 0;

    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (!Node)
        {
            continue;
        }

        for (UEdGraphPin* Pin : Node->Pins)
        {
            if (!Pin || Pin->Direction != EGPD_Output)
            {
                continue;
            }

            for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
            {
                TotalConnections++;
                
                if (!ValidatePinConnection(Pin, LinkedPin))
                {
                    InvalidConnections++;
                    ValidationErrors.Add(FString::Printf(TEXT("Invalid connection: %s.%s -> %s.%s"), 
                        *Node->GetName(), *Pin->PinName.ToString(),
                        LinkedPin->GetOwningNode() ? *LinkedPin->GetOwningNode()->GetName() : TEXT("Unknown"),
                        *LinkedPin->PinName.ToString()));
                }
            }
        }
    }

    LogCompatibilityActivity(FString::Printf(TEXT("Validated %d connections in graph, found %d invalid"), 
        TotalConnections, InvalidConnections));

    UpdateCompatibilityStatistics(TEXT("ValidateGraph"));

    return ValidationErrors;
}

bool FPinCompatibilityChecker::HasCircularDependency(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!SourcePin || !TargetPin)
    {
        return false;
    }

    // Simple circular dependency check - would need more sophisticated implementation for complex cases
    UEdGraphNode* SourceNode = SourcePin->GetOwningNode();
    UEdGraphNode* TargetNode = TargetPin->GetOwningNode();
    
    if (!SourceNode || !TargetNode)
    {
        return false;
    }

    // Check if target node already has a path back to source node
    TSet<UEdGraphNode*> VisitedNodes;
    return CheckNodeDependency(TargetNode, SourceNode, VisitedNodes);
}

// Utility Functions

FString FPinCompatibilityChecker::GetPinTypeString(UEdGraphPin* Pin)
{
    if (!Pin)
    {
        return TEXT("Unknown");
    }

    FString TypeString = Pin->PinType.PinCategory.ToString();
    
    if (!Pin->PinType.PinSubCategory.IsNone())
    {
        TypeString += FString::Printf(TEXT(".%s"), *Pin->PinType.PinSubCategory.ToString());
    }
    
    if (Pin->PinType.PinSubCategoryObject.IsValid())
    {
        TypeString += FString::Printf(TEXT("<%s>"), *Pin->PinType.PinSubCategoryObject->GetName());
    }
    
    if (Pin->PinType.IsArray())
    {
        TypeString = FString::Printf(TEXT("Array<%s>"), *TypeString);
    }
    
    if (Pin->PinType.bIsReference)
    {
        TypeString += TEXT("&");
    }

    return TypeString;
}

bool FPinCompatibilityChecker::IsExecutionPin(UEdGraphPin* Pin)
{
    return Pin && Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Exec;
}

bool FPinCompatibilityChecker::IsDataPin(UEdGraphPin* Pin)
{
    return Pin && Pin->PinType.PinCategory != UEdGraphSchema_K2::PC_Exec;
}

bool FPinCompatibilityChecker::IsWildcardPin(UEdGraphPin* Pin)
{
    return Pin && Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Wildcard;
}

bool FPinCompatibilityChecker::IsArrayPin(UEdGraphPin* Pin)
{
    return Pin && Pin->PinType.IsArray();
}

bool FPinCompatibilityChecker::IsReferencePin(UEdGraphPin* Pin)
{
    return Pin && Pin->PinType.bIsReference;
}

FString FPinCompatibilityChecker::GetArrayElementType(UEdGraphPin* Pin)
{
    if (!Pin || !Pin->PinType.IsArray())
    {
        return FString();
    }

    return Pin->PinType.PinCategory.ToString();
}

// Statistics and Debugging

TMap<FString, FString> FPinCompatibilityChecker::GetCompatibilityStatistics()
{
    TMap<FString, FString> Stats;
    
    Stats.Add(TEXT("TotalCompatibilityChecks"), FString::FromInt(TotalCompatibilityChecks));
    Stats.Add(TEXT("TotalBatchOperations"), FString::FromInt(TotalBatchOperations));
    Stats.Add(TEXT("TotalValidationOperations"), FString::FromInt(TotalValidationOperations));
    Stats.Add(TEXT("LastAnalysisTimeMs"), FString::SanitizeFloat(LastAnalysisTimeMs));
    Stats.Add(TEXT("IsInitialized"), bIsInitialized ? TEXT("true") : TEXT("false"));
    
    for (const auto& StatPair : CompatibilityStatistics)
    {
        Stats.Add(StatPair.Key, FString::FromInt(StatPair.Value));
    }
    
    return Stats;
}

void FPinCompatibilityChecker::SetVerboseLogging(bool bEnabled)
{
    bVerboseLogging = bEnabled;
    LogCompatibilityActivity(FString::Printf(TEXT("Verbose logging %s"), bEnabled ? TEXT("enabled") : TEXT("disabled")));
}

void FPinCompatibilityChecker::SetTrackStatistics(bool bEnabled)
{
    bTrackStatistics = bEnabled;
    LogCompatibilityActivity(FString::Printf(TEXT("Statistics tracking %s"), bEnabled ? TEXT("enabled") : TEXT("disabled")));
}

// Helper Methods

EPinConnectionType FPinCompatibilityChecker::AnalyzePinConnectionType(UEdGraphPin* Pin)
{
    if (!Pin)
    {
        return EPinConnectionType::Unknown;
    }

    FString Category = Pin->PinType.PinCategory.ToString();
    
    if (Category == UEdGraphSchema_K2::PC_Exec.ToString())
    {
        return EPinConnectionType::Execution;
    }
    else if (Category == UEdGraphSchema_K2::PC_Delegate.ToString())
    {
        return EPinConnectionType::Delegate;
    }
    else if (Category == UEdGraphSchema_K2::PC_Wildcard.ToString())
    {
        return EPinConnectionType::Wildcard;
    }
    else if (Pin->PinType.IsArray())
    {
        return EPinConnectionType::Array;
    }
    else if (Pin->PinType.bIsReference)
    {
        return EPinConnectionType::Reference;
    }
    else
    {
        return EPinConnectionType::Data;
    }
}

bool FPinCompatibilityChecker::CheckBasicTypeCompatibility(const FString& SourceType, const FString& TargetType)
{
    if (SourceType == TargetType)
    {
        return true;
    }

    // Check compatibility rules
    if (CompatibilityRules.Contains(SourceType))
    {
        const TArray<FString>& CompatibleTypes = CompatibilityRules[SourceType];
        return CompatibleTypes.Contains(TargetType);
    }

    return false;
}

bool FPinCompatibilityChecker::CheckObjectTypeCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!SourcePin || !TargetPin)
    {
        return false;
    }

    // If both are object pins, check class hierarchy
    if (SourcePin->PinType.PinCategory == UEdGraphSchema_K2::PC_Object && 
        TargetPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Object)
    {
        UObject* SourceClass = SourcePin->PinType.PinSubCategoryObject.Get();
        UObject* TargetClass = TargetPin->PinType.PinSubCategoryObject.Get();
        
        if (!SourceClass || !TargetClass)
        {
            return false;
        }

        // Check if source class is compatible with target class
        if (UClass* SourceUClass = Cast<UClass>(SourceClass))
        {
            if (UClass* TargetUClass = Cast<UClass>(TargetClass))
            {
                return SourceUClass->IsChildOf(TargetUClass) || TargetUClass->IsChildOf(SourceUClass);
            }
        }
    }

    return false;
}

bool FPinCompatibilityChecker::CheckArrayCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!SourcePin || !TargetPin)
    {
        return false;
    }

    bool bSourceIsArray = SourcePin->PinType.IsArray();
    bool bTargetIsArray = TargetPin->PinType.IsArray();

    // Both arrays - check element type compatibility
    if (bSourceIsArray && bTargetIsArray)
    {
        return CheckBasicTypeCompatibility(SourcePin->PinType.PinCategory.ToString(), TargetPin->PinType.PinCategory.ToString());
    }

    // One array, one single - may require conversion
    if (bSourceIsArray != bTargetIsArray)
    {
        return true; // Possible with conversion
    }

    return true;
}

float FPinCompatibilityChecker::CalculateCompatibilityScore(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!SourcePin || !TargetPin)
    {
        return 0.0f;
    }

    float Score = 0.0f;

    // Perfect type match
    if (SourcePin->PinType.PinCategory == TargetPin->PinType.PinCategory)
    {
        Score += 0.4f;
        
        if (SourcePin->PinType.PinSubCategory == TargetPin->PinType.PinSubCategory)
        {
            Score += 0.3f;
            
            if (SourcePin->PinType.PinSubCategoryObject == TargetPin->PinType.PinSubCategoryObject)
            {
                Score += 0.2f;
            }
        }
    }

    // Array compatibility
    if (SourcePin->PinType.IsArray() == TargetPin->PinType.IsArray())
    {
        Score += 0.1f;
    }

    return FMath::Clamp(Score, 0.0f, 1.0f);
}

EPinConversionType FPinCompatibilityChecker::DetermineConversionType(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!SourcePin || !TargetPin)
    {
        return EPinConversionType::None;
    }

    FString SourceType = SourcePin->PinType.PinCategory.ToString();
    FString TargetType = TargetPin->PinType.PinCategory.ToString();

    // Check for implicit conversions
    if ((SourceType == TEXT("int") && TargetType == TEXT("float")) ||
        (SourceType == TEXT("float") && TargetType == TEXT("double")))
    {
        return EPinConversionType::Implicit;
    }

    // Check for explicit conversions
    if ((SourceType == TEXT("float") && TargetType == TEXT("int")) ||
        (SourceType == TEXT("string") && TargetType == TEXT("int")) ||
        (SourceType == TEXT("string") && TargetType == TEXT("float")))
    {
        return EPinConversionType::Explicit;
    }

    // Check for object casts
    if (SourceType == TEXT("object") && TargetType == TEXT("object"))
    {
        return EPinConversionType::Cast;
    }

    return EPinConversionType::None;
}

FString FPinCompatibilityChecker::GenerateCompatibilityExplanation(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, EPinCompatibilityLevel Level)
{
    if (!SourcePin || !TargetPin)
    {
        return TEXT("Invalid pins");
    }

    FString SourceType = GetPinTypeString(SourcePin);
    FString TargetType = GetPinTypeString(TargetPin);

    switch (Level)
    {
        case EPinCompatibilityLevel::PerfectMatch:
            return FString::Printf(TEXT("Pin types %s and %s match perfectly"), *SourceType, *TargetType);
        case EPinCompatibilityLevel::Compatible:
            return FString::Printf(TEXT("Pin types %s and %s are compatible"), *SourceType, *TargetType);
        case EPinCompatibilityLevel::RequiresConversion:
            return FString::Printf(TEXT("Pin types %s and %s require conversion"), *SourceType, *TargetType);
        case EPinCompatibilityLevel::Incompatible:
        default:
            return FString::Printf(TEXT("Pin types %s and %s are incompatible"), *SourceType, *TargetType);
    }
}

void FPinCompatibilityChecker::InitializeCompatibilityRules()
{
    // Initialize basic type compatibility rules
    CompatibilityRules.Add(TEXT("bool"), {TEXT("int"), TEXT("float"), TEXT("string")});
    CompatibilityRules.Add(TEXT("int"), {TEXT("float"), TEXT("double"), TEXT("string")});
    CompatibilityRules.Add(TEXT("float"), {TEXT("int"), TEXT("double"), TEXT("string")});
    CompatibilityRules.Add(TEXT("double"), {TEXT("float"), TEXT("string")});
    CompatibilityRules.Add(TEXT("string"), {TEXT("name"), TEXT("text")});
    CompatibilityRules.Add(TEXT("name"), {TEXT("string")});
    CompatibilityRules.Add(TEXT("text"), {TEXT("string")});
    CompatibilityRules.Add(TEXT("vector"), {TEXT("rotator"), TEXT("transform")});
    CompatibilityRules.Add(TEXT("rotator"), {TEXT("vector")});
    
    LogCompatibilityActivity(TEXT("Initialized compatibility rules"));
}

void FPinCompatibilityChecker::InitializeConversionMappings()
{
    // Initialize conversion node mappings
    ConversionNodeMappings.Add(TEXT("int_to_float"), TEXT("Convert Integer to Float"));
    ConversionNodeMappings.Add(TEXT("float_to_int"), TEXT("Convert Float to Integer"));
    ConversionNodeMappings.Add(TEXT("bool_to_string"), TEXT("Convert Boolean to String"));
    ConversionNodeMappings.Add(TEXT("int_to_string"), TEXT("Convert Integer to String"));
    ConversionNodeMappings.Add(TEXT("float_to_string"), TEXT("Convert Float to String"));
    ConversionNodeMappings.Add(TEXT("string_to_int"), TEXT("Convert String to Integer"));
    ConversionNodeMappings.Add(TEXT("string_to_float"), TEXT("Convert String to Float"));
    ConversionNodeMappings.Add(TEXT("vector_to_string"), TEXT("Convert Vector to String"));
    ConversionNodeMappings.Add(TEXT("rotator_to_string"), TEXT("Convert Rotator to String"));
    ConversionNodeMappings.Add(TEXT("object_to_object"), TEXT("Cast To"));
    
    LogCompatibilityActivity(TEXT("Initialized conversion mappings"));
}

void FPinCompatibilityChecker::LogCompatibilityActivity(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogPinCompatibilityChecker, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogPinCompatibilityChecker, Log, TEXT("%s"), *Message);
    }
}

void FPinCompatibilityChecker::UpdateCompatibilityStatistics(const FString& Operation, const FString& PinType)
{
    if (!bTrackStatistics)
    {
        return;
    }

    FString StatKey = FString::Printf(TEXT("%s_Count"), *Operation);
    if (CompatibilityStatistics.Contains(StatKey))
    {
        CompatibilityStatistics[StatKey]++;
    }
    else
    {
        CompatibilityStatistics.Add(StatKey, 1);
    }

    if (!PinType.IsEmpty())
    {
        FString TypeStatKey = FString::Printf(TEXT("%s_%s_Count"), *Operation, *PinType);
        if (CompatibilityStatistics.Contains(TypeStatKey))
        {
            CompatibilityStatistics[TypeStatKey]++;
        }
        else
        {
            CompatibilityStatistics.Add(TypeStatKey, 1);
        }
    }
}

bool FPinCompatibilityChecker::CheckNodeDependency(UEdGraphNode* StartNode, UEdGraphNode* TargetNode, TSet<UEdGraphNode*>& VisitedNodes)
{
    if (!StartNode || !TargetNode)
    {
        return false;
    }

    if (StartNode == TargetNode)
    {
        return true;
    }

    if (VisitedNodes.Contains(StartNode))
    {
        return false;
    }

    VisitedNodes.Add(StartNode);

    // Check all output pins and their connections
    for (UEdGraphPin* Pin : StartNode->Pins)
    {
        if (Pin && Pin->Direction == EGPD_Output)
        {
            for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
            {
                if (LinkedPin && LinkedPin->GetOwningNode())
                {
                    if (CheckNodeDependency(LinkedPin->GetOwningNode(), TargetNode, VisitedNodes))
                    {
                        return true;
                    }
                }
            }
        }
    }

    return false;
} 