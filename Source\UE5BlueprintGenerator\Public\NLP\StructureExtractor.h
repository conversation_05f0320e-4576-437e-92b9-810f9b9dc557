#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

DECLARE_LOG_CATEGORY_EXTERN(LogStructureExtractor, Log, All);

/**
 * Represents a blueprint variable extracted from natural language input
 */
USTRUCT()
struct FExtractedVariable
{
	GENERATED_BODY()

	/** Name of the variable */
	UPROPERTY()
	FString Name;

	/** Data type of the variable (e.g., "float", "boolean", "vector", "string") */
	UPROPERTY()
	FString Type;

	/** Default value if specified */
	UPROPERTY()
	FString DefaultValue;

	/** Whether this variable is required for the blueprint to function */
	UPROPERTY()
	bool bRequired = false;

	/** Whether this variable should be exposed to the editor */
	UPROPERTY()
	bool bEditable = false;

	/** Whether this variable should be visible in the Blueprint editor */
	UPROPERTY()
	bool bBlueprintReadOnly = false;

	/** Category for organizing variables in the editor */
	UPROPERTY()
	FString Category;

	/** Tooltip description for the variable */
	UPROPERTY()
	FString Description;

	/** Confidence score for this extraction (0.0-1.0) */
	UPROPERTY()
	float Confidence = 0.0f;

	FExtractedVariable()
	{
		Name = TEXT("");
		Type = TEXT("float");
		DefaultValue = TEXT("");
		Category = TEXT("Default");
		Description = TEXT("");
	}
};

/**
 * Represents a blueprint function extracted from natural language input
 */
USTRUCT()
struct FExtractedFunction
{
	GENERATED_BODY()

	/** Name of the function */
	UPROPERTY()
	FString Name;

	/** Input parameters for the function */
	UPROPERTY()
	TArray<FExtractedVariable> InputParameters;

	/** Output parameters (return values) for the function */
	UPROPERTY()
	TArray<FExtractedVariable> OutputParameters;

	/** Whether this is a pure function (no execution pins) */
	UPROPERTY()
	bool bPure = false;

	/** Whether this function can be called from Blueprint */
	UPROPERTY()
	bool bCallInEditor = false;

	/** Category for organizing functions */
	UPROPERTY()
	FString Category;

	/** Description of what the function does */
	UPROPERTY()
	FString Description;

	/** Estimated complexity of the function (1.0-3.0) */
	UPROPERTY()
	float Complexity = 1.0f;

	/** Confidence score for this extraction (0.0-1.0) */
	UPROPERTY()
	float Confidence = 0.0f;

	FExtractedFunction()
	{
		Name = TEXT("");
		Category = TEXT("Default");
		Description = TEXT("");
	}
};

/**
 * Represents a blueprint event extracted from natural language input
 */
USTRUCT()
struct FExtractedEvent
{
	GENERATED_BODY()

	/** Name/type of the event (e.g., "BeginPlay", "OnHit", "CustomEvent") */
	UPROPERTY()
	FString EventType;

	/** Custom name for the event if it's a custom event */
	UPROPERTY()
	FString CustomName;

	/** Parameters that the event receives */
	UPROPERTY()
	TArray<FExtractedVariable> Parameters;

	/** Whether this is a custom event or built-in event */
	UPROPERTY()
	bool bCustomEvent = false;

	/** Whether this event can be called from other blueprints */
	UPROPERTY()
	bool bCallable = false;

	/** Whether this event should be replicated in multiplayer */
	UPROPERTY()
	bool bReliable = false;

	/** Description of when/why this event triggers */
	UPROPERTY()
	FString Description;

	/** Confidence score for this extraction (0.0-1.0) */
	UPROPERTY()
	float Confidence = 0.0f;

	FExtractedEvent()
	{
		EventType = TEXT("");
		CustomName = TEXT("");
		Description = TEXT("");
	}
};

/**
 * Represents a blueprint component extracted from natural language input
 */
USTRUCT()
struct FExtractedComponent
{
	GENERATED_BODY()

	/** Name of the component instance */
	UPROPERTY()
	FString Name;

	/** Class type of the component (e.g., "StaticMeshComponent", "BoxComponent") */
	UPROPERTY()
	FString ComponentClass;

	/** Properties to set on the component */
	UPROPERTY()
	TMap<FString, FString> Properties;

	/** Whether this component should be the root component */
	UPROPERTY()
	bool bRootComponent = false;

	/** Parent component name if this should be attached to another component */
	UPROPERTY()
	FString ParentComponent;

	/** Description of the component's purpose */
	UPROPERTY()
	FString Description;

	/** Confidence score for this extraction (0.0-1.0) */
	UPROPERTY()
	float Confidence = 0.0f;

	FExtractedComponent()
	{
		Name = TEXT("");
		ComponentClass = TEXT("");
		ParentComponent = TEXT("");
		Description = TEXT("");
	}
};

/**
 * Represents a relationship between blueprint elements
 */
USTRUCT()
struct FElementRelationship
{
	GENERATED_BODY()

	/** Type of the source element (Variable, Function, Event, Component) */
	UPROPERTY()
	FString SourceType;

	/** Name of the source element */
	UPROPERTY()
	FString SourceName;

	/** Type of the target element */
	UPROPERTY()
	FString TargetType;

	/** Name of the target element */
	UPROPERTY()
	FString TargetName;

	/** Type of relationship (Uses, Calls, Triggers, Depends, Modifies) */
	UPROPERTY()
	FString RelationshipType;

	/** Additional context about the relationship */
	UPROPERTY()
	FString Context;

	/** Confidence score for this relationship (0.0-1.0) */
	UPROPERTY()
	float Confidence = 0.0f;

	FElementRelationship()
	{
		SourceType = TEXT("");
		SourceName = TEXT("");
		TargetType = TEXT("");
		TargetName = TEXT("");
		RelationshipType = TEXT("");
		Context = TEXT("");
	}
};

/**
 * Complete extracted blueprint structure from natural language input
 */
USTRUCT()
struct FExtractedBlueprintStructure
{
	GENERATED_BODY()

	/** Variables extracted from the input */
	UPROPERTY()
	TArray<FExtractedVariable> Variables;

	/** Functions extracted from the input */
	UPROPERTY()
	TArray<FExtractedFunction> Functions;

	/** Events extracted from the input */
	UPROPERTY()
	TArray<FExtractedEvent> Events;

	/** Components extracted from the input */
	UPROPERTY()
	TArray<FExtractedComponent> Components;

	/** Relationships between elements */
	UPROPERTY()
	TArray<FElementRelationship> Relationships;

	/** Overall confidence score for the extraction (0.0-1.0) */
	UPROPERTY()
	float OverallConfidence = 0.0f;

	/** Estimated implementation complexity (1.0-3.0) */
	UPROPERTY()
	float EstimatedComplexity = 1.0f;

	/** Suggested blueprint type based on extracted structure */
	UPROPERTY()
	FString SuggestedBlueprintType;

	/** Additional notes or warnings about the extraction */
	UPROPERTY()
	TArray<FString> ExtractionNotes;

	FExtractedBlueprintStructure()
	{
		SuggestedBlueprintType = TEXT("Actor");
	}
};

/**
 * Structure Extractor - Identifies and extracts blueprint elements from natural language input
 * 
 * This component analyzes processed natural language input to identify specific blueprint
 * elements such as variables, functions, events, and components, along with their
 * relationships and dependencies.
 */
class UEBLUEPRINT_GENERATOR_API FStructureExtractor
{
public:
	FStructureExtractor();
	~FStructureExtractor();

	// Main extraction methods
	
	/**
	 * Extract complete blueprint structure from natural language input
	 * @param InputText The processed natural language input
	 * @param IntentResult Intent analysis result for context
	 * @param PatternType Detected blueprint pattern type
	 * @return Complete extracted blueprint structure
	 */
	FExtractedBlueprintStructure ExtractStructure(const FString& InputText, 
		const struct FIntentAnalysisResult& IntentResult, 
		const enum class EBlueprintPatternType PatternType);

	// Individual element extraction methods
	
	/**
	 * Extract variables from the input text
	 * @param InputText The input text to analyze
	 * @return Array of extracted variables
	 */
	TArray<FExtractedVariable> ExtractVariables(const FString& InputText);

	/**
	 * Extract functions from the input text
	 * @param InputText The input text to analyze
	 * @return Array of extracted functions
	 */
	TArray<FExtractedFunction> ExtractFunctions(const FString& InputText);

	/**
	 * Extract events from the input text
	 * @param InputText The input text to analyze
	 * @return Array of extracted events
	 */
	TArray<FExtractedEvent> ExtractEvents(const FString& InputText);

	/**
	 * Extract components from the input text
	 * @param InputText The input text to analyze
	 * @return Array of extracted components
	 */
	TArray<FExtractedComponent> ExtractComponents(const FString& InputText);

	/**
	 * Analyze relationships between extracted elements
	 * @param Structure The blueprint structure to analyze
	 * @return Array of identified relationships
	 */
	TArray<FElementRelationship> AnalyzeRelationships(const FExtractedBlueprintStructure& Structure);

	// Configuration and customization
	
	/**
	 * Set the minimum confidence threshold for element extraction
	 * @param Threshold Confidence threshold (0.0-1.0)
	 */
	void SetConfidenceThreshold(float Threshold);

	/**
	 * Add custom variable type mapping
	 * @param Keywords Keywords that indicate this variable type
	 * @param VariableType The UE5 variable type to map to
	 */
	void AddVariableTypeMapping(const TArray<FString>& Keywords, const FString& VariableType);

	/**
	 * Add custom component type mapping
	 * @param Keywords Keywords that indicate this component type
	 * @param ComponentClass The UE5 component class to map to
	 */
	void AddComponentTypeMapping(const TArray<FString>& Keywords, const FString& ComponentClass);

	/**
	 * Get extraction statistics for debugging
	 * @return Map of extraction statistics
	 */
	TMap<FString, int32> GetExtractionStatistics() const;

private:
	// Configuration
	float ConfidenceThreshold;
	
	// Extraction patterns and mappings
	TMap<FString, TArray<FString>> VariablePatterns;
	TMap<FString, TArray<FString>> FunctionPatterns;
	TMap<FString, TArray<FString>> EventPatterns;
	TMap<FString, TArray<FString>> ComponentPatterns;
	TMap<FString, TArray<FString>> RelationshipPatterns;
	
	// Type mappings
	TMap<FString, FString> VariableTypeMap;
	TMap<FString, FString> ComponentTypeMap;
	TMap<FString, FString> EventTypeMap;
	
	// Statistics
	mutable TMap<FString, int32> ExtractionStats;
	
	// Initialization methods
	void InitializeVariablePatterns();
	void InitializeFunctionPatterns();
	void InitializeEventPatterns();
	void InitializeComponentPatterns();
	void InitializeRelationshipPatterns();
	void InitializeTypeMappings();
	
	// Variable extraction helpers
	FExtractedVariable ExtractVariableFromPattern(const FString& InputText, const FString& Pattern);
	FString DetermineVariableType(const FString& Context, const FString& VariableName);
	bool IsVariableRequired(const FString& Context);
	bool IsVariableEditable(const FString& Context);
	FString ExtractVariableDefaultValue(const FString& Context, const FString& VariableType);
	
	// Function extraction helpers
	FExtractedFunction ExtractFunctionFromPattern(const FString& InputText, const FString& Pattern);
	TArray<FExtractedVariable> ExtractFunctionParameters(const FString& FunctionContext, bool bInputParameters);
	bool IsPureFunction(const FString& FunctionContext);
	float EstimateFunctionComplexity(const FExtractedFunction& Function);
	
	// Event extraction helpers
	FExtractedEvent ExtractEventFromPattern(const FString& InputText, const FString& Pattern);
	FString DetermineEventType(const FString& Context);
	bool IsCustomEvent(const FString& EventContext);
	TArray<FExtractedVariable> ExtractEventParameters(const FString& EventContext);
	
	// Component extraction helpers
	FExtractedComponent ExtractComponentFromPattern(const FString& InputText, const FString& Pattern);
	FString DetermineComponentClass(const FString& Context, const FString& ComponentName);
	TMap<FString, FString> ExtractComponentProperties(const FString& ComponentContext);
	bool IsRootComponent(const FString& ComponentContext);
	
	// Relationship analysis helpers
	TArray<FElementRelationship> FindVariableUsage(const FExtractedBlueprintStructure& Structure);
	TArray<FElementRelationship> FindFunctionCalls(const FExtractedBlueprintStructure& Structure);
	TArray<FElementRelationship> FindEventTriggers(const FExtractedBlueprintStructure& Structure);
	TArray<FElementRelationship> FindComponentDependencies(const FExtractedBlueprintStructure& Structure);
	
	// Utility methods
	TArray<FString> TokenizeInput(const FString& InputText) const;
	FString NormalizeText(const FString& Text) const;
	bool MatchesPattern(const FString& Text, const FString& Pattern) const;
	float CalculateElementConfidence(const FString& ElementContext, const TArray<FString>& Keywords) const;
	FString ExtractElementName(const FString& Context, const FString& ElementType) const;
	FString GenerateElementName(const FString& ElementType, int32 Index) const;
	
	// Validation methods
	bool ValidateExtractedStructure(const FExtractedBlueprintStructure& Structure) const;
	TArray<FString> GetValidationWarnings(const FExtractedBlueprintStructure& Structure) const;
	void UpdateExtractionStatistics(const FString& ElementType, int32 Count) const;
}; 