#include "NLP/DependencyAnalyzer.h"
#include "UE5BlueprintGeneratorModule.h"
#include "Algo/TopologicalSort.h"

DEFINE_LOG_CATEGORY_EXTERN(LogDependencyAnalyzer, Log, All);
DEFINE_LOG_CATEGORY(LogDependencyAnalyzer);

FDependencyAnalyzer::FDependencyAnalyzer()
	: CircularDependencyThreshold(0.7f)
	, MaxDependencyDepth(10)
	, bEnableAutoResolution(true)
{
	InitializeDependencyStrengthThresholds();
	InitializeElementBuildTimes();
	InitializeAnalysisParameters();
	
	UE_LOG(LogDependencyAnalyzer, Log, TEXT("Dependency analyzer initialized with %d dependency types, max depth %d"), 
		DependencyStrengthThresholds.Num(), MaxDependencyDepth);
}

FDependencyAnalyzer::~FDependencyAnalyzer()
{
	UE_LOG(LogDependencyAnalyzer, Log, TEXT("Dependency analyzer destroyed"));
}

FDependencyAnalysisResult FDependencyAnalyzer::AnalyzeDependencies(const FExtractedBlueprintStructure& Structure,
	const FComplexityAnalysisResult& ComplexityResult)
{
	FDependencyAnalysisResult Result;
	
	UE_LOG(LogDependencyAnalyzer, VeryVerbose, TEXT("Analyzing dependencies for structure with %d variables, %d functions, %d events, %d components"), 
		Structure.Variables.Num(), Structure.Functions.Num(), Structure.Events.Num(), Structure.Components.Num());
	
	// Extract all dependencies
	Result.Dependencies = ExtractDependencies(Structure);
	
	// Detect circular dependencies
	Result.CircularDependencies = DetectCircularDependencies(Result.Dependencies);
	
	// Attempt auto-resolution of circular dependencies if enabled
	if (bEnableAutoResolution && Result.CircularDependencies.Num() > 0)
	{
		int32 ResolvedCount = AutoResolveCircularDependencies(Result.Dependencies, Result.CircularDependencies);
		UE_LOG(LogDependencyAnalyzer, Log, TEXT("Auto-resolved %d circular dependencies"), ResolvedCount);
	}
	
	// Generate optimized build order
	Result.BuildOrder = GenerateBuildOrder(Structure, Result.Dependencies, Result.CircularDependencies);
	
	// Calculate dependency complexity
	Result.DependencyComplexity = CalculateDependencyComplexity(Result.Dependencies, Result.CircularDependencies);
	
	// Find critical path
	Result.CriticalPath = FindCriticalPath(Result.BuildOrder);
	
	// Calculate build times
	Result.TotalBuildTime = CalculateTotalBuildTime(Result.BuildOrder, false);
	Result.ParallelBuildTime = CalculateTotalBuildTime(Result.BuildOrder, true);
	
	// Identify independent and unresolved elements
	Result.IndependentElements = FindIndependentElements(Result.BuildOrder);
	Result.UnresolvedElements = FindUnresolvedElements(Result.Dependencies, Structure);
	
	// Generate analysis warnings and suggestions
	Result.AnalysisWarnings = GenerateAnalysisWarnings(Result);
	Result.OptimizationSuggestions = GenerateOptimizationSuggestions(Result);
	
	// Validate structure
	Result.bIsValidStructure = ValidateDependencyStructure(Result);
	
	// Update statistics
	UpdateAnalysisStatistics(Result);
	
	UE_LOG(LogDependencyAnalyzer, Log, TEXT("Dependency analysis complete: %d dependencies, %d circular, complexity %.2f"), 
		Result.Dependencies.Num(), Result.CircularDependencies.Num(), Result.DependencyComplexity);
	
	return Result;
}

TArray<FBlueprintDependency> FDependencyAnalyzer::ExtractDependencies(const FExtractedBlueprintStructure& Structure)
{
	TArray<FBlueprintDependency> AllDependencies;
	
	// Extract dependencies from different element types
	TArray<FBlueprintDependency> VariableDeps = ExtractVariableDependencies(Structure);
	TArray<FBlueprintDependency> FunctionDeps = ExtractFunctionDependencies(Structure);
	TArray<FBlueprintDependency> EventDeps = ExtractEventDependencies(Structure);
	TArray<FBlueprintDependency> ComponentDeps = ExtractComponentDependencies(Structure);
	TArray<FBlueprintDependency> RelationshipDeps = ExtractRelationshipDependencies(Structure.Relationships);
	
	// Combine all dependencies
	AllDependencies.Append(VariableDeps);
	AllDependencies.Append(FunctionDeps);
	AllDependencies.Append(EventDeps);
	AllDependencies.Append(ComponentDeps);
	AllDependencies.Append(RelationshipDeps);
	
	// Apply custom dependency rules
	for (const auto& RulePair : CustomDependencyRules)
	{
		TArray<FBlueprintDependency> CustomDeps = RulePair.Value(Structure);
		AllDependencies.Append(CustomDeps);
	}
	
	// Remove duplicates and calculate dependency strengths
	TArray<FBlueprintDependency> UniqueDependencies;
	for (FBlueprintDependency& Dependency : AllDependencies)
	{
		// Calculate dependency strength
		Dependency.DependencyStrength = CalculateDependencyStrength(Dependency, Structure);
		
		// Check for duplicates
		bool bDuplicate = false;
		for (const FBlueprintDependency& ExistingDep : UniqueDependencies)
		{
			if (ExistingDep.SourceElement == Dependency.SourceElement && 
				ExistingDep.TargetElement == Dependency.TargetElement &&
				ExistingDep.DependencyType == Dependency.DependencyType)
			{
				bDuplicate = true;
				break;
			}
		}
		
		if (!bDuplicate)
		{
			UniqueDependencies.Add(Dependency);
		}
	}
	
	return UniqueDependencies;
}

TArray<FCircularDependency> FDependencyAnalyzer::DetectCircularDependencies(const TArray<FBlueprintDependency>& Dependencies)
{
	TArray<FCircularDependency> CircularDependencies;
	TArray<FString> AllElements;
	
	// Get all unique elements
	for (const FBlueprintDependency& Dependency : Dependencies)
	{
		AllElements.AddUnique(Dependency.SourceElement);
		AllElements.AddUnique(Dependency.TargetElement);
	}
	
	// Check each element for circular dependencies
	for (const FString& Element : AllElements)
	{
		TArray<FString> VisitedElements;
		TArray<FString> CurrentPath;
		
		if (HasCircularDependency(Dependencies, Element, VisitedElements, CurrentPath))
		{
			// Create circular dependency from the detected path
			FCircularDependency CircularDep = CreateCircularDependencyFromPath(CurrentPath, Dependencies);
			
			// Check if this circular dependency is already detected
			bool bAlreadyDetected = false;
			for (const FCircularDependency& ExistingCircular : CircularDependencies)
			{
				if (ExistingCircular.DependencyChain.Num() == CircularDep.DependencyChain.Num())
				{
					bool bSameChain = true;
					for (int32 i = 0; i < CircularDep.DependencyChain.Num(); i++)
					{
						if (!ExistingCircular.DependencyChain.Contains(CircularDep.DependencyChain[i]))
						{
							bSameChain = false;
							break;
						}
					}
					if (bSameChain)
					{
						bAlreadyDetected = true;
						break;
					}
				}
			}
			
			if (!bAlreadyDetected)
			{
				CircularDependencies.Add(CircularDep);
			}
		}
	}
	
	return CircularDependencies;
}

TArray<FBuildOrderInfo> FDependencyAnalyzer::GenerateBuildOrder(const FExtractedBlueprintStructure& Structure,
	const TArray<FBlueprintDependency>& Dependencies,
	const TArray<FCircularDependency>& CircularDeps)
{
	TArray<FBuildOrderInfo> BuildOrder;
	TArray<FString> AllElements = GetAllElementNames(Structure);
	
	// Perform topological sort to get initial order
	TArray<FString> SortedElements = TopologicalSort(Dependencies, AllElements);
	
	// Create build order info for each element
	for (int32 i = 0; i < SortedElements.Num(); i++)
	{
		FBuildOrderInfo BuildInfo;
		BuildInfo.ElementName = SortedElements[i];
		BuildInfo.ElementType = GetElementType(SortedElements[i], Structure);
		BuildInfo.BuildPriority = i;
		BuildInfo.EstimatedBuildTime = ElementBuildTimes.FindRef(BuildInfo.ElementType);
		
		// Find prerequisites
		for (const FBlueprintDependency& Dependency : Dependencies)
		{
			if (Dependency.SourceElement == SortedElements[i])
			{
				BuildInfo.Prerequisites.AddUnique(Dependency.TargetElement);
			}
		}
		
		// Find dependents
		for (const FBlueprintDependency& Dependency : Dependencies)
		{
			if (Dependency.TargetElement == SortedElements[i])
			{
				BuildInfo.Dependents.AddUnique(Dependency.SourceElement);
			}
		}
		
		BuildOrder.Add(BuildInfo);
	}
	
	// Assign build priorities and optimize for parallel builds
	AssignBuildPriorities(BuildOrder, Dependencies);
	OptimizeParallelBuilds(BuildOrder);
	
	return BuildOrder;
}

float FDependencyAnalyzer::CalculateDependencyComplexity(const TArray<FBlueprintDependency>& Dependencies,
	const TArray<FCircularDependency>& CircularDeps)
{
	float Complexity = 0.0f;
	
	// Base complexity from dependency count
	Complexity += Dependencies.Num() * 0.1f;
	
	// Additional complexity from dependency types
	for (const FBlueprintDependency& Dependency : Dependencies)
	{
		switch (Dependency.DependencyType)
		{
		case EDependencyType::DirectUsage:
			Complexity += 0.1f;
			break;
		case EDependencyType::FunctionCall:
			Complexity += 0.2f;
			break;
		case EDependencyType::EventTrigger:
			Complexity += 0.25f;
			break;
		case EDependencyType::VariableAccess:
			Complexity += 0.15f;
			break;
		case EDependencyType::ComponentHierarchy:
			Complexity += 0.3f;
			break;
		case EDependencyType::InterfaceImplementation:
			Complexity += 0.4f;
			break;
		case EDependencyType::Inheritance:
			Complexity += 0.5f;
			break;
		case EDependencyType::Compilation:
			Complexity += 0.2f;
			break;
		case EDependencyType::Runtime:
			Complexity += 0.3f;
			break;
		default:
			Complexity += 0.1f;
			break;
		}
		
		// Additional complexity from dependency strength
		switch (Dependency.DependencyStrength)
		{
		case EDependencyStrength::Weak:
			Complexity += 0.05f;
			break;
		case EDependencyStrength::Moderate:
			Complexity += 0.1f;
			break;
		case EDependencyStrength::Strong:
			Complexity += 0.2f;
			break;
		case EDependencyStrength::Critical:
			Complexity += 0.3f;
			break;
		}
	}
	
	// Significant complexity increase from circular dependencies
	for (const FCircularDependency& CircularDep : CircularDeps)
	{
		float CircularComplexity = CircularDep.DependencyChain.Num() * 0.5f;
		
		// Increase based on severity
		switch (CircularDep.Severity)
		{
		case EDependencyStrength::Weak:
			CircularComplexity *= 1.0f;
			break;
		case EDependencyStrength::Moderate:
			CircularComplexity *= 1.5f;
			break;
		case EDependencyStrength::Strong:
			CircularComplexity *= 2.0f;
			break;
		case EDependencyStrength::Critical:
			CircularComplexity *= 3.0f;
			break;
		}
		
		Complexity += CircularComplexity;
	}
	
	// Normalize complexity to 0-10 scale
	return FMath::Clamp(Complexity, 0.0f, 10.0f);
}

void FDependencyAnalyzer::InitializeDependencyStrengthThresholds()
{
	// Set default thresholds for different dependency types
	DependencyStrengthThresholds.Add(EDependencyType::DirectUsage, EDependencyStrength::Weak);
	DependencyStrengthThresholds.Add(EDependencyType::FunctionCall, EDependencyStrength::Moderate);
	DependencyStrengthThresholds.Add(EDependencyType::EventTrigger, EDependencyStrength::Moderate);
	DependencyStrengthThresholds.Add(EDependencyType::VariableAccess, EDependencyStrength::Weak);
	DependencyStrengthThresholds.Add(EDependencyType::ComponentHierarchy, EDependencyStrength::Strong);
	DependencyStrengthThresholds.Add(EDependencyType::InterfaceImplementation, EDependencyStrength::Strong);
	DependencyStrengthThresholds.Add(EDependencyType::Inheritance, EDependencyStrength::Critical);
	DependencyStrengthThresholds.Add(EDependencyType::Compilation, EDependencyStrength::Strong);
	DependencyStrengthThresholds.Add(EDependencyType::Runtime, EDependencyStrength::Strong);
}

void FDependencyAnalyzer::InitializeElementBuildTimes()
{
	// Set default build times for different element types (in seconds)
	ElementBuildTimes.Add(TEXT("Variable"), 0.5f);
	ElementBuildTimes.Add(TEXT("Function"), 2.0f);
	ElementBuildTimes.Add(TEXT("Event"), 1.5f);
	ElementBuildTimes.Add(TEXT("Component"), 1.0f);
}

void FDependencyAnalyzer::InitializeAnalysisParameters()
{
	// Set default analysis parameters
	CircularDependencyThreshold = 0.7f;
	MaxDependencyDepth = 10;
	bEnableAutoResolution = true;
}

TArray<FBlueprintDependency> FDependencyAnalyzer::ExtractVariableDependencies(const FExtractedBlueprintStructure& Structure)
{
	TArray<FBlueprintDependency> Dependencies;
	
	// Variables typically don't have many dependencies, but they can depend on other variables for default values
	for (const FExtractedVariable& Variable : Structure.Variables)
	{
		// Check if default value references other variables
		if (!Variable.DefaultValue.IsEmpty())
		{
			for (const FExtractedVariable& OtherVariable : Structure.Variables)
			{
				if (Variable.Name != OtherVariable.Name && Variable.DefaultValue.Contains(OtherVariable.Name))
				{
					FBlueprintDependency Dependency;
					Dependency.SourceElement = Variable.Name;
					Dependency.SourceType = TEXT("Variable");
					Dependency.TargetElement = OtherVariable.Name;
					Dependency.TargetType = TEXT("Variable");
					Dependency.DependencyType = EDependencyType::VariableAccess;
					Dependency.Description = FString::Printf(TEXT("Variable %s uses %s in default value"), *Variable.Name, *OtherVariable.Name);
					Dependency.Confidence = 0.8f;
					Dependencies.Add(Dependency);
				}
			}
		}
	}
	
	return Dependencies;
}

TArray<FBlueprintDependency> FDependencyAnalyzer::ExtractFunctionDependencies(const FExtractedBlueprintStructure& Structure)
{
	TArray<FBlueprintDependency> Dependencies;
	
	// Functions can depend on variables, other functions, and components
	for (const FExtractedFunction& Function : Structure.Functions)
	{
		// Check dependencies on variables (parameters and return values)
		for (const FExtractedVariable& InputParam : Function.InputParameters)
		{
			// Find matching variables in the structure
			for (const FExtractedVariable& Variable : Structure.Variables)
			{
				if (InputParam.Type == Variable.Type || Function.Description.Contains(Variable.Name))
				{
					FBlueprintDependency Dependency;
					Dependency.SourceElement = Function.Name;
					Dependency.SourceType = TEXT("Function");
					Dependency.TargetElement = Variable.Name;
					Dependency.TargetType = TEXT("Variable");
					Dependency.DependencyType = EDependencyType::VariableAccess;
					Dependency.Description = FString::Printf(TEXT("Function %s accesses variable %s"), *Function.Name, *Variable.Name);
					Dependency.Confidence = 0.6f;
					Dependencies.Add(Dependency);
				}
			}
		}
		
		// Check dependencies on components
		for (const FExtractedComponent& Component : Structure.Components)
		{
			if (Function.Description.Contains(Component.Name) || Function.Description.Contains(Component.ComponentClass))
			{
				FBlueprintDependency Dependency;
				Dependency.SourceElement = Function.Name;
				Dependency.SourceType = TEXT("Function");
				Dependency.TargetElement = Component.Name;
				Dependency.TargetType = TEXT("Component");
				Dependency.DependencyType = EDependencyType::DirectUsage;
				Dependency.Description = FString::Printf(TEXT("Function %s uses component %s"), *Function.Name, *Component.Name);
				Dependency.Confidence = 0.7f;
				Dependencies.Add(Dependency);
			}
		}
	}
	
	return Dependencies;
}

// ... continue with remaining implementation methods ... 