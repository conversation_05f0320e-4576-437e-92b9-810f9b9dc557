#include "AIModelConnector.h"
#include "AI/OllamaConnector.h"
#include "AI/LMStudioConnector.h"
#include "UE5BlueprintGeneratorModule.h"

DEFINE_LOG_CATEGORY(LogAIModelConnector);

// Factory Implementation
TSharedPtr<IAIModelConnector> FAIModelConnectorFactory::CreateConnector(EAIModelProvider Provider)
{
	switch (Provider)
	{
		case EAIModelProvider::Ollama:
			return MakeShareable(new FOllamaConnector());
		case EAIModelProvider::LMStudio:
			return MakeShareable(new FLMStudioConnector());
		default:
			UE_LOG(LogAIModelConnector, Error, TEXT("Unsupported AI model provider: %d"), (int32)Provider);
			return nullptr;
	}
}

TArray<EAIModelProvider> FAIModelConnectorFactory::GetSupportedProviders()
{
	return {
		EAIModelProvider::Ollama,
		EAIModelProvider::LMStudio
	};
}

FString FAIModelConnectorFactory::GetProviderDisplayName(EAIModelProvider Provider)
{
	switch (Provider)
	{
		case EAIModelProvider::Ollama:
			return TEXT("Ollama");
		case EAIModelProvider::LMStudio:
			return TEXT("LM Studio");
		default:
			return TEXT("Unknown");
	}
}

// Manager Implementation
FAIModelManager& FAIModelManager::Get()
{
	static FAIModelManager Instance;
	return Instance;
}

bool FAIModelManager::Initialize()
{
	if (bIsInitialized)
	{
		UE_LOG(LogAIModelConnector, Warning, TEXT("AI Model Manager already initialized"));
		return true;
	}

	// Set default settings
	CurrentSettings.Provider = EAIModelProvider::Ollama;
	CurrentSettings.Endpoint = TEXT("http://localhost:11434/api/generate");
	CurrentSettings.ModelName = TEXT("llama3");
	CurrentSettings.Temperature = 0.7f;
	CurrentSettings.MaxTokens = 2048;
	CurrentSettings.TopP = 0.9f;
	CurrentSettings.ContextWindow = 4096;
	CurrentSettings.bUseStreaming = false;
	CurrentSettings.bEnableCaching = true;
	CurrentSettings.SystemPrompt = TEXT("You are an expert in Unreal Engine 5 Blueprint creation. Generate detailed, efficient blueprint structures following UE5 best practices.");
	CurrentSettings.TimeoutSeconds = 30;

	// Create initial connector
	UpdateConnector();

	bIsInitialized = true;
	UE_LOG(LogAIModelConnector, Log, TEXT("AI Model Manager initialized successfully"));
	return true;
}

void FAIModelManager::Shutdown()
{
	if (!bIsInitialized)
	{
		return;
	}

	CancelAllRequests();
	CurrentConnector.Reset();
	bIsInitialized = false;
	
	UE_LOG(LogAIModelConnector, Log, TEXT("AI Model Manager shut down"));
}

void FAIModelManager::SetCurrentSettings(const FAIModelSettings& Settings)
{
	EAIModelProvider OldProvider = CurrentSettings.Provider;
	CurrentSettings = Settings;

	// Update connector if provider changed
	if (OldProvider != Settings.Provider)
	{
		UpdateConnector();
	}

	UE_LOG(LogAIModelConnector, Log, TEXT("AI Model settings updated - Provider: %s, Model: %s"), 
		*FAIModelConnectorFactory::GetProviderDisplayName(Settings.Provider), 
		*Settings.ModelName);
}

const FAIModelSettings& FAIModelManager::GetCurrentSettings() const
{
	return CurrentSettings;
}

bool FAIModelManager::SendGenerationRequest(const FAIModelRequest& Request, TFunction<void(const FAIModelResponse&)> OnResponse)
{
	if (!bIsInitialized)
	{
		UE_LOG(LogAIModelConnector, Error, TEXT("AI Model Manager not initialized"));
		FAIModelResponse ErrorResponse;
		ErrorResponse.bSuccess = false;
		ErrorResponse.ErrorMessage = TEXT("AI Model Manager not initialized");
		OnResponse(ErrorResponse);
		return false;
	}

	if (!CurrentConnector.IsValid())
	{
		UE_LOG(LogAIModelConnector, Error, TEXT("No valid connector available"));
		FAIModelResponse ErrorResponse;
		ErrorResponse.bSuccess = false;
		ErrorResponse.ErrorMessage = TEXT("No valid connector available");
		OnResponse(ErrorResponse);
		return false;
	}

	return CurrentConnector->SendRequest(Request, OnResponse);
}

bool FAIModelManager::TestCurrentConnection(TFunction<void(bool, const FString&)> OnResponse)
{
	if (!bIsInitialized)
	{
		UE_LOG(LogAIModelConnector, Error, TEXT("AI Model Manager not initialized"));
		OnResponse(false, TEXT("AI Model Manager not initialized"));
		return false;
	}

	if (!CurrentConnector.IsValid())
	{
		UE_LOG(LogAIModelConnector, Error, TEXT("No valid connector available"));
		OnResponse(false, TEXT("No valid connector available"));
		return false;
	}

	return CurrentConnector->TestConnection(CurrentSettings, OnResponse);
}

bool FAIModelManager::GetAvailableModels(TFunction<void(const TArray<FString>&)> OnResponse)
{
	if (!bIsInitialized)
	{
		UE_LOG(LogAIModelConnector, Error, TEXT("AI Model Manager not initialized"));
		TArray<FString> EmptyArray;
		OnResponse(EmptyArray);
		return false;
	}

	if (!CurrentConnector.IsValid())
	{
		UE_LOG(LogAIModelConnector, Error, TEXT("No valid connector available"));
		TArray<FString> EmptyArray;
		OnResponse(EmptyArray);
		return false;
	}

	return CurrentConnector->GetAvailableModels(CurrentSettings, OnResponse);
}

void FAIModelManager::CancelAllRequests()
{
	if (CurrentConnector.IsValid())
	{
		CurrentConnector->CancelPendingRequests();
		UE_LOG(LogAIModelConnector, Log, TEXT("Cancelled all pending requests"));
	}
}

bool FAIModelManager::IsReady() const
{
	return bIsInitialized && CurrentConnector.IsValid();
}

void FAIModelManager::UpdateConnector()
{
	// Cancel any pending requests from the old connector
	if (CurrentConnector.IsValid())
	{
		CurrentConnector->CancelPendingRequests();
	}

	// Create new connector
	CurrentConnector = FAIModelConnectorFactory::CreateConnector(CurrentSettings.Provider);
	
	if (CurrentConnector.IsValid())
	{
		UE_LOG(LogAIModelConnector, Log, TEXT("Created connector for provider: %s"), 
			*FAIModelConnectorFactory::GetProviderDisplayName(CurrentSettings.Provider));
	}
	else
	{
		UE_LOG(LogAIModelConnector, Error, TEXT("Failed to create connector for provider: %s"), 
			*FAIModelConnectorFactory::GetProviderDisplayName(CurrentSettings.Provider));
	}
} 