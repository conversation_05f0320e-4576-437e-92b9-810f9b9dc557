#include "NLP/IntentAnalyzer.h"
#include "UE5BlueprintGeneratorModule.h"
#include "Misc/Char.h"
#include "Misc/CString.h"
#include "Misc/Regex.h"

DEFINE_LOG_CATEGORY_EXTERN(LogIntentAnalyzer, Log, All);
DEFINE_LOG_CATEGORY(LogIntentAnalyzer);

FIntentAnalyzer::FIntentAnalyzer()
	: IntentThreshold(0.3f)
	, ActionThreshold(0.3f)
{
	InitializeIntentKeywords();
	InitializeActionKeywords();
	InitializeTargetKeywords();
	InitializeParameterPatterns();
	
	UE_LOG(LogIntentAnalyzer, Log, TEXT("Intent analyzer initialized with %d intent types, %d action types, %d target types"), 
		IntentKeywords.Num(), ActionKeywords.Num(), TargetKeywords.Num());
}

FIntentAnalyzer::~FIntentAnalyzer()
{
	UE_LOG(LogIntent<PERSON>nalyzer, Log, TEXT("Intent analyzer destroyed"));
}

FIntentAnalysisResult FIntentAnalyzer::AnalyzeIntent(const FString& InputText)
{
	FIntentAnalysisResult Result;
	
	if (InputText.IsEmpty())
	{
		return Result;
	}
	
	// Analyze primary intent
	TArray<TPair<EUserIntentType, float>> RankedIntents = RankIntents(InputText);
	if (RankedIntents.Num() > 0)
	{
		Result.PrimaryIntent = RankedIntents[0].Key;
		Result.IntentConfidence = RankedIntents[0].Value;
		
		// Add secondary intents
		for (int32 i = 1; i < FMath::Min(RankedIntents.Num(), 4); i++)
		{
			if (RankedIntents[i].Value >= IntentThreshold * 0.7f) // Lower threshold for secondary
			{
				Result.SecondaryIntents.Add(RankedIntents[i].Key);
			}
		}
	}
	
	// Analyze primary action
	TArray<TPair<EActionType, float>> RankedActions = RankActions(InputText);
	if (RankedActions.Num() > 0)
	{
		Result.PrimaryAction = RankedActions[0].Key;
		Result.ActionConfidence = RankedActions[0].Value;
	}
	
	// Analyze target object
	TArray<TPair<ETargetObjectType, float>> RankedTargets = RankTargets(InputText);
	if (RankedTargets.Num() > 0)
	{
		Result.TargetObject = RankedTargets[0].Key;
	}
	
	// Extract additional information
	Result.TargetName = ExtractTargetName(InputText);
	Result.Parameters = ExtractParameters(InputText);
	Result.ContextData = ExtractContextData(InputText);
	Result.bRequiresUserInput = RequiresUserInput(InputText);
	Result.EstimatedComplexity = EstimateComplexity(InputText);
	
	UE_LOG(LogIntentAnalyzer, VeryVerbose, TEXT("Analyzed intent: %d (%.2f confidence), action: %d (%.2f confidence)"), 
		(int32)Result.PrimaryIntent, Result.IntentConfidence, (int32)Result.PrimaryAction, Result.ActionConfidence);
	
	return Result;
}

EUserIntentType FIntentAnalyzer::GetPrimaryIntent(const FString& InputText)
{
	TArray<TPair<EUserIntentType, float>> RankedIntents = RankIntents(InputText);
	return RankedIntents.Num() > 0 ? RankedIntents[0].Key : EUserIntentType::Unknown;
}

EActionType FIntentAnalyzer::GetPrimaryAction(const FString& InputText)
{
	TArray<TPair<EActionType, float>> RankedActions = RankActions(InputText);
	return RankedActions.Num() > 0 ? RankedActions[0].Key : EActionType::Unknown;
}

ETargetObjectType FIntentAnalyzer::GetTargetObject(const FString& InputText)
{
	TArray<TPair<ETargetObjectType, float>> RankedTargets = RankTargets(InputText);
	return RankedTargets.Num() > 0 ? RankedTargets[0].Key : ETargetObjectType::Unknown;
}

TArray<FExtractedParameter> FIntentAnalyzer::ExtractParameters(const FString& InputText)
{
	TArray<FExtractedParameter> Parameters;
	
	if (InputText.IsEmpty())
	{
		return Parameters;
	}
	
	// Extract common parameter types
	FExtractedParameter NumericParam = ExtractNumericParameter(InputText, TEXT("value"));
	if (!NumericParam.Value.IsEmpty())
	{
		Parameters.Add(NumericParam);
	}
	
	FExtractedParameter StringParam = ExtractStringParameter(InputText, TEXT("name"));
	if (!StringParam.Value.IsEmpty())
	{
		Parameters.Add(StringParam);
	}
	
	FExtractedParameter BoolParam = ExtractBooleanParameter(InputText, TEXT("enabled"));
	if (!BoolParam.Value.IsEmpty())
	{
		Parameters.Add(BoolParam);
	}
	
	// Extract named entities (potential object names)
	TArray<FString> NamedEntities = ExtractNamedEntities(InputText);
	for (const FString& Entity : NamedEntities)
	{
		FExtractedParameter EntityParam;
		EntityParam.Name = TEXT("entity");
		EntityParam.Type = TEXT("string");
		EntityParam.Value = Entity;
		EntityParam.bRequired = false;
		EntityParam.Description = TEXT("Named entity found in input");
		Parameters.Add(EntityParam);
	}
	
	// Extract specific parameters based on context
	FString LowerInput = InputText.ToLower();
	
	// Location parameters
	if (LowerInput.Contains(TEXT("position")) || LowerInput.Contains(TEXT("location")) || LowerInput.Contains(TEXT("move")))
	{
		FExtractedParameter LocationParam;
		LocationParam.Name = TEXT("location");
		LocationParam.Type = TEXT("vector");
		LocationParam.bRequired = true;
		LocationParam.Description = TEXT("Target location or position");
		Parameters.Add(LocationParam);
	}
	
	// Rotation parameters
	if (LowerInput.Contains(TEXT("rotation")) || LowerInput.Contains(TEXT("rotate")) || LowerInput.Contains(TEXT("angle")))
	{
		FExtractedParameter RotationParam;
		RotationParam.Name = TEXT("rotation");
		RotationParam.Type = TEXT("rotator");
		RotationParam.bRequired = true;
		RotationParam.Description = TEXT("Target rotation or angle");
		Parameters.Add(RotationParam);
	}
	
	// Scale parameters
	if (LowerInput.Contains(TEXT("scale")) || LowerInput.Contains(TEXT("size")))
	{
		FExtractedParameter ScaleParam;
		ScaleParam.Name = TEXT("scale");
		ScaleParam.Type = TEXT("vector");
		ScaleParam.bRequired = false;
		ScaleParam.Description = TEXT("Scale or size factor");
		Parameters.Add(ScaleParam);
	}
	
	// Timer parameters
	if (LowerInput.Contains(TEXT("timer")) || LowerInput.Contains(TEXT("delay")) || LowerInput.Contains(TEXT("seconds")))
	{
		FExtractedParameter TimerParam;
		TimerParam.Name = TEXT("duration");
		TimerParam.Type = TEXT("float");
		TimerParam.bRequired = true;
		TimerParam.Description = TEXT("Timer duration in seconds");
		Parameters.Add(TimerParam);
	}
	
	// Speed parameters
	if (LowerInput.Contains(TEXT("speed")) || LowerInput.Contains(TEXT("velocity")))
	{
		FExtractedParameter SpeedParam;
		SpeedParam.Name = TEXT("speed");
		SpeedParam.Type = TEXT("float");
		SpeedParam.bRequired = false;
		SpeedParam.Description = TEXT("Movement or animation speed");
		Parameters.Add(SpeedParam);
	}
	
	return Parameters;
}

TMap<FString, FString> FIntentAnalyzer::ExtractContextData(const FString& InputText)
{
	TMap<FString, FString> ContextData;
	
	if (InputText.IsEmpty())
	{
		return ContextData;
	}
	
	FString LowerInput = InputText.ToLower();
	
	// Determine context type
	if (LowerInput.Contains(TEXT("player")) || LowerInput.Contains(TEXT("character")))
	{
		ContextData.Add(TEXT("context_type"), TEXT("player"));
	}
	else if (LowerInput.Contains(TEXT("enemy")) || LowerInput.Contains(TEXT("ai")) || LowerInput.Contains(TEXT("npc")))
	{
		ContextData.Add(TEXT("context_type"), TEXT("ai"));
	}
	else if (LowerInput.Contains(TEXT("ui")) || LowerInput.Contains(TEXT("interface")) || LowerInput.Contains(TEXT("menu")))
	{
		ContextData.Add(TEXT("context_type"), TEXT("ui"));
	}
	else if (LowerInput.Contains(TEXT("game")) || LowerInput.Contains(TEXT("gameplay")))
	{
		ContextData.Add(TEXT("context_type"), TEXT("gameplay");
	}
	
	// Determine urgency
	if (LowerInput.Contains(TEXT("urgent")) || LowerInput.Contains(TEXT("immediately")) || LowerInput.Contains(TEXT("asap")))
	{
		ContextData.Add(TEXT("urgency"), TEXT("high"));
	}
	else if (LowerInput.Contains(TEXT("when possible")) || LowerInput.Contains(TEXT("eventually")))
	{
		ContextData.Add(TEXT("urgency"), TEXT("low"));
	}
	else
	{
		ContextData.Add(TEXT("urgency"), TEXT("normal"));
	}
	
	// Determine scope
	if (LowerInput.Contains(TEXT("simple")) || LowerInput.Contains(TEXT("basic")) || LowerInput.Contains(TEXT("quick")))
	{
		ContextData.Add(TEXT("scope"), TEXT("simple"));
	}
	else if (LowerInput.Contains(TEXT("complex")) || LowerInput.Contains(TEXT("advanced")) || LowerInput.Contains(TEXT("detailed")))
	{
		ContextData.Add(TEXT("scope"), TEXT("complex"));
	}
	else
	{
		ContextData.Add(TEXT("scope"), TEXT("normal"));
	}
	
	// Determine performance requirements
	if (LowerInput.Contains(TEXT("fast")) || LowerInput.Contains(TEXT("performance")) || LowerInput.Contains(TEXT("optimized")))
	{
		ContextData.Add(TEXT("performance"), TEXT("high"));
	}
	else if (LowerInput.Contains(TEXT("prototype")) || LowerInput.Contains(TEXT("test")) || LowerInput.Contains(TEXT("temporary")))
	{
		ContextData.Add(TEXT("performance"), TEXT("low"));
	}
	else
	{
		ContextData.Add(TEXT("performance"), TEXT("normal"));
	}
	
	return ContextData;
}

FString FIntentAnalyzer::ExtractTargetName(const FString& InputText)
{
	if (InputText.IsEmpty())
	{
		return FString();
	}
	
	// Look for quoted strings (explicit names)
	FRegexPattern QuotedPattern(TEXT("\"([^\"]+)\""));
	FRegexMatcher QuotedMatcher(QuotedPattern, InputText);
	if (QuotedMatcher.FindNext())
	{
		return QuotedMatcher.GetCaptureGroup(1);
	}
	
	// Look for "named" or "called" patterns
	FRegexPattern NamedPattern(TEXT("(?:named|called)\\s+([A-Za-z][A-Za-z0-9_]*)"));
	FRegexMatcher NamedMatcher(NamedPattern, InputText);
	if (NamedMatcher.FindNext())
	{
		return NamedMatcher.GetCaptureGroup(1);
	}
	
	// Look for capitalized words (potential proper nouns)
	TArray<FString> Words = TokenizeInput(InputText);
	for (const FString& Word : Words)
	{
		if (Word.Len() > 2 && FChar::IsUpper(Word[0]) && !IsQuestionWord(Word) && !IsActionVerb(Word))
		{
			return Word;
		}
	}
	
	return FString();
}

float FIntentAnalyzer::CalculateIntentConfidence(const FString& InputText, EUserIntentType IntentType)
{
	if (InputText.IsEmpty())
	{
		return 0.0f;
	}
	
	const TArray<FString>* Keywords = IntentKeywords.Find(IntentType);
	if (!Keywords)
	{
		return 0.0f;
	}
	
	// Calculate confidence based on keyword matches and density
	int32 Matches = CountKeywordMatches(InputText, *Keywords);
	float Density = CalculateKeywordDensity(InputText, *Keywords);
	
	// Base confidence from matches
	float Confidence = Keywords->Num() > 0 ? (float)Matches / Keywords->Num() : 0.0f;
	
	// Boost confidence with density
	Confidence = (Confidence * 0.7f) + (Density * 0.3f);
	
	// Apply intent-specific logic
	switch (IntentType)
	{
		case EUserIntentType::Create:
			if (IsCreationIntent(InputText))
			{
				Confidence *= 1.2f;
			}
			break;
			
		case EUserIntentType::Modify:
			if (IsModificationIntent(InputText))
			{
				Confidence *= 1.2f;
			}
			break;
			
		case EUserIntentType::Debug:
			if (IsDebuggingIntent(InputText))
			{
				Confidence *= 1.2f;
			}
			break;
			
		case EUserIntentType::Optimize:
			if (IsOptimizationIntent(InputText))
			{
				Confidence *= 1.2f;
			}
			break;
			
		case EUserIntentType::Learn:
			if (IsLearningIntent(InputText))
			{
				Confidence *= 1.2f;
			}
			break;
			
		default:
			break;
	}
	
	return FMath::Clamp(Confidence, 0.0f, 1.0f);
}

float FIntentAnalyzer::CalculateActionConfidence(const FString& InputText, EActionType ActionType)
{
	if (InputText.IsEmpty())
	{
		return 0.0f;
	}
	
	const TArray<FString>* Keywords = ActionKeywords.Find(ActionType);
	if (!Keywords)
	{
		return 0.0f;
	}
	
	// Calculate confidence based on keyword matches and density
	int32 Matches = CountKeywordMatches(InputText, *Keywords);
	float Density = CalculateKeywordDensity(InputText, *Keywords);
	
	// Base confidence from matches
	float Confidence = Keywords->Num() > 0 ? (float)Matches / Keywords->Num() : 0.0f;
	
	// Boost confidence with density
	Confidence = (Confidence * 0.8f) + (Density * 0.2f);
	
	return FMath::Clamp(Confidence, 0.0f, 1.0f);
}

bool FIntentAnalyzer::RequiresUserInput(const FString& InputText)
{
	if (InputText.IsEmpty())
	{
		return false;
	}
	
	FString LowerInput = InputText.ToLower();
	
	// Check for question words
	TArray<FString> QuestionWords = { TEXT("what"), TEXT("how"), TEXT("where"), TEXT("when"), TEXT("why"), TEXT("which") };
	for (const FString& QuestionWord : QuestionWords)
	{
		if (LowerInput.Contains(QuestionWord))
		{
			return true;
		}
	}
	
	// Check for uncertainty indicators
	TArray<FString> UncertaintyWords = { TEXT("maybe"), TEXT("perhaps"), TEXT("possibly"), TEXT("might"), TEXT("could") };
	for (const FString& UncertaintyWord : UncertaintyWords)
	{
		if (LowerInput.Contains(UncertaintyWord))
		{
			return true;
		}
	}
	
	// Check for incomplete specifications
	if (LowerInput.Contains(TEXT("something")) || LowerInput.Contains(TEXT("anything")) || LowerInput.Contains(TEXT("somehow")))
	{
		return true;
	}
	
	return false;
}

float FIntentAnalyzer::EstimateComplexity(const FString& InputText)
{
	if (InputText.IsEmpty())
	{
		return 1.0f;
	}
	
	float Complexity = 1.0f;
	FString LowerInput = InputText.ToLower();
	
	// Base complexity from text length
	float LengthFactor = FMath::Clamp(InputText.Len() / 100.0f, 0.5f, 2.0f);
	Complexity *= LengthFactor;
	
	// Complexity indicators
	TArray<FString> ComplexityWords = {
		TEXT("complex"), TEXT("advanced"), TEXT("sophisticated"), TEXT("multiple"), TEXT("various"),
		TEXT("system"), TEXT("network"), TEXT("ai"), TEXT("animation"), TEXT("state machine")
	};
	
	int32 ComplexityMatches = CountKeywordMatches(InputText, ComplexityWords);
	Complexity += ComplexityMatches * 0.2f;
	
	// Simplicity indicators (reduce complexity)
	TArray<FString> SimplicityWords = {
		TEXT("simple"), TEXT("basic"), TEXT("easy"), TEXT("quick"), TEXT("straightforward")
	};
	
	int32 SimplicityMatches = CountKeywordMatches(InputText, SimplicityWords);
	Complexity -= SimplicityMatches * 0.3f;
	
	return FMath::Clamp(Complexity, 0.1f, 3.0f);
}

TArray<FString> FIntentAnalyzer::GetIntentKeywords(EUserIntentType IntentType) const
{
	const TArray<FString>* Keywords = IntentKeywords.Find(IntentType);
	return Keywords ? *Keywords : TArray<FString>();
}

TArray<FString> FIntentAnalyzer::GetActionKeywords(EActionType ActionType) const
{
	const TArray<FString>* Keywords = ActionKeywords.Find(ActionType);
	return Keywords ? *Keywords : TArray<FString>();
}

TArray<FString> FIntentAnalyzer::GetTargetKeywords(ETargetObjectType TargetType) const
{
	const TArray<FString>* Keywords = TargetKeywords.Find(TargetType);
	return Keywords ? *Keywords : TArray<FString>();
}

void FIntentAnalyzer::SetIntentThreshold(float Threshold)
{
	IntentThreshold = FMath::Clamp(Threshold, 0.0f, 1.0f);
}

void FIntentAnalyzer::SetActionThreshold(float Threshold)
{
	ActionThreshold = FMath::Clamp(Threshold, 0.0f, 1.0f);
}

void FIntentAnalyzer::AddCustomIntentKeywords(EUserIntentType IntentType, const TArray<FString>& Keywords)
{
	if (!IntentKeywords.Contains(IntentType))
	{
		IntentKeywords.Add(IntentType, TArray<FString>());
	}
	
	for (const FString& Keyword : Keywords)
	{
		IntentKeywords[IntentType].AddUnique(Keyword.ToLower());
	}
}

void FIntentAnalyzer::AddCustomActionKeywords(EActionType ActionType, const TArray<FString>& Keywords)
{
	if (!ActionKeywords.Contains(ActionType))
	{
		ActionKeywords.Add(ActionType, TArray<FString>());
	}
	
	for (const FString& Keyword : Keywords)
	{
		ActionKeywords[ActionType].AddUnique(Keyword.ToLower());
	}
}

TArray<TPair<EUserIntentType, float>> FIntentAnalyzer::RankIntents(const FString& InputText)
{
	TArray<TPair<EUserIntentType, float>> RankedIntents;
	
	// Calculate confidence for each intent type
	for (const auto& IntentPair : IntentKeywords)
	{
		float Confidence = CalculateIntentConfidence(InputText, IntentPair.Key);
		if (Confidence >= IntentThreshold)
		{
			RankedIntents.Add(TPair<EUserIntentType, float>(IntentPair.Key, Confidence));
		}
	}
	
	// Sort by confidence (highest first)
	RankedIntents.Sort([](const TPair<EUserIntentType, float>& A, const TPair<EUserIntentType, float>& B) {
		return A.Value > B.Value;
	});
	
	return RankedIntents;
}

TArray<TPair<EActionType, float>> FIntentAnalyzer::RankActions(const FString& InputText)
{
	TArray<TPair<EActionType, float>> RankedActions;
	
	// Calculate confidence for each action type
	for (const auto& ActionPair : ActionKeywords)
	{
		float Confidence = CalculateActionConfidence(InputText, ActionPair.Key);
		if (Confidence >= ActionThreshold)
		{
			RankedActions.Add(TPair<EActionType, float>(ActionPair.Key, Confidence));
		}
	}
	
	// Sort by confidence (highest first)
	RankedActions.Sort([](const TPair<EActionType, float>& A, const TPair<EActionType, float>& B) {
		return A.Value > B.Value;
	});
	
	return RankedActions;
}

TArray<TPair<ETargetObjectType, float>> FIntentAnalyzer::RankTargets(const FString& InputText)
{
	TArray<TPair<ETargetObjectType, float>> RankedTargets;
	
	// Calculate confidence for each target type
	for (const auto& TargetPair : TargetKeywords)
	{
		int32 Matches = CountKeywordMatches(InputText, TargetPair.Value);
		float Confidence = TargetPair.Value.Num() > 0 ? (float)Matches / TargetPair.Value.Num() : 0.0f;
		
		if (Confidence > 0.0f)
		{
			RankedTargets.Add(TPair<ETargetObjectType, float>(TargetPair.Key, Confidence));
		}
	}
	
	// Sort by confidence (highest first)
	RankedTargets.Sort([](const TPair<ETargetObjectType, float>& A, const TPair<ETargetObjectType, float>& B) {
		return A.Value > B.Value;
	});
	
	return RankedTargets;
}

int32 FIntentAnalyzer::CountKeywordMatches(const FString& InputText, const TArray<FString>& Keywords) const
{
	if (InputText.IsEmpty() || Keywords.Num() == 0)
	{
		return 0;
	}
	
	FString LowerInput = InputText.ToLower();
	int32 MatchCount = 0;
	
	for (const FString& Keyword : Keywords)
	{
		if (ContainsKeyword(LowerInput, Keyword))
		{
			MatchCount++;
		}
	}
	
	return MatchCount;
}

float FIntentAnalyzer::CalculateKeywordDensity(const FString& InputText, const TArray<FString>& Keywords) const
{
	if (InputText.IsEmpty() || Keywords.Num() == 0)
	{
		return 0.0f;
	}
	
	TArray<FString> Words = TokenizeInput(InputText);
	if (Words.Num() == 0)
	{
		return 0.0f;
	}
	
	int32 KeywordCount = 0;
	FString LowerInput = InputText.ToLower();
	
	for (const FString& Keyword : Keywords)
	{
		int32 SearchIndex = 0;
		while ((SearchIndex = LowerInput.Find(Keyword, SearchIndex)) != INDEX_NONE)
		{
			KeywordCount++;
			SearchIndex += Keyword.Len();
		}
	}
	
	return (float)KeywordCount / Words.Num();
}

bool FIntentAnalyzer::ContainsKeyword(const FString& InputText, const FString& Keyword) const
{
	return InputText.Contains(Keyword);
}

FExtractedParameter FIntentAnalyzer::ExtractNumericParameter(const FString& InputText, const FString& ParameterName) const
{
	FExtractedParameter Parameter;
	Parameter.Name = ParameterName;
	Parameter.Type = TEXT("float");
	
	// Look for numeric patterns
	FRegexPattern NumericPattern(TEXT("\\b(\\d+(?:\\.\\d+)?)\\b"));
	FRegexMatcher NumericMatcher(NumericPattern, InputText);
	
	if (NumericMatcher.FindNext())
	{
		Parameter.Value = NumericMatcher.GetCaptureGroup(1);
		Parameter.Description = FString::Printf(TEXT("Numeric value: %s"), *Parameter.Value);
	}
	
	return Parameter;
}

FExtractedParameter FIntentAnalyzer::ExtractStringParameter(const FString& InputText, const FString& ParameterName) const
{
	FExtractedParameter Parameter;
	Parameter.Name = ParameterName;
	Parameter.Type = TEXT("string");
	
	// Look for quoted strings
	FRegexPattern StringPattern(TEXT("\"([^\"]+)\""));
	FRegexMatcher StringMatcher(StringPattern, InputText);
	
	if (StringMatcher.FindNext())
	{
		Parameter.Value = StringMatcher.GetCaptureGroup(1);
		Parameter.Description = FString::Printf(TEXT("String value: %s"), *Parameter.Value);
	}
	
	return Parameter;
}

FExtractedParameter FIntentAnalyzer::ExtractBooleanParameter(const FString& InputText, const FString& ParameterName) const
{
	FExtractedParameter Parameter;
	Parameter.Name = ParameterName;
	Parameter.Type = TEXT("boolean");
	
	FString LowerInput = InputText.ToLower();
	
	if (LowerInput.Contains(TEXT("true")) || LowerInput.Contains(TEXT("enabled")) || LowerInput.Contains(TEXT("on")))
	{
		Parameter.Value = TEXT("true");
		Parameter.Description = TEXT("Boolean value: true");
	}
	else if (LowerInput.Contains(TEXT("false")) || LowerInput.Contains(TEXT("disabled")) || LowerInput.Contains(TEXT("off")))
	{
		Parameter.Value = TEXT("false");
		Parameter.Description = TEXT("Boolean value: false");
	}
	
	return Parameter;
}

TArray<FString> FIntentAnalyzer::ExtractNamedEntities(const FString& InputText) const
{
	TArray<FString> Entities;
	
	// Look for capitalized words that might be names
	TArray<FString> Words = TokenizeInput(InputText);
	
	for (const FString& Word : Words)
	{
		if (Word.Len() > 2 && FChar::IsUpper(Word[0]))
		{
			// Skip common words that are capitalized
			if (!IsQuestionWord(Word) && !IsActionVerb(Word) && 
				Word != TEXT("Blueprint") && Word != TEXT("Function") && Word != TEXT("Event"))
			{
				Entities.AddUnique(Word);
			}
		}
	}
	
	return Entities;
}

bool FIntentAnalyzer::IsCreationIntent(const FString& InputText) const
{
	FString LowerInput = InputText.ToLower();
	TArray<FString> CreationWords = { TEXT("create"), TEXT("make"), TEXT("build"), TEXT("generate"), TEXT("add"), TEXT("new") };
	
	for (const FString& Word : CreationWords)
	{
		if (LowerInput.Contains(Word))
		{
			return true;
		}
	}
	
	return false;
}

bool FIntentAnalyzer::IsModificationIntent(const FString& InputText) const
{
	FString LowerInput = InputText.ToLower();
	TArray<FString> ModificationWords = { TEXT("modify"), TEXT("change"), TEXT("update"), TEXT("edit"), TEXT("alter"), TEXT("adjust") };
	
	for (const FString& Word : ModificationWords)
	{
		if (LowerInput.Contains(Word))
		{
			return true;
		}
	}
	
	return false;
}

bool FIntentAnalyzer::IsDebuggingIntent(const FString& InputText) const
{
	FString LowerInput = InputText.ToLower();
	TArray<FString> DebuggingWords = { TEXT("debug"), TEXT("fix"), TEXT("error"), TEXT("problem"), TEXT("issue"), TEXT("bug") };
	
	for (const FString& Word : DebuggingWords)
	{
		if (LowerInput.Contains(Word))
		{
			return true;
		}
	}
	
	return false;
}

bool FIntentAnalyzer::IsOptimizationIntent(const FString& InputText) const
{
	FString LowerInput = InputText.ToLower();
	TArray<FString> OptimizationWords = { TEXT("optimize"), TEXT("improve"), TEXT("performance"), TEXT("faster"), TEXT("efficient") };
	
	for (const FString& Word : OptimizationWords)
	{
		if (LowerInput.Contains(Word))
		{
			return true;
		}
	}
	
	return false;
}

bool FIntentAnalyzer::IsLearningIntent(const FString& InputText) const
{
	FString LowerInput = InputText.ToLower();
	TArray<FString> LearningWords = { TEXT("learn"), TEXT("how"), TEXT("what"), TEXT("explain"), TEXT("teach"), TEXT("show") };
	
	for (const FString& Word : LearningWords)
	{
		if (LowerInput.Contains(Word))
		{
			return true;
		}
	}
	
	return false;
}

void FIntentAnalyzer::InitializeIntentKeywords()
{
	// Create intent
	IntentKeywords.Add(EUserIntentType::Create, {
		TEXT("create"), TEXT("make"), TEXT("build"), TEXT("generate"), TEXT("add"), TEXT("new"),
		TEXT("construct"), TEXT("develop"), TEXT("implement"), TEXT("design")
	});
	
	// Modify intent
	IntentKeywords.Add(EUserIntentType::Modify, {
		TEXT("modify"), TEXT("change"), TEXT("update"), TEXT("edit"), TEXT("alter"), TEXT("adjust"),
		TEXT("revise"), TEXT("refactor"), TEXT("improve"), TEXT("enhance")
	});
	
	// Debug intent
	IntentKeywords.Add(EUserIntentType::Debug, {
		TEXT("debug"), TEXT("fix"), TEXT("solve"), TEXT("troubleshoot"), TEXT("diagnose"),
		TEXT("error"), TEXT("problem"), TEXT("issue"), TEXT("bug"), TEXT("broken")
	});
	
	// Optimize intent
	IntentKeywords.Add(EUserIntentType::Optimize, {
		TEXT("optimize"), TEXT("improve"), TEXT("performance"), TEXT("faster"), TEXT("efficient"),
		TEXT("speed up"), TEXT("streamline"), TEXT("enhance"), TEXT("better")
	});
	
	// Learn intent
	IntentKeywords.Add(EUserIntentType::Learn, {
		TEXT("learn"), TEXT("how"), TEXT("what"), TEXT("explain"), TEXT("teach"), TEXT("show"),
		TEXT("understand"), TEXT("help"), TEXT("guide"), TEXT("tutorial")
	});
	
	// Convert intent
	IntentKeywords.Add(EUserIntentType::Convert, {
		TEXT("convert"), TEXT("transform"), TEXT("translate"), TEXT("port"), TEXT("migrate"),
		TEXT("change to"), TEXT("turn into"), TEXT("adapt")
	});
	
	// Fix intent
	IntentKeywords.Add(EUserIntentType::Fix, {
		TEXT("fix"), TEXT("repair"), TEXT("correct"), TEXT("resolve"), TEXT("solve"),
		TEXT("mend"), TEXT("restore"), TEXT("patch")
	});
	
	// Enhance intent
	IntentKeywords.Add(EUserIntentType::Enhance, {
		TEXT("enhance"), TEXT("upgrade"), TEXT("extend"), TEXT("expand"), TEXT("augment"),
		TEXT("improve"), TEXT("boost"), TEXT("strengthen")
	});
}

void FIntentAnalyzer::InitializeActionKeywords()
{
	// Add Component
	ActionKeywords.Add(EActionType::AddComponent, {
		TEXT("add component"), TEXT("attach"), TEXT("component"), TEXT("mesh"), TEXT("collision"),
		TEXT("camera"), TEXT("light"), TEXT("audio"), TEXT("movement")
	});
	
	// Create Function
	ActionKeywords.Add(EActionType::CreateFunction, {
		TEXT("function"), TEXT("method"), TEXT("procedure"), TEXT("calculate"), TEXT("compute"),
		TEXT("return"), TEXT("call"), TEXT("invoke")
	});
	
	// Handle Event
	ActionKeywords.Add(EActionType::HandleEvent, {
		TEXT("event"), TEXT("handle"), TEXT("respond"), TEXT("trigger"), TEXT("when"),
		TEXT("on"), TEXT("listen"), TEXT("react")
	});
	
	// Manage State
	ActionKeywords.Add(EActionType::ManageState, {
		TEXT("state"), TEXT("condition"), TEXT("status"), TEXT("mode"), TEXT("phase"),
		TEXT("switch"), TEXT("transition"), TEXT("change state")
	});
	
	// Process Data
	ActionKeywords.Add(EActionType::ProcessData, {
		TEXT("process"), TEXT("data"), TEXT("array"), TEXT("list"), TEXT("collection"),
		TEXT("filter"), TEXT("sort"), TEXT("transform"), TEXT("iterate")
	});
	
	// Setup Timer
	ActionKeywords.Add(EActionType::SetupTimer, {
		TEXT("timer"), TEXT("delay"), TEXT("wait"), TEXT("schedule"), TEXT("timeout"),
		TEXT("interval"), TEXT("periodic"), TEXT("countdown")
	});
	
	// Configure Input
	ActionKeywords.Add(EActionType::ConfigureInput, {
		TEXT("input"), TEXT("key"), TEXT("button"), TEXT("mouse"), TEXT("keyboard"),
		TEXT("controller"), TEXT("press"), TEXT("action"), TEXT("axis")
	});
	
	// Control Animation
	ActionKeywords.Add(EActionType::ControlAnimation, {
		TEXT("animation"), TEXT("animate"), TEXT("play"), TEXT("stop"), TEXT("blend"),
		TEXT("transition"), TEXT("montage"), TEXT("sequence")
	});
	
	// Implement AI
	ActionKeywords.Add(EActionType::ImplementAI, {
		TEXT("ai"), TEXT("artificial intelligence"), TEXT("behavior"), TEXT("decision"),
		TEXT("pathfinding"), TEXT("navigation"), TEXT("enemy"), TEXT("npc")
	});
	
	// Setup Networking
	ActionKeywords.Add(EActionType::SetupNetworking, {
		TEXT("network"), TEXT("multiplayer"), TEXT("server"), TEXT("client"), TEXT("replicate"),
		TEXT("rpc"), TEXT("authority"), TEXT("connection")
	});
	
	// Create UI
	ActionKeywords.Add(EActionType::CreateUI, {
		TEXT("ui"), TEXT("interface"), TEXT("widget"), TEXT("menu"), TEXT("button"),
		TEXT("panel"), TEXT("screen"), TEXT("hud"), TEXT("dialog")
	});
	
	// Add Gameplay
	ActionKeywords.Add(EActionType::AddGameplay, {
		TEXT("gameplay"), TEXT("mechanic"), TEXT("feature"), TEXT("system"), TEXT("rule"),
		TEXT("interaction"), TEXT("player"), TEXT("game")
	});
	
	// Create Utility
	ActionKeywords.Add(EActionType::CreateUtility, {
		TEXT("utility"), TEXT("helper"), TEXT("tool"), TEXT("library"), TEXT("common"),
		TEXT("shared"), TEXT("reusable"), TEXT("static")
	});
}

void FIntentAnalyzer::InitializeTargetKeywords()
{
	// Actor
	TargetKeywords.Add(ETargetObjectType::Actor, {
		TEXT("actor"), TEXT("object"), TEXT("entity"), TEXT("thing"), TEXT("item")
	});
	
	// Component
	TargetKeywords.Add(ETargetObjectType::Component, {
		TEXT("component"), TEXT("part"), TEXT("element"), TEXT("piece")
	});
	
	// Function
	TargetKeywords.Add(ETargetObjectType::Function, {
		TEXT("function"), TEXT("method"), TEXT("procedure"), TEXT("routine")
	});
	
	// Variable
	TargetKeywords.Add(ETargetObjectType::Variable, {
		TEXT("variable"), TEXT("property"), TEXT("field"), TEXT("value"), TEXT("data")
	});
	
	// Event
	TargetKeywords.Add(ETargetObjectType::Event, {
		TEXT("event"), TEXT("trigger"), TEXT("signal"), TEXT("notification")
	});
	
	// Interface
	TargetKeywords.Add(ETargetObjectType::Interface, {
		TEXT("interface"), TEXT("contract"), TEXT("protocol"), TEXT("api")
	});
	
	// Widget
	TargetKeywords.Add(ETargetObjectType::Widget, {
		TEXT("widget"), TEXT("ui"), TEXT("interface"), TEXT("control"), TEXT("element")
	});
	
	// Game Mode
	TargetKeywords.Add(ETargetObjectType::GameMode, {
		TEXT("game mode"), TEXT("gamemode"), TEXT("mode"), TEXT("rules")
	});
	
	// Player Controller
	TargetKeywords.Add(ETargetObjectType::PlayerController, {
		TEXT("player controller"), TEXT("controller"), TEXT("player"), TEXT("input")
	});
	
	// AI Controller
	TargetKeywords.Add(ETargetObjectType::AIController, {
		TEXT("ai controller"), TEXT("ai"), TEXT("bot"), TEXT("npc")
	});
	
	// Animation
	TargetKeywords.Add(ETargetObjectType::Animation, {
		TEXT("animation"), TEXT("anim"), TEXT("movement"), TEXT("motion")
	});
	
	// Material
	TargetKeywords.Add(ETargetObjectType::Material, {
		TEXT("material"), TEXT("shader"), TEXT("surface"), TEXT("texture")
	});
	
	// Sound
	TargetKeywords.Add(ETargetObjectType::Sound, {
		TEXT("sound"), TEXT("audio"), TEXT("music"), TEXT("sfx"), TEXT("voice")
	});
}

void FIntentAnalyzer::InitializeParameterPatterns()
{
	// Common parameter patterns for extraction
	ParameterPatterns.Add(TEXT("name"), TEXT("(?:named|called)\\s+([A-Za-z][A-Za-z0-9_]*)"));
	ParameterPatterns.Add(TEXT("value"), TEXT("(?:value|amount|number)\\s+(?:of\\s+)?(\\d+(?:\\.\\d+)?)"));
	ParameterPatterns.Add(TEXT("duration"), TEXT("(?:for|after|in)\\s+(\\d+(?:\\.\\d+)?)\\s*(?:seconds?|s)"));
	ParameterPatterns.Add(TEXT("position"), TEXT("(?:at|to|position)\\s+\\((\\d+(?:\\.\\d+)?),\\s*(\\d+(?:\\.\\d+)?),\\s*(\\d+(?:\\.\\d+)?)\\)"));
	ParameterPatterns.Add(TEXT("speed"), TEXT("(?:speed|velocity)\\s+(?:of\\s+)?(\\d+(?:\\.\\d+)?)"));
	
	// Required parameter indicators
	RequiredParameterIndicators = {
		TEXT("must"), TEXT("required"), TEXT("need"), TEXT("necessary"), TEXT("essential")
	};
	
	// Optional parameter indicators
	OptionalParameterIndicators = {
		TEXT("optional"), TEXT("maybe"), TEXT("possibly"), TEXT("if needed"), TEXT("can")
	};
}

TArray<FString> FIntentAnalyzer::TokenizeInput(const FString& InputText) const
{
	TArray<FString> Tokens;
	
	if (InputText.IsEmpty())
	{
		return Tokens;
	}
	
	FString CurrentToken;
	for (int32 i = 0; i < InputText.Len(); i++)
	{
		TCHAR Char = InputText[i];
		
		if (FChar::IsAlnum(Char) || Char == '\'' || Char == '-')
		{
			CurrentToken.AppendChar(Char);
		}
		else
		{
			if (!CurrentToken.IsEmpty())
			{
				Tokens.Add(CurrentToken);
			}
			CurrentToken.Empty();
		}
	}
	
	// Add the last token if any
	if (!CurrentToken.IsEmpty())
	{
		Tokens.Add(CurrentToken);
	}
	
	return Tokens;
}

FString FIntentAnalyzer::NormalizeText(const FString& Text) const
{
	return Text.ToLower().TrimStartAndEnd();
}

bool FIntentAnalyzer::IsQuestionWord(const FString& Word) const
{
	TArray<FString> QuestionWords = { TEXT("What"), TEXT("How"), TEXT("Where"), TEXT("When"), TEXT("Why"), TEXT("Which"), TEXT("Who") };
	return QuestionWords.Contains(Word);
}

bool FIntentAnalyzer::IsActionVerb(const FString& Word) const
{
	TArray<FString> ActionVerbs = { TEXT("Create"), TEXT("Make"), TEXT("Build"), TEXT("Add"), TEXT("Remove"), TEXT("Delete"), TEXT("Modify"), TEXT("Change") };
	return ActionVerbs.Contains(Word);
}

bool FIntentAnalyzer::IsTargetNoun(const FString& Word) const
{
	TArray<FString> TargetNouns = { TEXT("Actor"), TEXT("Component"), TEXT("Function"), TEXT("Variable"), TEXT("Event"), TEXT("Widget") };
	return TargetNouns.Contains(Word);
} 