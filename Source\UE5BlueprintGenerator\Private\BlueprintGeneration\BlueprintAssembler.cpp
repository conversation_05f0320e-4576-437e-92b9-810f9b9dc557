#include "BlueprintGeneration/BlueprintAssembler.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"

DEFINE_LOG_CATEGORY(LogBlueprintAssembler);

FBlueprintAssembler::FBlueprintAssembler()
    : ExecutionMode(EAssemblyExecutionMode::Synchronous)
    , NodeCreationStrategy(ENodeCreationStrategy::Optimized)
    , bIsAssemblyInProgress(false)
    , bIsAssemblyPaused(false)
    , bAsyncExecutionRequested(false)
{
    InitializeComponents();
}

FBlueprintAssembler::~FBlueprintAssembler()
{
    ShutdownComponents();
}

bool FBlueprintAssembler::ExecuteAssembly(const FAssemblyRequest& Request, FAssemblyExecutionResult& OutResult)
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Starting blueprint assembly execution"));

    if (bIsAssemblyInProgress)
    {
        OutResult.bSuccess = false;
        OutResult.ExecutionErrors.Add(TEXT("Assembly already in progress"));
        return false;
    }

    // Create assembly plan
    FAssemblyExecutionPlan Plan;
    if (!CreateAssemblyPlan(Request.ExtractedStructure, Request.DependencyAnalysis, Plan))
    {
        OutResult.bSuccess = false;
        OutResult.ExecutionErrors.Add(TEXT("Failed to create assembly plan"));
        return false;
    }

    // Validate plan
    if (!ValidateAssemblyPlan(Plan))
    {
        OutResult.bSuccess = false;
        OutResult.ExecutionErrors.Add(TEXT("Assembly plan validation failed"));
        return false;
    }

    // Optimize plan
    if (!OptimizeAssemblyPlan(Plan))
    {
        UE_LOG(LogBlueprintAssembler, Warning, TEXT("Assembly plan optimization failed, continuing with unoptimized plan"));
    }

    // Create execution context
    FAssemblyExecutionContext Context;
    Context.Blueprint = Request.BlueprintRequest.CreatedBlueprint;
    Context.Config = Request.Config;
    Context.AssemblyStartTime = FDateTime::Now();

    // Execute plan
    return ExecuteAssemblyPlan(Plan, Context, OutResult);
}

bool FBlueprintAssembler::ExecuteAssemblyPlan(const FAssemblyExecutionPlan& Plan, const FAssemblyExecutionContext& Context, FAssemblyExecutionResult& OutResult)
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Executing assembly plan: %s"), *Plan.PlanId);

    bIsAssemblyInProgress = true;
    CurrentPlan = Plan;
    CurrentContext = Context;
    ExecutionStartTime = FDateTime::Now();

    StartExecutionTimer();
    BroadcastExecutionStarted(Plan.PlanId);

    bool bSuccess = true;
    int32 ExecutedCount = 0;
    int32 FailedCount = 0;

    // Execute instructions in order
    for (const FString& InstructionId : Plan.ExecutionOrder)
    {
        if (bIsAssemblyPaused)
        {
            UE_LOG(LogBlueprintAssembler, Log, TEXT("Assembly paused at instruction: %s"), *InstructionId);
            break;
        }

        // Find instruction
        const FNodeAssemblyInstruction* Instruction = nullptr;
        for (const FNodeAssemblyInstruction& Inst : Plan.Instructions)
        {
            if (Inst.InstructionId == InstructionId)
            {
                Instruction = &Inst;
                break;
            }
        }

        if (!Instruction)
        {
            UE_LOG(LogBlueprintAssembler, Error, TEXT("Instruction not found: %s"), *InstructionId);
            FailedCount++;
            bSuccess = false;
            continue;
        }

        // Execute instruction
        bool bInstructionSuccess = ExecuteInstruction(*Instruction, CurrentContext);
        
        if (bInstructionSuccess)
        {
            ExecutedCount++;
            BroadcastInstructionExecuted(InstructionId, true);
        }
        else
        {
            FailedCount++;
            bSuccess = false;
            BroadcastInstructionExecuted(InstructionId, false);
            
            if (Context.Config.bEnableErrorRecovery)
            {
                if (AttemptInstructionRecovery(*Instruction, CurrentContext))
                {
                    UE_LOG(LogBlueprintAssembler, Log, TEXT("Instruction recovery successful: %s"), *InstructionId);
                    ExecutedCount++;
                    bSuccess = true;
                }
            }
        }
    }

    // Establish connections after all nodes are created
    if (bSuccess && !EstablishConnections(Plan, CurrentContext))
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Failed to establish connections"));
        bSuccess = false;
    }

    StopExecutionTimer();

    // Prepare result
    OutResult.bSuccess = bSuccess;
    OutResult.ExecutedInstructions = ExecutedCount;
    OutResult.FailedInstructions = FailedCount;
    OutResult.CreatedNodes = CurrentContext.CreatedNodes;
    UpdatePerformanceMetrics(OutResult);

    BroadcastExecutionCompleted(OutResult);

    bIsAssemblyInProgress = false;
    bIsAssemblyPaused = false;

    UE_LOG(LogBlueprintAssembler, Log, TEXT("Assembly execution completed. Success: %s, Executed: %d, Failed: %d"), 
           bSuccess ? TEXT("true") : TEXT("false"), ExecutedCount, FailedCount);

    return bSuccess;
}

bool FBlueprintAssembler::CreateAssemblyPlan(const FExtractedBlueprintStructure& Structure, const FDependencyAnalysisResult& Dependencies, FAssemblyExecutionPlan& OutPlan)
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Creating assembly plan"));

    OutPlan.PlanId = GeneratePlanId();
    OutPlan.Instructions.Empty();

    TArray<FNodeAssemblyInstruction> AllInstructions;

    // Create instructions for each element type
    if (!CreateVariableInstructions(Structure.Variables, AllInstructions))
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Failed to create variable instructions"));
        return false;
    }

    if (!CreateFunctionInstructions(Structure.Functions, AllInstructions))
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Failed to create function instructions"));
        return false;
    }

    if (!CreateEventInstructions(Structure.Events, AllInstructions))
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Failed to create event instructions"));
        return false;
    }

    if (!CreateComponentInstructions(Structure.Components, AllInstructions))
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Failed to create component instructions"));
        return false;
    }

    OutPlan.Instructions = AllInstructions;

    // Resolve execution order based on dependencies
    if (!ResolveDependencyOrder(OutPlan.Instructions, OutPlan.ExecutionOrder))
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Failed to resolve dependency order"));
        return false;
    }

    // Calculate complexity and estimated time
    OutPlan.ComplexityScore = static_cast<float>(AllInstructions.Num()) * 0.1f;
    OutPlan.EstimatedExecutionTime = static_cast<float>(AllInstructions.Num()) * 0.5f; // 0.5 seconds per instruction

    OutPlan.bIsValid = true;

    UE_LOG(LogBlueprintAssembler, Log, TEXT("Assembly plan created with %d instructions"), AllInstructions.Num());

    return true;
}

bool FBlueprintAssembler::CreateVariableInstructions(const TArray<FExtractedVariable>& Variables, TArray<FNodeAssemblyInstruction>& OutInstructions)
{
    for (int32 i = 0; i < Variables.Num(); ++i)
    {
        const FExtractedVariable& Variable = Variables[i];

        // Create variable get instruction
        FNodeAssemblyInstruction GetInstruction;
        GetInstruction.InstructionId = GenerateInstructionId(TEXT("VariableGet"), i);
        GetInstruction.NodeType = TEXT("K2Node_VariableGet");
        GetInstruction.Priority = EAssemblyPriority::Normal;
        GetInstruction.Properties.Add(TEXT("VariableName"), Variable.Name);
        GetInstruction.Properties.Add(TEXT("VariableType"), Variable.Type);
        OutInstructions.Add(GetInstruction);

        // Create variable set instruction
        FNodeAssemblyInstruction SetInstruction;
        SetInstruction.InstructionId = GenerateInstructionId(TEXT("VariableSet"), i);
        SetInstruction.NodeType = TEXT("K2Node_VariableSet");
        SetInstruction.Priority = EAssemblyPriority::Normal;
        SetInstruction.Properties.Add(TEXT("VariableName"), Variable.Name);
        SetInstruction.Properties.Add(TEXT("VariableType"), Variable.Type);
        if (!Variable.DefaultValue.IsEmpty())
        {
            SetInstruction.Properties.Add(TEXT("DefaultValue"), Variable.DefaultValue);
        }
        OutInstructions.Add(SetInstruction);
    }

    return true;
}

bool FBlueprintAssembler::CreateFunctionInstructions(const TArray<FExtractedFunction>& Functions, TArray<FNodeAssemblyInstruction>& OutInstructions)
{
    for (int32 i = 0; i < Functions.Num(); ++i)
    {
        const FExtractedFunction& Function = Functions[i];

        FNodeAssemblyInstruction Instruction;
        Instruction.InstructionId = GenerateInstructionId(TEXT("Function"), i);
        Instruction.NodeType = TEXT("K2Node_CallFunction");
        Instruction.Priority = EAssemblyPriority::High;
        Instruction.Properties.Add(TEXT("FunctionName"), Function.Name);
        
        // Add parameters
        for (const FString& Parameter : Function.Parameters)
        {
            Instruction.Properties.Add(TEXT("Parameter"), Parameter);
        }

        // Add return values
        for (const FString& ReturnValue : Function.ReturnValues)
        {
            Instruction.Properties.Add(TEXT("ReturnValue"), ReturnValue);
        }

        OutInstructions.Add(Instruction);
    }

    return true;
}

bool FBlueprintAssembler::CreateEventInstructions(const TArray<FExtractedEvent>& Events, TArray<FNodeAssemblyInstruction>& OutInstructions)
{
    for (int32 i = 0; i < Events.Num(); ++i)
    {
        const FExtractedEvent& Event = Events[i];

        FNodeAssemblyInstruction Instruction;
        Instruction.InstructionId = GenerateInstructionId(TEXT("Event"), i);
        Instruction.NodeType = TEXT("K2Node_Event");
        Instruction.Priority = EAssemblyPriority::Critical;
        Instruction.Properties.Add(TEXT("EventName"), Event.Name);
        
        // Add parameters
        for (const FString& Parameter : Event.Parameters)
        {
            Instruction.Properties.Add(TEXT("Parameter"), Parameter);
        }

        OutInstructions.Add(Instruction);
    }

    return true;
}

bool FBlueprintAssembler::CreateComponentInstructions(const TArray<FExtractedComponent>& Components, TArray<FNodeAssemblyInstruction>& OutInstructions)
{
    for (int32 i = 0; i < Components.Num(); ++i)
    {
        const FExtractedComponent& Component = Components[i];

        FNodeAssemblyInstruction Instruction;
        Instruction.InstructionId = GenerateInstructionId(TEXT("Component"), i);
        Instruction.NodeType = TEXT("K2Node_AddComponent");
        Instruction.Priority = EAssemblyPriority::High;
        Instruction.Properties.Add(TEXT("ComponentClass"), Component.ComponentClass);
        Instruction.Properties.Add(TEXT("ComponentName"), Component.Name);

        // Add component properties
        for (const auto& Property : Component.Properties)
        {
            Instruction.Properties.Add(Property.Key, Property.Value);
        }

        OutInstructions.Add(Instruction);
    }

    return true;
}

bool FBlueprintAssembler::ValidateAssemblyPlan(const FAssemblyExecutionPlan& Plan)
{
    if (Plan.Instructions.Num() == 0)
    {
        UE_LOG(LogBlueprintAssembler, Warning, TEXT("Assembly plan has no instructions"));
        return false;
    }

    if (Plan.ExecutionOrder.Num() != Plan.Instructions.Num())
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Execution order count mismatch"));
        return false;
    }

    // Validate each instruction
    for (const FNodeAssemblyInstruction& Instruction : Plan.Instructions)
    {
        if (!ValidateInstruction(Instruction))
        {
            UE_LOG(LogBlueprintAssembler, Error, TEXT("Invalid instruction: %s"), *Instruction.InstructionId);
            return false;
        }
    }

    return true;
}

bool FBlueprintAssembler::OptimizeAssemblyPlan(FAssemblyExecutionPlan& Plan)
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Optimizing assembly plan"));

    // Optimize execution order
    if (!OptimizeExecutionOrder(Plan))
    {
        UE_LOG(LogBlueprintAssembler, Warning, TEXT("Failed to optimize execution order"));
        return false;
    }

    // Recalculate estimated time based on optimization
    Plan.EstimatedExecutionTime *= 0.8f; // Assume 20% improvement from optimization

    return true;
}

bool FBlueprintAssembler::OptimizeExecutionOrder(FAssemblyExecutionPlan& Plan)
{
    // Group instructions by priority
    TMap<EAssemblyPriority, TArray<FString>> PriorityGroups;
    
    for (const FNodeAssemblyInstruction& Instruction : Plan.Instructions)
    {
        if (!PriorityGroups.Contains(Instruction.Priority))
        {
            PriorityGroups.Add(Instruction.Priority, TArray<FString>());
        }
        PriorityGroups[Instruction.Priority].Add(Instruction.InstructionId);
    }

    // Rebuild execution order by priority
    Plan.ExecutionOrder.Empty();
    
    // Critical first
    if (PriorityGroups.Contains(EAssemblyPriority::Critical))
    {
        Plan.ExecutionOrder.Append(PriorityGroups[EAssemblyPriority::Critical]);
    }
    
    // High priority
    if (PriorityGroups.Contains(EAssemblyPriority::High))
    {
        Plan.ExecutionOrder.Append(PriorityGroups[EAssemblyPriority::High]);
    }
    
    // Normal priority
    if (PriorityGroups.Contains(EAssemblyPriority::Normal))
    {
        Plan.ExecutionOrder.Append(PriorityGroups[EAssemblyPriority::Normal]);
    }
    
    // Low priority
    if (PriorityGroups.Contains(EAssemblyPriority::Low))
    {
        Plan.ExecutionOrder.Append(PriorityGroups[EAssemblyPriority::Low]);
    }
    
    // Deferred last
    if (PriorityGroups.Contains(EAssemblyPriority::Deferred))
    {
        Plan.ExecutionOrder.Append(PriorityGroups[EAssemblyPriority::Deferred]);
    }

    return true;
}

bool FBlueprintAssembler::ResolveDependencyOrder(TArray<FNodeAssemblyInstruction>& Instructions, TArray<FString>& OutExecutionOrder)
{
    OutExecutionOrder.Empty();

    // Simple dependency resolution - for now just use the order they were added
    // In a full implementation, this would use topological sorting
    for (const FNodeAssemblyInstruction& Instruction : Instructions)
    {
        OutExecutionOrder.Add(Instruction.InstructionId);
    }

    return true;
}

bool FBlueprintAssembler::ExecuteInstruction(const FNodeAssemblyInstruction& Instruction, FAssemblyExecutionContext& Context)
{
    UE_LOG(LogBlueprintAssembler, Verbose, TEXT("Executing instruction: %s"), *Instruction.InstructionId);

    if (!ValidateInstruction(Instruction))
    {
        HandleExecutionError(TEXT("Invalid instruction"), Instruction.InstructionId);
        return false;
    }

    if (!ValidateExecutionContext(Context))
    {
        HandleExecutionError(TEXT("Invalid execution context"), Instruction.InstructionId);
        return false;
    }

    // Create node from instruction
    TObjectPtr<UK2Node> CreatedNode = nullptr;
    if (!CreateNodeFromInstruction(Instruction, Context, CreatedNode))
    {
        HandleExecutionError(TEXT("Failed to create node"), Instruction.InstructionId);
        return false;
    }

    // Configure node properties
    if (!ConfigureNodeProperties(Instruction, CreatedNode))
    {
        HandleExecutionError(TEXT("Failed to configure node properties"), Instruction.InstructionId);
        return false;
    }

    // Set node position
    if (!SetNodePosition(Instruction, CreatedNode))
    {
        HandleExecutionError(TEXT("Failed to set node position"), Instruction.InstructionId);
        return false;
    }

    // Store created node
    Context.CreatedNodes.Add(Instruction.InstructionId, CreatedNode);
    Context.NodeCreationOrder.Add(Instruction.InstructionId);

    return true;
}

bool FBlueprintAssembler::CreateNodeFromInstruction(const FNodeAssemblyInstruction& Instruction, FAssemblyExecutionContext& Context, TObjectPtr<UK2Node>& OutNode)
{
    if (!NodeFactory.IsValid())
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Node Factory not initialized"));
        return false;
    }

    // Create node using NodeFactory
    FNodeCreationInfo CreationInfo;
    CreationInfo.NodeType = Instruction.NodeType;
    CreationInfo.Position = Instruction.Position;
    CreationInfo.Graph = Context.CurrentGraph;

    FNodeCreationResult CreationResult;
    bool bSuccess = NodeFactory->CreateNode(CreationInfo, CreationResult);

    if (bSuccess && CreationResult.CreatedNode)
    {
        OutNode = CreationResult.CreatedNode;
        return true;
    }

    UE_LOG(LogBlueprintAssembler, Error, TEXT("Failed to create node of type: %s"), *Instruction.NodeType);
    return false;
}

bool FBlueprintAssembler::ConfigureNodeProperties(const FNodeAssemblyInstruction& Instruction, TObjectPtr<UK2Node> Node)
{
    if (!PropertyManager.IsValid() || !Node)
    {
        return false;
    }

    // Configure properties using PropertyManager
    for (const auto& Property : Instruction.Properties)
    {
        FPropertyConfigurationRequest ConfigRequest;
        ConfigRequest.Node = Node;
        ConfigRequest.PropertyName = Property.Key;
        ConfigRequest.PropertyValue = Property.Value;

        FPropertyConfigurationResult ConfigResult;
        if (!PropertyManager->ConfigureProperty(ConfigRequest, ConfigResult))
        {
            UE_LOG(LogBlueprintAssembler, Warning, TEXT("Failed to configure property %s on node %s"), 
                   *Property.Key, *Instruction.InstructionId);
        }
    }

    return true;
}

bool FBlueprintAssembler::SetNodePosition(const FNodeAssemblyInstruction& Instruction, TObjectPtr<UK2Node> Node)
{
    if (!Node)
    {
        return false;
    }

    // Set node position
    Node->NodePosX = static_cast<int32>(Instruction.Position.X);
    Node->NodePosY = static_cast<int32>(Instruction.Position.Y);

    return true;
}

bool FBlueprintAssembler::EstablishConnections(const FAssemblyExecutionPlan& Plan, FAssemblyExecutionContext& Context)
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Establishing connections"));

    if (!ConnectionManager.IsValid())
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Connection Manager not initialized"));
        return false;
    }

    // Process connections for each instruction
    for (const FNodeAssemblyInstruction& Instruction : Plan.Instructions)
    {
        for (const FString& ConnectionString : Instruction.Connections)
        {
            FString SourceNode, SourcePin, TargetNode, TargetPin;
            if (!ParseConnectionString(ConnectionString, SourceNode, SourcePin, TargetNode, TargetPin))
            {
                UE_LOG(LogBlueprintAssembler, Warning, TEXT("Failed to parse connection string: %s"), *ConnectionString);
                continue;
            }

            // Find source and target nodes
            TObjectPtr<UK2Node>* SourceNodePtr = Context.CreatedNodes.Find(SourceNode);
            TObjectPtr<UK2Node>* TargetNodePtr = Context.CreatedNodes.Find(TargetNode);

            if (!SourceNodePtr || !TargetNodePtr || !*SourceNodePtr || !*TargetNodePtr)
            {
                UE_LOG(LogBlueprintAssembler, Warning, TEXT("Source or target node not found for connection: %s"), *ConnectionString);
                continue;
            }

            // Establish connection using ConnectionManager
            FConnectionRequest ConnectionRequest;
            ConnectionRequest.SourceNode = *SourceNodePtr;
            ConnectionRequest.SourcePinName = SourcePin;
            ConnectionRequest.TargetNode = *TargetNodePtr;
            ConnectionRequest.TargetPinName = TargetPin;

            FConnectionResult ConnectionResult;
            if (!ConnectionManager->EstablishConnection(ConnectionRequest, ConnectionResult))
            {
                UE_LOG(LogBlueprintAssembler, Warning, TEXT("Failed to establish connection: %s"), *ConnectionString);
            }
        }
    }

    return true;
}

bool FBlueprintAssembler::ParseConnectionString(const FString& ConnectionString, FString& OutSourceNode, FString& OutSourcePin, FString& OutTargetNode, FString& OutTargetPin)
{
    // Expected format: "SourceNode.SourcePin->TargetNode.TargetPin"
    FString LeftSide, RightSide;
    if (!ConnectionString.Split(TEXT("->"), &LeftSide, &RightSide))
    {
        return false;
    }

    if (!LeftSide.Split(TEXT("."), &OutSourceNode, &OutSourcePin))
    {
        return false;
    }

    if (!RightSide.Split(TEXT("."), &OutTargetNode, &OutTargetPin))
    {
        return false;
    }

    return true;
}

bool FBlueprintAssembler::HandleExecutionError(const FString& ErrorMessage, const FString& InstructionId)
{
    UE_LOG(LogBlueprintAssembler, Error, TEXT("Execution error in instruction %s: %s"), *InstructionId, *ErrorMessage);
    BroadcastExecutionError(ErrorMessage, InstructionId);
    return false;
}

bool FBlueprintAssembler::AttemptInstructionRecovery(const FNodeAssemblyInstruction& Instruction, FAssemblyExecutionContext& Context)
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Attempting instruction recovery: %s"), *Instruction.InstructionId);
    
    // Basic recovery strategies could be implemented here
    // For now, we just log the attempt
    
    return false; // Recovery not implemented yet
}

bool FBlueprintAssembler::ValidateExecutionContext(const FAssemblyExecutionContext& Context)
{
    if (!Context.Blueprint)
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Execution context has no blueprint"));
        return false;
    }

    return true;
}

bool FBlueprintAssembler::ValidateInstruction(const FNodeAssemblyInstruction& Instruction)
{
    if (Instruction.InstructionId.IsEmpty())
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Instruction has empty ID"));
        return false;
    }

    if (Instruction.NodeType.IsEmpty())
    {
        UE_LOG(LogBlueprintAssembler, Error, TEXT("Instruction has empty node type"));
        return false;
    }

    return true;
}

FString FBlueprintAssembler::GenerateInstructionId(const FString& NodeType, int32 Index)
{
    return FString::Printf(TEXT("%s_%d"), *NodeType, Index);
}

FString FBlueprintAssembler::GeneratePlanId()
{
    return FString::Printf(TEXT("Plan_%s"), *FDateTime::Now().ToString());
}

void FBlueprintAssembler::StartExecutionTimer()
{
    ExecutionStartTime = FDateTime::Now();
}

void FBlueprintAssembler::StopExecutionTimer()
{
    // Timer stopped, execution time will be calculated in UpdatePerformanceMetrics
}

void FBlueprintAssembler::UpdatePerformanceMetrics(FAssemblyExecutionResult& Result)
{
    FTimespan ExecutionTime = FDateTime::Now() - ExecutionStartTime;
    Result.ActualExecutionTime = ExecutionTime.GetTotalSeconds();
    
    Result.PerformanceMetrics.Add(TEXT("ExecutionTime"), Result.ActualExecutionTime);
    Result.PerformanceMetrics.Add(TEXT("InstructionsPerSecond"), 
                                  Result.ExecutedInstructions / FMath::Max(Result.ActualExecutionTime, 0.001f));
}

void FBlueprintAssembler::InitializeComponents()
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Initializing assembler components"));

    NodeFactory = MakeShared<FNodeFactory>();
    NodeTypeRegistry = MakeShared<FNodeTypeRegistry>();
    PropertyManager = MakeShared<FNodePropertyManager>();
    PositionCalculator = MakeShared<FNodePositionCalculator>();
    ConnectionManager = MakeShared<FConnectionManager>();
}

void FBlueprintAssembler::ShutdownComponents()
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Shutting down assembler components"));

    NodeFactory.Reset();
    NodeTypeRegistry.Reset();
    PropertyManager.Reset();
    PositionCalculator.Reset();
    ConnectionManager.Reset();
}

// Delegate broadcast methods
void FBlueprintAssembler::BroadcastExecutionStarted(const FString& PlanId)
{
    OnAssemblyExecutionStarted.Broadcast(PlanId);
}

void FBlueprintAssembler::BroadcastInstructionExecuted(const FString& InstructionId, bool bSuccess)
{
    OnAssemblyInstructionExecuted.Broadcast(InstructionId, bSuccess);
}

void FBlueprintAssembler::BroadcastExecutionCompleted(const FAssemblyExecutionResult& Result)
{
    OnAssemblyExecutionCompleted.Broadcast(Result);
}

void FBlueprintAssembler::BroadcastExecutionError(const FString& ErrorMessage, const FString& InstructionId)
{
    OnAssemblyExecutionError.Broadcast(ErrorMessage, InstructionId);
}

// Public interface methods
void FBlueprintAssembler::SetExecutionMode(EAssemblyExecutionMode Mode)
{
    ExecutionMode = Mode;
}

EAssemblyExecutionMode FBlueprintAssembler::GetExecutionMode() const
{
    return ExecutionMode;
}

void FBlueprintAssembler::SetNodeCreationStrategy(ENodeCreationStrategy Strategy)
{
    NodeCreationStrategy = Strategy;
}

ENodeCreationStrategy FBlueprintAssembler::GetNodeCreationStrategy() const
{
    return NodeCreationStrategy;
}

bool FBlueprintAssembler::IsAssemblyInProgress() const
{
    return bIsAssemblyInProgress;
}

float FBlueprintAssembler::GetAssemblyProgress() const
{
    if (!bIsAssemblyInProgress || CurrentPlan.Instructions.Num() == 0)
    {
        return 0.0f;
    }

    int32 CompletedInstructions = 0;
    for (const FNodeAssemblyInstruction& Instruction : CurrentPlan.Instructions)
    {
        if (Instruction.bIsExecuted)
        {
            CompletedInstructions++;
        }
    }

    return static_cast<float>(CompletedInstructions) / CurrentPlan.Instructions.Num();
}

FAssemblyExecutionContext FBlueprintAssembler::GetCurrentContext() const
{
    return CurrentContext;
}

TArray<FNodeAssemblyInstruction> FBlueprintAssembler::GetCurrentInstructions() const
{
    return CurrentPlan.Instructions;
}

bool FBlueprintAssembler::StartAsyncAssembly(const FAssemblyRequest& Request)
{
    if (bIsAssemblyInProgress)
    {
        return false;
    }

    PendingAsyncRequest = Request;
    bAsyncExecutionRequested = true;
    
    // In a full implementation, this would start async execution
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Async assembly requested"));
    
    return true;
}

bool FBlueprintAssembler::PauseAssembly()
{
    if (bIsAssemblyInProgress)
    {
        bIsAssemblyPaused = true;
        UE_LOG(LogBlueprintAssembler, Log, TEXT("Assembly paused"));
        return true;
    }
    return false;
}

bool FBlueprintAssembler::ResumeAssembly()
{
    if (bIsAssemblyInProgress && bIsAssemblyPaused)
    {
        bIsAssemblyPaused = false;
        UE_LOG(LogBlueprintAssembler, Log, TEXT("Assembly resumed"));
        return true;
    }
    return false;
}

bool FBlueprintAssembler::CancelAssembly()
{
    if (bIsAssemblyInProgress)
    {
        bIsAssemblyInProgress = false;
        bIsAssemblyPaused = false;
        UE_LOG(LogBlueprintAssembler, Log, TEXT("Assembly cancelled"));
        return true;
    }
    return false;
}

bool FBlueprintAssembler::ExecuteBatchAssembly(const TArray<FAssemblyRequest>& Requests, const FAssemblyBatchConfig& BatchConfig, TArray<FAssemblyExecutionResult>& OutResults)
{
    UE_LOG(LogBlueprintAssembler, Log, TEXT("Starting batch assembly of %d requests"), Requests.Num());

    OutResults.Empty();
    OutResults.SetNum(Requests.Num());

    bool bOverallSuccess = true;

    for (int32 i = 0; i < Requests.Num(); ++i)
    {
        FAssemblyExecutionResult Result;
        bool bSuccess = ExecuteAssembly(Requests[i], Result);
        
        OutResults[i] = Result;
        
        if (!bSuccess)
        {
            bOverallSuccess = false;
            if (BatchConfig.bStopOnFirstError)
            {
                UE_LOG(LogBlueprintAssembler, Error, TEXT("Batch assembly stopped on first error at request %d"), i);
                break;
            }
        }
    }

    UE_LOG(LogBlueprintAssembler, Log, TEXT("Batch assembly completed. Overall success: %s"), 
           bOverallSuccess ? TEXT("true") : TEXT("false"));

    return bOverallSuccess;
} 