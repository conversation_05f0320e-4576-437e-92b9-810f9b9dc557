#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "AIModelConnector.h"

/**
 * Settings for the Blueprint Generator plugin
 * Handles configuration persistence and default values
 */
UCLASS(config = EditorPerProjectUserSettings, defaultconfig, meta = (DisplayName = "Blueprint Generator"))
class UE5BLUEPRINTGENERATOR_API UBlueprintGeneratorSettings : public UDeveloperSettings
{
	GENERATED_BODY()

public:
	UBlueprintGeneratorSettings();

	// UDeveloperSettings interface
	virtual FName GetCategoryName() const override;
	virtual FText GetSectionText() const override;
	virtual FText GetSectionDescription() const override;

	// Static access
	static UBlueprintGeneratorSettings* Get();

	// Settings properties
	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "AI Provider"))
	EAIModelProvider AIProvider = EAIModelProvider::Ollama;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Model Name"))
	FString ModelName = TEXT("llama3");

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "API Endpoint"))
	FString APIEndpoint = TEXT("http://localhost:11434/api/generate");

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "API Key", PasswordField = true))
	FString APIKey = TEXT("");

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Temperature", ClampMin = "0.0", ClampMax = "2.0"))
	float Temperature = 0.7f;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Max Tokens", ClampMin = "1", ClampMax = "8192"))
	int32 MaxTokens = 2048;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Top P", ClampMin = "0.0", ClampMax = "1.0"))
	float TopP = 0.9f;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Context Window"))
	int32 ContextWindow = 4096;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Use Streaming"))
	bool bUseStreaming = false;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Enable Caching"))
	bool bEnableCaching = true;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "Request Timeout (seconds)", ClampMin = "5", ClampMax = "300"))
	int32 TimeoutSeconds = 30;

	UPROPERTY(config, EditAnywhere, Category = "AI Model", meta = (DisplayName = "System Prompt", MultiLine = true))
	FString SystemPrompt = TEXT("You are an expert in Unreal Engine 5 Blueprint creation. Generate detailed, efficient blueprint structures following UE5 best practices.");

	// Generation options
	UPROPERTY(config, EditAnywhere, Category = "Generation", meta = (DisplayName = "Generate Comments by Default"))
	bool bGenerateCommentsByDefault = true;

	UPROPERTY(config, EditAnywhere, Category = "Generation", meta = (DisplayName = "Include Error Handling by Default"))
	bool bIncludeErrorHandlingByDefault = true;

	UPROPERTY(config, EditAnywhere, Category = "Generation", meta = (DisplayName = "Use Advanced Logic by Default"))
	bool bUseAdvancedLogicByDefault = false;

	UPROPERTY(config, EditAnywhere, Category = "Generation", meta = (DisplayName = "Optimize Performance by Default"))
	bool bOptimizePerformanceByDefault = true;

	// UI preferences
	UPROPERTY(config, EditAnywhere, Category = "UI", meta = (DisplayName = "Remember Window Size"))
	bool bRememberWindowSize = true;

	UPROPERTY(config, EditAnywhere, Category = "UI", meta = (DisplayName = "Auto-save Input"))
	bool bAutoSaveInput = true;

	UPROPERTY(config, EditAnywhere, Category = "UI", meta = (DisplayName = "Show Complexity Indicator"))
	bool bShowComplexityIndicator = true;

	UPROPERTY(config, EditAnywhere, Category = "UI", meta = (DisplayName = "Show Real-time Suggestions"))
	bool bShowRealtimeSuggestions = true;

	// Advanced settings
	UPROPERTY(config, EditAnywhere, Category = "Advanced", meta = (DisplayName = "Enable Debug Logging"))
	bool bEnableDebugLogging = false;

	UPROPERTY(config, EditAnywhere, Category = "Advanced", meta = (DisplayName = "Max History Entries", ClampMin = "10", ClampMax = "1000"))
	int32 MaxHistoryEntries = 100;

	UPROPERTY(config, EditAnywhere, Category = "Advanced", meta = (DisplayName = "Auto-refresh Models on Startup"))
	bool bAutoRefreshModelsOnStartup = true;

	// Conversion methods
	FAIModelSettings ToAIModelSettings() const;
	void FromAIModelSettings(const FAIModelSettings& Settings);

	// Validation
	bool ValidateSettings(FString& OutErrorMessage) const;

	// Reset to defaults
	UFUNCTION(CallInEditor, Category = "Settings")
	void ResetToDefaults();

	// Test connection
	UFUNCTION(CallInEditor, Category = "AI Model")
	void TestConnection();

	// Refresh available models
	UFUNCTION(CallInEditor, Category = "AI Model")
	void RefreshAvailableModels();

protected:
	// UObject interface
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;

private:
	// Internal validation helpers
	bool IsValidEndpoint(const FString& Endpoint) const;
	bool IsValidModelName(const FString& ModelName) const;
};

#include "BlueprintGeneratorSettings.generated.h"