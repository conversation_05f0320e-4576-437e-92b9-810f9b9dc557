#include "CodeGeneration/ExportManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "Misc/Paths.h"
#include "Async/Async.h"
#include "HAL/PlatformFile.h"
#include "GenericPlatform/GenericPlatformFile.h"

DEFINE_LOG_CATEGORY(LogExportManager);

// Singleton instance
UExportManager* UExportManager::Instance = nullptr;

UExportManager::UExportManager()
{
    UE_LOG(LogExportManager, Log, TEXT("Export Manager initialized"));
}

UExportManager* UExportManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UExportManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

FExportResult UExportManager::ExportCode(const FExportRequest& Request)
{
    FScopeLock Lock(&CriticalSection);
    
    FExportResult Result;
    Result.RequestId = Request.RequestId;
    
    double StartTime = FPlatformTime::Seconds();
    
    // Validate request
    FString ErrorMessage;
    if (!ValidateRequest(Request, ErrorMessage))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = ErrorMessage;
        UE_LOG(LogExportManager, Error, TEXT("Export request validation failed: %s"), *ErrorMessage);
        OnExportError.Broadcast(Request.RequestId, ErrorMessage);
        return Result;
    }
    
    // Broadcast export started
    OnExportStarted.Broadcast(Request.RequestId);
    
    try
    {
        // Progress: 10%
        OnExportProgress.Broadcast(Request.RequestId, 0.1f);
        
        // Find best export handler
        TSharedPtr<IExportHandler> Handler = FindBestHandler(Request);
        if (Handler.IsValid())
        {
            // Use registered handler
            Result = Handler->ExportFiles(Request);
        }
        else
        {
            // Use built-in export logic
            
            // Prepare export directory
            if (!PrepareExportDirectory(Request.Config.OutputDirectory))
            {
                Result.bSuccess = false;
                Result.ErrorMessage = FString::Printf(TEXT("Failed to prepare export directory: %s"), *Request.Config.OutputDirectory);
                OnExportError.Broadcast(Request.RequestId, Result.ErrorMessage);
                return Result;
            }
            
            // Progress: 20%
            OnExportProgress.Broadcast(Request.RequestId, 0.2f);
            
            // Create export package
            FExportPackage Package = CreatePackage(Request);
            
            // Progress: 60%
            OnExportProgress.Broadcast(Request.RequestId, 0.6f);
            
            // Apply compression if requested
            if (Request.Config.Compression != EExportCompression::None)
            {
                if (!CompressPackage(Package, Request.Config.Compression))
                {
                    UE_LOG(LogExportManager, Warning, TEXT("Failed to compress package, continuing without compression"));
                }
            }
            
            // Progress: 80%
            OnExportProgress.Broadcast(Request.RequestId, 0.8f);
            
            // Generate manifest if requested
            if (Request.Config.bGenerateManifest)
            {
                FString ManifestContent = GenerateManifest(Package);
                FString ManifestPath = FPaths::Combine(Request.Config.OutputDirectory, TEXT("manifest.json"));
                
                if (FFileHelper::SaveStringToFile(ManifestContent, *ManifestPath))
                {
                    Result.ManifestPath = ManifestPath;
                    OnExportFileGenerated.Broadcast(Request.RequestId, ManifestPath);
                }
            }
            
            // Save package files
            TArray<FString> ExportedFiles;
            for (const FExportFile& File : Package.Files)
            {
                FString FullPath = FPaths::Combine(Request.Config.OutputDirectory, File.FilePath);
                
                // Ensure directory exists
                FString Directory = FPaths::GetPath(FullPath);
                if (!FPaths::DirectoryExists(Directory))
                {
                    IFileManager::Get().MakeDirectory(*Directory, true);
                }
                
                if (FFileHelper::SaveStringToFile(File.Content, *FullPath))
                {
                    ExportedFiles.Add(FullPath);
                    OnExportFileGenerated.Broadcast(Request.RequestId, FullPath);
                }
                else
                {
                    UE_LOG(LogExportManager, Warning, TEXT("Failed to save file: %s"), *FullPath);
                }
            }
            
            // Set result data
            Result.bSuccess = true;
            Result.ExportedPackage = Package;
            Result.ExportedFiles = ExportedFiles;
            Result.FileCount = ExportedFiles.Num();
            Result.TotalSize = CalculatePackageSize(Package);
            Result.CompressionRatio = Package.Compression != EExportCompression::None ? 0.7f : 1.0f; // Estimated
        }
        
        // Calculate metrics
        Result.ExportTime = FPlatformTime::Seconds() - StartTime;
        
        // Progress: 100%
        OnExportProgress.Broadcast(Request.RequestId, 1.0f);
        
        // Update statistics
        UpdateStatistics(Result);
        
        // Broadcast completion
        OnExportCompleted.Broadcast(Request.RequestId, Result);
        
        UE_LOG(LogExportManager, Log, TEXT("Export completed successfully. Time: %.3fs, Files: %d, Size: %lld bytes"), 
            Result.ExportTime, Result.FileCount, Result.TotalSize);
    }
    catch (const std::exception& e)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Exception during export: %s"), UTF8_TO_TCHAR(e.what()));
        Result.ExportTime = FPlatformTime::Seconds() - StartTime;
        
        UpdateStatistics(Result);
        OnExportError.Broadcast(Request.RequestId, Result.ErrorMessage);
        
        UE_LOG(LogExportManager, Error, TEXT("Exception during export: %s"), *Result.ErrorMessage);
    }
    
    return Result;
}

FString UExportManager::ExportCodeAsync(const FExportRequest& Request)
{
    FString RequestId = Request.RequestId.IsEmpty() ? GenerateRequestId() : Request.RequestId;
    
    // Store request
    {
        FScopeLock Lock(&CriticalSection);
        FExportRequest StoredRequest = Request;
        StoredRequest.RequestId = RequestId;
        StoredRequest.Timestamp = FPlatformTime::Seconds();
        ActiveRequests.Add(RequestId, StoredRequest);
    }
    
    // Execute asynchronously
    Async(EAsyncExecution::ThreadPool, [this, RequestId]()
    {
        FExportRequest StoredRequest;
        {
            FScopeLock Lock(&CriticalSection);
            if (ActiveRequests.Contains(RequestId))
            {
                StoredRequest = ActiveRequests[RequestId];
            }
            else
            {
                return; // Request was cancelled
            }
        }
        
        FExportResult Result = ExportCode(StoredRequest);
        
        // Remove from active requests
        {
            FScopeLock Lock(&CriticalSection);
            ActiveRequests.Remove(RequestId);
        }
    });
    
    return RequestId;
}

bool UExportManager::RegisterHandler(TSharedPtr<IExportHandler> Handler)
{
    if (!Handler.IsValid())
    {
        UE_LOG(LogExportManager, Warning, TEXT("Attempted to register null export handler"));
        return false;
    }
    
    FScopeLock Lock(&CriticalSection);
    
    // Check if handler with same name already exists
    for (const auto& ExistingHandler : RegisteredHandlers)
    {
        if (ExistingHandler.IsValid() && ExistingHandler->GetHandlerName() == Handler->GetHandlerName())
        {
            UE_LOG(LogExportManager, Warning, TEXT("Export handler with name '%s' already registered"), 
                *Handler->GetHandlerName());
            return false;
        }
    }
    
    RegisteredHandlers.Add(Handler);
    UE_LOG(LogExportManager, Log, TEXT("Registered export handler: %s"), *Handler->GetHandlerName());
    return true;
}

bool UExportManager::UnregisterHandler(const FString& HandlerName)
{
    FScopeLock Lock(&CriticalSection);
    
    for (int32 i = RegisteredHandlers.Num() - 1; i >= 0; --i)
    {
        if (RegisteredHandlers[i].IsValid() && RegisteredHandlers[i]->GetHandlerName() == HandlerName)
        {
            RegisteredHandlers.RemoveAt(i);
            UE_LOG(LogExportManager, Log, TEXT("Unregistered export handler: %s"), *HandlerName);
            return true;
        }
    }
    
    UE_LOG(LogExportManager, Warning, TEXT("Export handler '%s' not found for unregistration"), *HandlerName);
    return false;
}

TArray<FString> UExportManager::GetAvailableHandlers() const
{
    FScopeLock Lock(&CriticalSection);
    
    TArray<FString> HandlerNames;
    for (const auto& Handler : RegisteredHandlers)
    {
        if (Handler.IsValid())
        {
            HandlerNames.Add(Handler->GetHandlerName());
        }
    }
    
    return HandlerNames;
}

TArray<FString> UExportManager::GetSupportedFormats() const
{
    FScopeLock Lock(&CriticalSection);
    
    TSet<FString> UniqueFormats;
    
    // Add built-in formats
    UniqueFormats.Add(TEXT("CppProject"));
    UniqueFormats.Add(TEXT("CppFiles"));
    UniqueFormats.Add(TEXT("VisualStudio"));
    UniqueFormats.Add(TEXT("CMake"));
    UniqueFormats.Add(TEXT("UnrealProject"));
    UniqueFormats.Add(TEXT("Archive"));
    UniqueFormats.Add(TEXT("Documentation"));
    
    // Add formats from registered handlers
    for (const auto& Handler : RegisteredHandlers)
    {
        if (Handler.IsValid())
        {
            TArray<EExportFormat> SupportedFormats = Handler->GetSupportedFormats();
            for (EExportFormat Format : SupportedFormats)
            {
                switch (Format)
                {
                case EExportFormat::CppProject:
                    UniqueFormats.Add(TEXT("CppProject"));
                    break;
                case EExportFormat::CppFiles:
                    UniqueFormats.Add(TEXT("CppFiles"));
                    break;
                case EExportFormat::VisualStudio:
                    UniqueFormats.Add(TEXT("VisualStudio"));
                    break;
                case EExportFormat::CMake:
                    UniqueFormats.Add(TEXT("CMake"));
                    break;
                case EExportFormat::UnrealProject:
                    UniqueFormats.Add(TEXT("UnrealProject"));
                    break;
                case EExportFormat::Archive:
                    UniqueFormats.Add(TEXT("Archive"));
                    break;
                case EExportFormat::Documentation:
                    UniqueFormats.Add(TEXT("Documentation"));
                    break;
                case EExportFormat::Custom:
                    UniqueFormats.Add(FString::Printf(TEXT("Custom_%s"), *Handler->GetHandlerName()));
                    break;
                }
            }
        }
    }
    
    return UniqueFormats.Array();
}

TArray<FString> UExportManager::GetSupportedTargets() const
{
    FScopeLock Lock(&CriticalSection);
    
    TSet<FString> UniqueTargets;
    
    // Add built-in targets
    UniqueTargets.Add(TEXT("LocalDirectory"));
    UniqueTargets.Add(TEXT("NetworkShare"));
    UniqueTargets.Add(TEXT("CloudStorage"));
    UniqueTargets.Add(TEXT("VersionControl"));
    UniqueTargets.Add(TEXT("Email"));
    UniqueTargets.Add(TEXT("FTP"));
    
    // Add targets from registered handlers
    for (const auto& Handler : RegisteredHandlers)
    {
        if (Handler.IsValid())
        {
            TArray<EExportTarget> SupportedTargets = Handler->GetSupportedTargets();
            for (EExportTarget Target : SupportedTargets)
            {
                switch (Target)
                {
                case EExportTarget::LocalDirectory:
                    UniqueTargets.Add(TEXT("LocalDirectory"));
                    break;
                case EExportTarget::NetworkShare:
                    UniqueTargets.Add(TEXT("NetworkShare"));
                    break;
                case EExportTarget::CloudStorage:
                    UniqueTargets.Add(TEXT("CloudStorage"));
                    break;
                case EExportTarget::VersionControl:
                    UniqueTargets.Add(TEXT("VersionControl"));
                    break;
                case EExportTarget::Email:
                    UniqueTargets.Add(TEXT("Email"));
                    break;
                case EExportTarget::FTP:
                    UniqueTargets.Add(TEXT("FTP"));
                    break;
                case EExportTarget::Custom:
                    UniqueTargets.Add(FString::Printf(TEXT("Custom_%s"), *Handler->GetHandlerName()));
                    break;
                }
            }
        }
    }
    
    return UniqueTargets.Array();
}

bool UExportManager::ValidateRequest(const FExportRequest& Request, FString& ErrorMessage) const
{
    // Validate request ID
    if (Request.RequestId.IsEmpty())
    {
        ErrorMessage = TEXT("Request ID cannot be empty");
        return false;
    }
    
    // Validate output directory
    if (Request.Config.OutputDirectory.IsEmpty())
    {
        ErrorMessage = TEXT("Output directory cannot be empty");
        return false;
    }
    
    // Validate package name
    if (Request.Config.PackageName.IsEmpty())
    {
        ErrorMessage = TEXT("Package name cannot be empty");
        return false;
    }
    
    // Validate that we have something to export
    if (Request.Blueprints.Num() == 0 && Request.GeneratedFiles.Num() == 0)
    {
        ErrorMessage = TEXT("No blueprints or generated files to export");
        return false;
    }
    
    // Validate blueprints
    for (const TWeakObjectPtr<UBlueprint>& BlueprintPtr : Request.Blueprints)
    {
        if (!BlueprintPtr.IsValid())
        {
            ErrorMessage = TEXT("One or more blueprints are invalid or have been garbage collected");
            return false;
        }
    }
    
    return true;
}

FExportPackage UExportManager::CreatePackage(const FExportRequest& Request) const
{
    FExportPackage Package;
    Package.PackageName = Request.Config.PackageName;
    Package.PackagePath = Request.Config.OutputDirectory;
    Package.Format = Request.Config.Format;
    Package.Compression = Request.Config.Compression;
    Package.CreationTime = FDateTime::Now();
    Package.Version = TEXT("1.0.0");
    Package.Description = FString::Printf(TEXT("Exported package containing %d blueprints and %d generated files"), 
        Request.Blueprints.Num(), Request.GeneratedFiles.Num());
    
    TArray<FExportFile> AllFiles;
    
    // Add generated files
    for (const FGeneratedCodeFile& GeneratedFile : Request.GeneratedFiles)
    {
        FExportFile ExportFile;
        ExportFile.FileName = FPaths::GetCleanFilename(GeneratedFile.FilePath);
        ExportFile.FilePath = GeneratedFile.FilePath;
        ExportFile.Content = GeneratedFile.Content;
        ExportFile.FileType = FPaths::GetExtension(GeneratedFile.FilePath);
        ExportFile.FileSize = GeneratedFile.Content.Len();
        ExportFile.CreationTime = FDateTime::Now();
        ExportFile.ModificationTime = FDateTime::Now();
        ExportFile.bIsGenerated = true;
        
        // Add metadata
        ExportFile.Metadata.Add(TEXT("GenerationTarget"), GeneratedFile.Target.ToString());
        ExportFile.Metadata.Add(TEXT("GenerationMode"), GeneratedFile.Mode.ToString());
        ExportFile.Metadata.Add(TEXT("QualityLevel"), GeneratedFile.QualityLevel.ToString());
        
        AllFiles.Add(ExportFile);
    }
    
    // Generate project files if requested
    if (Request.Config.bIncludeProjectFiles)
    {
        TArray<FExportFile> ProjectFiles = GenerateProjectFiles(Request);
        AllFiles.Append(ProjectFiles);
    }
    
    // Generate documentation files if requested
    if (Request.Config.bIncludeDocumentation)
    {
        TArray<FExportFile> DocumentationFiles = GenerateDocumentationFiles(Request);
        AllFiles.Append(DocumentationFiles);
    }
    
    // Apply file filters
    Package.Files = ApplyFileFilters(AllFiles, Request.Config);
    
    // Calculate total size
    Package.TotalSize = CalculatePackageSize(Package);
    
    // Set package properties
    Package.Properties.Add(TEXT("BlueprintCount"), FString::FromInt(Request.Blueprints.Num()));
    Package.Properties.Add(TEXT("GeneratedFileCount"), FString::FromInt(Request.GeneratedFiles.Num()));
    Package.Properties.Add(TEXT("TotalFileCount"), FString::FromInt(Package.Files.Num()));
    Package.Properties.Add(TEXT("ExportFormat"), UEnum::GetValueAsString(Request.Config.Format));
    Package.Properties.Add(TEXT("CompressionType"), UEnum::GetValueAsString(Request.Config.Compression));
    
    return Package;
}

bool UExportManager::CompressPackage(FExportPackage& Package, EExportCompression CompressionType) const
{
    // This is a simplified implementation
    // In a real implementation, you would use actual compression libraries
    
    switch (CompressionType)
    {
    case EExportCompression::ZIP:
        UE_LOG(LogExportManager, Log, TEXT("Applying ZIP compression to package"));
        break;
    case EExportCompression::RAR:
        UE_LOG(LogExportManager, Log, TEXT("Applying RAR compression to package"));
        break;
    case EExportCompression::SevenZip:
        UE_LOG(LogExportManager, Log, TEXT("Applying 7-Zip compression to package"));
        break;
    case EExportCompression::TAR:
        UE_LOG(LogExportManager, Log, TEXT("Applying TAR compression to package"));
        break;
    case EExportCompression::GZIP:
        UE_LOG(LogExportManager, Log, TEXT("Applying GZIP compression to package"));
        break;
    default:
        return false;
    }
    
    // Mark files as compressed
    for (FExportFile& File : Package.Files)
    {
        File.bIsCompressed = true;
        // Simulate compression by reducing file size
        File.FileSize = static_cast<int64>(File.FileSize * 0.7f);
    }
    
    Package.Compression = CompressionType;
    Package.TotalSize = CalculatePackageSize(Package);
    
    return true;
}

FString UExportManager::GenerateManifest(const FExportPackage& Package) const
{
    FString Manifest;
    
    // JSON-like manifest format
    Manifest += TEXT("{\n");
    Manifest += FString::Printf(TEXT("  \"packageName\": \"%s\",\n"), *Package.PackageName);
    Manifest += FString::Printf(TEXT("  \"version\": \"%s\",\n"), *Package.Version);
    Manifest += FString::Printf(TEXT("  \"description\": \"%s\",\n"), *Package.Description);
    Manifest += FString::Printf(TEXT("  \"creationTime\": \"%s\",\n"), *Package.CreationTime.ToString());
    Manifest += FString::Printf(TEXT("  \"format\": \"%s\",\n"), *UEnum::GetValueAsString(Package.Format));
    Manifest += FString::Printf(TEXT("  \"compression\": \"%s\",\n"), *UEnum::GetValueAsString(Package.Compression));
    Manifest += FString::Printf(TEXT("  \"totalSize\": %lld,\n"), Package.TotalSize);
    Manifest += FString::Printf(TEXT("  \"fileCount\": %d,\n"), Package.Files.Num());
    
    // Properties
    Manifest += TEXT("  \"properties\": {\n");
    int32 PropIndex = 0;
    for (const auto& Property : Package.Properties)
    {
        Manifest += FString::Printf(TEXT("    \"%s\": \"%s\""), *Property.Key, *Property.Value);
        if (PropIndex < Package.Properties.Num() - 1)
        {
            Manifest += TEXT(",");
        }
        Manifest += TEXT("\n");
        PropIndex++;
    }
    Manifest += TEXT("  },\n");
    
    // Files
    Manifest += TEXT("  \"files\": [\n");
    for (int32 i = 0; i < Package.Files.Num(); ++i)
    {
        const FExportFile& File = Package.Files[i];
        Manifest += TEXT("    {\n");
        Manifest += FString::Printf(TEXT("      \"fileName\": \"%s\",\n"), *File.FileName);
        Manifest += FString::Printf(TEXT("      \"filePath\": \"%s\",\n"), *File.FilePath);
        Manifest += FString::Printf(TEXT("      \"fileType\": \"%s\",\n"), *File.FileType);
        Manifest += FString::Printf(TEXT("      \"fileSize\": %lld,\n"), File.FileSize);
        Manifest += FString::Printf(TEXT("      \"isGenerated\": %s,\n"), File.bIsGenerated ? TEXT("true") : TEXT("false"));
        Manifest += FString::Printf(TEXT("      \"isCompressed\": %s\n"), File.bIsCompressed ? TEXT("true") : TEXT("false"));
        Manifest += TEXT("    }");
        if (i < Package.Files.Num() - 1)
        {
            Manifest += TEXT(",");
        }
        Manifest += TEXT("\n");
    }
    Manifest += TEXT("  ]\n");
    Manifest += TEXT("}\n");
    
    return Manifest;
}

FExportStatistics UExportManager::GetStatistics() const
{
    FScopeLock Lock(&CriticalSection);
    return Statistics;
}

void UExportManager::ResetStatistics()
{
    FScopeLock Lock(&CriticalSection);
    Statistics = FExportStatistics();
    UE_LOG(LogExportManager, Log, TEXT("Export statistics reset"));
}

bool UExportManager::CancelExport(const FString& RequestId)
{
    FScopeLock Lock(&CriticalSection);
    
    if (ActiveRequests.Contains(RequestId))
    {
        ActiveRequests.Remove(RequestId);
        UE_LOG(LogExportManager, Log, TEXT("Cancelled export request: %s"), *RequestId);
        return true;
    }
    
    return false;
}

TArray<FString> UExportManager::GetActiveRequests() const
{
    FScopeLock Lock(&CriticalSection);
    
    TArray<FString> RequestIds;
    ActiveRequests.GetKeys(RequestIds);
    return RequestIds;
}

void UExportManager::CleanupTemporaryFiles()
{
    FScopeLock Lock(&CriticalSection);
    
    int32 CleanedCount = 0;
    for (const FString& TempFile : TemporaryFiles)
    {
        if (FPaths::FileExists(TempFile))
        {
            if (IFileManager::Get().Delete(*TempFile))
            {
                CleanedCount++;
            }
        }
    }
    
    TemporaryFiles.Empty();
    UE_LOG(LogExportManager, Log, TEXT("Cleaned up %d temporary files"), CleanedCount);
}

TSharedPtr<IExportHandler> UExportManager::FindBestHandler(const FExportRequest& Request) const
{
    TSharedPtr<IExportHandler> BestHandler;
    int32 BestPriority = -1;
    
    for (const auto& Handler : RegisteredHandlers)
    {
        if (Handler.IsValid() && Handler->CanHandleRequest(Request))
        {
            int32 Priority = Handler->GetPriority();
            if (Priority > BestPriority)
            {
                BestPriority = Priority;
                BestHandler = Handler;
            }
        }
    }
    
    return BestHandler;
}

bool UExportManager::PrepareExportDirectory(const FString& Directory) const
{
    if (Directory.IsEmpty())
    {
        return false;
    }
    
    // Create directory if it doesn't exist
    if (!FPaths::DirectoryExists(Directory))
    {
        if (!IFileManager::Get().MakeDirectory(*Directory, true))
        {
            UE_LOG(LogExportManager, Error, TEXT("Failed to create export directory: %s"), *Directory);
            return false;
        }
    }
    
    // Check if directory is writable
    FString TestFile = FPaths::Combine(Directory, TEXT("test_write.tmp"));
    if (!FFileHelper::SaveStringToFile(TEXT("test"), *TestFile))
    {
        UE_LOG(LogExportManager, Error, TEXT("Export directory is not writable: %s"), *Directory);
        return false;
    }
    
    // Clean up test file
    IFileManager::Get().Delete(*TestFile);
    
    return true;
}

TArray<FExportFile> UExportManager::GenerateProjectFiles(const FExportRequest& Request) const
{
    TArray<FExportFile> ProjectFiles;
    
    // Generate CMakeLists.txt
    if (Request.Config.Format == EExportFormat::CMake || Request.Config.Format == EExportFormat::CppProject)
    {
        FExportFile CMakeFile;
        CMakeFile.FileName = TEXT("CMakeLists.txt");
        CMakeFile.FilePath = TEXT("CMakeLists.txt");
        CMakeFile.FileType = TEXT("txt");
        CMakeFile.bIsGenerated = true;
        CMakeFile.CreationTime = FDateTime::Now();
        CMakeFile.ModificationTime = FDateTime::Now();
        
        // Generate basic CMakeLists.txt content
        CMakeFile.Content = FString::Printf(TEXT(
            "cmake_minimum_required(VERSION 3.16)\n"
            "project(%s)\n\n"
            "set(CMAKE_CXX_STANDARD 17)\n"
            "set(CMAKE_CXX_STANDARD_REQUIRED ON)\n\n"
            "# Add source files\n"
            "file(GLOB_RECURSE SOURCES \"*.cpp\" \"*.h\")\n\n"
            "# Create executable\n"
            "add_executable(${PROJECT_NAME} ${SOURCES})\n\n"
            "# Include directories\n"
            "target_include_directories(${PROJECT_NAME} PRIVATE .)\n"
        ), *Request.Config.PackageName);
        
        CMakeFile.FileSize = CMakeFile.Content.Len();
        ProjectFiles.Add(CMakeFile);
    }
    
    // Generate Visual Studio project file
    if (Request.Config.Format == EExportFormat::VisualStudio)
    {
        FExportFile VSProjFile;
        VSProjFile.FileName = Request.Config.PackageName + TEXT(".vcxproj");
        VSProjFile.FilePath = VSProjFile.FileName;
        VSProjFile.FileType = TEXT("vcxproj");
        VSProjFile.bIsGenerated = true;
        VSProjFile.CreationTime = FDateTime::Now();
        VSProjFile.ModificationTime = FDateTime::Now();
        
        // Generate basic Visual Studio project content
        VSProjFile.Content = FString::Printf(TEXT(
            "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"
            "<Project DefaultTargets=\"Build\" xmlns=\"http://schemas.microsoft.com/developer/msbuild/2003\">\n"
            "  <PropertyGroup>\n"
            "    <ProjectName>%s</ProjectName>\n"
            "    <ConfigurationType>Application</ConfigurationType>\n"
            "    <PlatformToolset>v143</PlatformToolset>\n"
            "    <CharacterSet>Unicode</CharacterSet>\n"
            "  </PropertyGroup>\n"
            "</Project>\n"
        ), *Request.Config.PackageName);
        
        VSProjFile.FileSize = VSProjFile.Content.Len();
        ProjectFiles.Add(VSProjFile);
    }
    
    // Generate README.md
    FExportFile ReadmeFile;
    ReadmeFile.FileName = TEXT("README.md");
    ReadmeFile.FilePath = TEXT("README.md");
    ReadmeFile.FileType = TEXT("md");
    ReadmeFile.bIsGenerated = true;
    ReadmeFile.CreationTime = FDateTime::Now();
    ReadmeFile.ModificationTime = FDateTime::Now();
    
    ReadmeFile.Content = FString::Printf(TEXT(
        "# %s\n\n"
        "This project was generated from UE5 Blueprints using the Blueprint Generator Plugin.\n\n"
        "## Generated Files\n\n"
        "- **Blueprints Converted**: %d\n"
        "- **Generated Files**: %d\n"
        "- **Export Format**: %s\n"
        "- **Generation Time**: %s\n\n"
        "## Build Instructions\n\n"
        "Please refer to the project files for build instructions specific to your platform.\n"
    ), 
    *Request.Config.PackageName,
    Request.Blueprints.Num(),
    Request.GeneratedFiles.Num(),
    *UEnum::GetValueAsString(Request.Config.Format),
    *FDateTime::Now().ToString());
    
    ReadmeFile.FileSize = ReadmeFile.Content.Len();
    ProjectFiles.Add(ReadmeFile);
    
    return ProjectFiles;
}

TArray<FExportFile> UExportManager::GenerateDocumentationFiles(const FExportRequest& Request) const
{
    TArray<FExportFile> DocumentationFiles;
    
    // Generate API documentation
    FExportFile ApiDocFile;
    ApiDocFile.FileName = TEXT("API_Documentation.md");
    ApiDocFile.FilePath = TEXT("docs/API_Documentation.md");
    ApiDocFile.FileType = TEXT("md");
    ApiDocFile.bIsGenerated = true;
    ApiDocFile.CreationTime = FDateTime::Now();
    ApiDocFile.ModificationTime = FDateTime::Now();
    
    ApiDocFile.Content = FString::Printf(TEXT(
        "# API Documentation\n\n"
        "## Overview\n\n"
        "This document provides API documentation for the generated C++ classes from UE5 Blueprints.\n\n"
        "## Generated Classes\n\n"
        "The following classes were generated from %d blueprints:\n\n"
    ), Request.Blueprints.Num());
    
    // Add class documentation for each blueprint
    for (const TWeakObjectPtr<UBlueprint>& BlueprintPtr : Request.Blueprints)
    {
        if (BlueprintPtr.IsValid())
        {
            UBlueprint* Blueprint = BlueprintPtr.Get();
            ApiDocFile.Content += FString::Printf(TEXT(
                "### %s\n\n"
                "Generated from Blueprint: `%s`\n\n"
                "**Base Class**: %s\n\n"
            ), 
            *Blueprint->GetName(),
            *Blueprint->GetName(),
            Blueprint->ParentClass ? *Blueprint->ParentClass->GetName() : TEXT("UObject"));
        }
    }
    
    ApiDocFile.FileSize = ApiDocFile.Content.Len();
    DocumentationFiles.Add(ApiDocFile);
    
    return DocumentationFiles;
}

TArray<FExportFile> UExportManager::ApplyFileFilters(const TArray<FExportFile>& Files, const FExportConfig& Config) const
{
    TArray<FExportFile> FilteredFiles;
    
    for (const FExportFile& File : Files)
    {
        bool bIncludeFile = true;
        
        // Apply include patterns
        if (Config.IncludePatterns.Num() > 0)
        {
            bIncludeFile = false;
            for (const FString& Pattern : Config.IncludePatterns)
            {
                if (File.FileName.MatchesWildcard(Pattern) || File.FilePath.MatchesWildcard(Pattern))
                {
                    bIncludeFile = true;
                    break;
                }
            }
        }
        
        // Apply exclude patterns
        if (bIncludeFile && Config.ExcludePatterns.Num() > 0)
        {
            for (const FString& Pattern : Config.ExcludePatterns)
            {
                if (File.FileName.MatchesWildcard(Pattern) || File.FilePath.MatchesWildcard(Pattern))
                {
                    bIncludeFile = false;
                    break;
                }
            }
        }
        
        if (bIncludeFile)
        {
            FilteredFiles.Add(File);
        }
    }
    
    return FilteredFiles;
}

int64 UExportManager::CalculatePackageSize(const FExportPackage& Package) const
{
    int64 TotalSize = 0;
    
    for (const FExportFile& File : Package.Files)
    {
        TotalSize += File.FileSize;
    }
    
    return TotalSize;
}

void UExportManager::UpdateStatistics(const FExportResult& Result)
{
    Statistics.TotalExports++;
    
    if (Result.bSuccess)
    {
        Statistics.SuccessfulExports++;
        Statistics.TotalDataExported += Result.TotalSize;
        
        // Update averages
        float TotalTime = Statistics.AverageExportTime * (Statistics.SuccessfulExports - 1) + Result.ExportTime;
        Statistics.AverageExportTime = TotalTime / Statistics.SuccessfulExports;
        
        float TotalCompression = Statistics.AverageCompressionRatio * (Statistics.SuccessfulExports - 1) + Result.CompressionRatio;
        Statistics.AverageCompressionRatio = TotalCompression / Statistics.SuccessfulExports;
        
        // Update usage statistics
        FString FormatString = UEnum::GetValueAsString(Result.ExportedPackage.Format);
        if (Statistics.FormatUsage.Contains(FormatString))
        {
            Statistics.FormatUsage[FormatString]++;
        }
        else
        {
            Statistics.FormatUsage.Add(FormatString, 1);
        }
        
        FString CompressionString = UEnum::GetValueAsString(Result.ExportedPackage.Compression);
        if (Statistics.CompressionUsage.Contains(CompressionString))
        {
            Statistics.CompressionUsage[CompressionString]++;
        }
        else
        {
            Statistics.CompressionUsage.Add(CompressionString, 1);
        }
    }
    else
    {
        Statistics.FailedExports++;
    }
}

FString UExportManager::GenerateRequestId() const
{
    return FGuid::NewGuid().ToString();
} 