#include "BlueprintGeneration/ConnectionManager.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "EdGraph/EdGraph.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"

DEFINE_LOG_CATEGORY(LogConnectionManager);

FCONNECTIONMANAGER::FCONNECTIONMANAGER()
    : bIsInitialized(false)
    , bVerboseLogging(false)
    , bTrackStatistics(true)
    , TotalConnections(0)
    , TotalDisconnections(0)
    , TotalBatchOperations(0)
    , TotalAutoConnections(0)
    , TotalConversionNodesCreated(0)
    , LastOperationTimeMs(0.0f)
{
    UE_LOG(LogConnectionManager, Log, TEXT("ConnectionManager created"));
}

FCONNECTIONMANAGER::~FCONNECTIONMANAGER()
{
    Shutdown();
    UE_LOG(LogConnectionManager, Log, TEXT("ConnectionManager destroyed"));
}

bool FCONNECTIONMANAGER::Initialize()
{
    if (bIsInitialized)
    {
        UE_LOG(LogConnectionManager, Warning, TEXT("ConnectionManager already initialized"));
        return true;
    }

    UE_LOG(LogConnectionManager, Log, TEXT("Initializing ConnectionManager..."));

    // Clear existing data
    ConnectionStatistics.Empty();

    // Initialize default compatibility checker if none provided
    if (!CompatibilityChecker.IsValid())
    {
        CompatibilityChecker = MakeShared<FPinCompatibilityChecker>();
        if (!CompatibilityChecker->Initialize())
        {
            UE_LOG(LogConnectionManager, Error, TEXT("Failed to initialize default pin compatibility checker"));
            return false;
        }
    }

    bIsInitialized = true;
    
    UE_LOG(LogConnectionManager, Log, TEXT("ConnectionManager initialized successfully"));
    LogConnectionActivity(TEXT("Connection manager initialized"));

    return true;
}

void FCONNECTIONMANAGER::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    UE_LOG(LogConnectionManager, Log, TEXT("Shutting down ConnectionManager..."));

    // Clear all data
    ConnectionStatistics.Empty();
    CompatibilityChecker.Reset();

    bIsInitialized = false;
    
    UE_LOG(LogConnectionManager, Log, TEXT("ConnectionManager shutdown complete"));
}

void FCONNECTIONMANAGER::SetPinCompatibilityChecker(TSharedPtr<FPinCompatibilityChecker> InCompatibilityChecker)
{
    CompatibilityChecker = InCompatibilityChecker;
    LogConnectionActivity(TEXT("Pin compatibility checker updated"));
}

// Single Connection Operations

FCONNECTIONRESULT FCONNECTIONMANAGER::ConnectPins(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    FCONNECTIONREQUEST Request;
    Request.SourcePin = SourcePin;
    Request.TargetPin = TargetPin;
    Request.Strategy = EConnectionStrategy::Direct;
    
    return ConnectPins(Request);
}

FCONNECTIONRESULT FCONNECTIONMANAGER::ConnectPins(const FCONNECTIONREQUEST& Request)
{
    FCONNECTIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized)
    {
        Result.ErrorMessages.Add(TEXT("Connection manager not initialized"));
        Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    if (!Request.SourcePin || !Request.TargetPin)
    {
        Result.ErrorMessages.Add(TEXT("Invalid source or target pin"));
        Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Validate connection request
    TArray<FString> ValidationErrors;
    if (Request.bValidateBeforeConnect && !ValidateConnectionRequest(Request, ValidationErrors))
    {
        if (!Request.bForceConnection)
        {
            Result.ErrorMessages.Append(ValidationErrors);
            Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
            return Result;
        }
        else
        {
            Result.WarningMessages.Append(ValidationErrors);
            Result.WarningMessages.Add(TEXT("Connection forced despite validation failures"));
        }
    }

    // Execute connection based on strategy
    Result = ConnectPinsInternal(Request.SourcePin, Request.TargetPin, Request.Strategy, Request.bAllowConversionNodes);
    
    // Update result metadata
    Result.SourcePin = Request.SourcePin;
    Result.TargetPin = Request.TargetPin;
    Result.UsedStrategy = Request.Strategy;
    Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    LastOperationTimeMs = Result.OperationTimeMs;

    if (Result.bSuccess)
    {
        TotalConnections++;
        LogConnectionActivity(FString::Printf(TEXT("Successfully connected pins '%s' -> '%s'"), 
            *Request.SourcePin->PinName.ToString(), *Request.TargetPin->PinName.ToString()));
    }
    else
    {
        LogConnectionActivity(FString::Printf(TEXT("Failed to connect pins '%s' -> '%s'"), 
            *Request.SourcePin->PinName.ToString(), *Request.TargetPin->PinName.ToString()), true);
    }

    UpdateConnectionStatistics(TEXT("Connect"), Request.SourcePin->PinType.PinCategory.ToString());

    return Result;
}

FCONNECTIONRESULT FCONNECTIONMANAGER::DisconnectPins(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    FCONNECTIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || !SourcePin || !TargetPin)
    {
        Result.ErrorMessages.Add(TEXT("Invalid parameters or manager not initialized"));
        Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    Result = DisconnectPinsInternal(SourcePin, TargetPin);
    Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    LastOperationTimeMs = Result.OperationTimeMs;

    if (Result.bSuccess)
    {
        TotalDisconnections++;
        LogConnectionActivity(FString::Printf(TEXT("Successfully disconnected pins '%s' -> '%s'"), 
            *SourcePin->PinName.ToString(), *TargetPin->PinName.ToString()));
    }

    UpdateConnectionStatistics(TEXT("Disconnect"));

    return Result;
}

FCONNECTIONRESULT FCONNECTIONMANAGER::DisconnectPin(UEdGraphPin* Pin)
{
    FCONNECTIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || !Pin)
    {
        Result.ErrorMessages.Add(TEXT("Invalid pin or manager not initialized"));
        Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    int32 DisconnectedCount = 0;
    TArray<UEdGraphPin*> LinkedPins = Pin->LinkedTo;

    for (UEdGraphPin* LinkedPin : LinkedPins)
    {
        if (LinkedPin)
        {
            FCONNECTIONRESULT DisconnectResult = DisconnectPinsInternal(Pin, LinkedPin);
            if (DisconnectResult.bSuccess)
            {
                DisconnectedCount++;
            }
            else
            {
                Result.ErrorMessages.Append(DisconnectResult.ErrorMessages);
            }
        }
    }

    Result.bSuccess = (DisconnectedCount > 0);
    Result.SourcePin = Pin;
    Result.InfoMessages.Add(FString::Printf(TEXT("Disconnected %d connections"), DisconnectedCount));
    Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    LastOperationTimeMs = Result.OperationTimeMs;

    LogConnectionActivity(FString::Printf(TEXT("Disconnected %d connections from pin '%s'"), 
        DisconnectedCount, *Pin->PinName.ToString()));

    UpdateConnectionStatistics(TEXT("DisconnectAll"));

    return Result;
}

// Batch Connection Operations

FBATCHCONNECTIONRESULT FCONNECTIONMANAGER::ExecuteBatchConnections(const FBATCHCONNECTIONREQUEST& BatchRequest)
{
    FBATCHCONNECTIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized)
    {
        Result.ErrorMessages.Add(TEXT("Connection manager not initialized"));
        Result.TotalOperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Validate all connections first if requested
    if (BatchRequest.bValidateAllFirst)
    {
        for (const FCONNECTIONREQUEST& Request : BatchRequest.ConnectionRequests)
        {
            TArray<FString> ValidationErrors;
            if (!ValidateConnectionRequest(Request, ValidationErrors))
            {
                Result.ErrorMessages.Append(ValidationErrors);
                if (BatchRequest.bStopOnFirstFailure)
                {
                    Result.TotalOperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
                    return Result;
                }
            }
        }
    }

    // Execute connections
    TArray<FCONNECTIONRESULT> CompletedConnections;
    
    for (const FCONNECTIONREQUEST& Request : BatchRequest.ConnectionRequests)
    {
        // Check time limit
        double CurrentTime = FPlatformTime::Seconds();
        if ((CurrentTime - StartTime) * 1000.0 > BatchRequest.MaxOperationTimeMs)
        {
            Result.WarningMessages.Add(TEXT("Batch operation time limit exceeded"));
            break;
        }

        FCONNECTIONRESULT ConnectionResult = ConnectPins(Request);
        Result.Results.Add(ConnectionResult);
        
        if (ConnectionResult.bSuccess)
        {
            Result.SuccessfulConnections++;
            CompletedConnections.Add(ConnectionResult);
            
            if (ConnectionResult.ConversionNode)
            {
                Result.ConversionNodesCreated++;
                Result.CreatedNodes.Add(ConnectionResult.ConversionNode);
            }
        }
        else
        {
            Result.FailedConnections++;
            
            if (BatchRequest.bStopOnFirstFailure)
            {
                if (BatchRequest.bRollbackOnFailure)
                {
                    // Rollback completed connections
                    for (const FCONNECTIONRESULT& CompletedConnection : CompletedConnections)
                    {
                        DisconnectPins(CompletedConnection.SourcePin, CompletedConnection.TargetPin);
                    }
                    Result.WarningMessages.Add(TEXT("Rolled back completed connections due to failure"));
                }
                break;
            }
        }
    }

    Result.bSuccess = (Result.FailedConnections == 0);
    Result.TotalOperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    TotalBatchOperations++;

    LogConnectionActivity(FString::Printf(TEXT("Batch operation completed: %d successful, %d failed"), 
        Result.SuccessfulConnections, Result.FailedConnections));

    UpdateConnectionStatistics(TEXT("BatchConnect"));

    return Result;
}

// Auto-Connection Features

FBATCHCONNECTIONRESULT FCONNECTIONMANAGER::AutoConnectNodes(UEdGraph* Graph, const FAUTOCONNECTIONCONFIG& Config)
{
    FBATCHCONNECTIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || !Graph)
    {
        Result.ErrorMessages.Add(TEXT("Invalid parameters or manager not initialized"));
        Result.TotalOperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    TArray<FCONNECTIONREQUEST> AllConnectionRequests;

    // Find potential connections between all nodes
    for (int32 i = 0; i < Graph->Nodes.Num(); i++)
    {
        for (int32 j = i + 1; j < Graph->Nodes.Num(); j++)
        {
            UEdGraphNode* SourceNode = Graph->Nodes[i];
            UEdGraphNode* TargetNode = Graph->Nodes[j];
            
            if (!SourceNode || !TargetNode)
            {
                continue;
            }

            TArray<FCONNECTIONREQUEST> NodeConnections = SuggestConnections(SourceNode, Graph, Config);
            AllConnectionRequests.Append(NodeConnections);
        }
    }

    // Execute auto-connections
    FBATCHCONNECTIONREQUEST BatchRequest;
    BatchRequest.ConnectionRequests = AllConnectionRequests;
    BatchRequest.bStopOnFirstFailure = false;
    BatchRequest.bValidateAllFirst = true;
    BatchRequest.bRollbackOnFailure = false;

    Result = ExecuteBatchConnections(BatchRequest);
    TotalAutoConnections += Result.SuccessfulConnections;

    LogConnectionActivity(FString::Printf(TEXT("Auto-connected %d pins in graph"), Result.SuccessfulConnections));

    UpdateConnectionStatistics(TEXT("AutoConnect"));

    return Result;
}

// Connection Validation

bool FCONNECTIONMANAGER::ValidateConnectionRequest(const FCONNECTIONREQUEST& Request, TArray<FString>& OutErrors)
{
    OutErrors.Empty();

    if (!Request.SourcePin || !Request.TargetPin)
    {
        OutErrors.Add(TEXT("Invalid source or target pin"));
        return false;
    }

    // Validate pin directions
    if (!ValidatePinDirection(Request.SourcePin, Request.TargetPin))
    {
        OutErrors.Add(TEXT("Invalid pin direction combination"));
        return false;
    }

    // Validate pin compatibility
    if (!ValidatePinCompatibility(Request.SourcePin, Request.TargetPin))
    {
        OutErrors.Add(TEXT("Pins are not compatible"));
        return false;
    }

    // Validate connection strategy
    if (!ValidateConnectionStrategy(Request))
    {
        OutErrors.Add(TEXT("Invalid connection strategy"));
        return false;
    }

    return OutErrors.Num() == 0;
}

bool FCONNECTIONMANAGER::CanConnect(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!bIsInitialized || !CompatibilityChecker.IsValid())
    {
        return false;
    }

    return CompatibilityChecker->CanPinsConnect(SourcePin, TargetPin);
}

// Utility Functions

TMap<FString, FString> FCONNECTIONMANAGER::GetConnectionStatistics()
{
    TMap<FString, FString> Stats;
    
    Stats.Add(TEXT("TotalConnections"), FString::FromInt(TotalConnections));
    Stats.Add(TEXT("TotalDisconnections"), FString::FromInt(TotalDisconnections));
    Stats.Add(TEXT("TotalBatchOperations"), FString::FromInt(TotalBatchOperations));
    Stats.Add(TEXT("TotalAutoConnections"), FString::FromInt(TotalAutoConnections));
    Stats.Add(TEXT("TotalConversionNodesCreated"), FString::FromInt(TotalConversionNodesCreated));
    Stats.Add(TEXT("LastOperationTimeMs"), FString::SanitizeFloat(LastOperationTimeMs));
    Stats.Add(TEXT("IsInitialized"), bIsInitialized ? TEXT("true") : TEXT("false"));
    
    for (const auto& StatPair : ConnectionStatistics)
    {
        Stats.Add(StatPair.Key, FString::FromInt(StatPair.Value));
    }
    
    return Stats;
}

void FCONNECTIONMANAGER::SetVerboseLogging(bool bEnabled)
{
    bVerboseLogging = bEnabled;
    LogConnectionActivity(FString::Printf(TEXT("Verbose logging %s"), bEnabled ? TEXT("enabled") : TEXT("disabled")));
}

void FCONNECTIONMANAGER::SetTrackStatistics(bool bEnabled)
{
    bTrackStatistics = bEnabled;
    LogConnectionActivity(FString::Printf(TEXT("Statistics tracking %s"), bEnabled ? TEXT("enabled") : TEXT("disabled")));
}

void FCONNECTIONMANAGER::ResetStatistics()
{
    TotalConnections = 0;
    TotalDisconnections = 0;
    TotalBatchOperations = 0;
    TotalAutoConnections = 0;
    TotalConversionNodesCreated = 0;
    LastOperationTimeMs = 0.0f;
    ConnectionStatistics.Empty();
    
    LogConnectionActivity(TEXT("Statistics reset"));
}

// Private Helper Methods

FCONNECTIONRESULT FCONNECTIONMANAGER::ConnectPinsInternal(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, EConnectionStrategy Strategy, bool bAllowConversion)
{
    FCONNECTIONRESULT Result;

    if (!SourcePin || !TargetPin)
    {
        Result.ErrorMessages.Add(TEXT("Invalid pins"));
        return Result;
    }

    // Check if pins are already connected
    if (SourcePin->LinkedTo.Contains(TargetPin))
    {
        Result.bSuccess = true;
        Result.InfoMessages.Add(TEXT("Pins are already connected"));
        return Result;
    }

    // Check compatibility
    if (CompatibilityChecker.IsValid())
    {
        FPINCOMPATIBILITYRESULT CompatibilityResult = CompatibilityChecker->CheckPinCompatibility(SourcePin, TargetPin);
        
        if (!CompatibilityResult.bIsCompatible)
        {
            if (Strategy == EConnectionStrategy::ForceConnect)
            {
                Result.WarningMessages.Add(TEXT("Forcing connection despite incompatibility"));
            }
            else
            {
                Result.ErrorMessages.Add(TEXT("Pins are not compatible"));
                return Result;
            }
        }
        else if (CompatibilityResult.ConversionType != EPinConversionType::None && bAllowConversion)
        {
            // Create conversion node if needed
            FString ConversionNodeType = CompatibilityResult.SuggestedConversionNode;
            if (!ConversionNodeType.IsEmpty())
            {
                UEdGraphNode* ConversionNode = CreateConversionNodeInternal(SourcePin, TargetPin, ConversionNodeType);
                if (ConversionNode)
                {
                    Result.ConversionNode = ConversionNode;
                    Result.InfoMessages.Add(FString::Printf(TEXT("Created conversion node: %s"), *ConversionNodeType));
                    TotalConversionNodesCreated++;
                }
            }
        }
    }

    // Make the connection
    try
    {
        SourcePin->MakeLinkTo(TargetPin);
        Result.bSuccess = true;
        Result.InfoMessages.Add(TEXT("Connection created successfully"));
    }
    catch (...)
    {
        Result.ErrorMessages.Add(TEXT("Failed to create pin connection"));
        Result.bSuccess = false;
    }

    return Result;
}

FCONNECTIONRESULT FCONNECTIONMANAGER::DisconnectPinsInternal(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    FCONNECTIONRESULT Result;

    if (!SourcePin || !TargetPin)
    {
        Result.ErrorMessages.Add(TEXT("Invalid pins"));
        return Result;
    }

    // Check if pins are connected
    if (!SourcePin->LinkedTo.Contains(TargetPin))
    {
        Result.bSuccess = true;
        Result.InfoMessages.Add(TEXT("Pins are not connected"));
        return Result;
    }

    // Break the connection
    try
    {
        SourcePin->BreakLinkTo(TargetPin);
        Result.bSuccess = true;
        Result.InfoMessages.Add(TEXT("Connection removed successfully"));
    }
    catch (...)
    {
        Result.ErrorMessages.Add(TEXT("Failed to break pin connection"));
        Result.bSuccess = false;
    }

    return Result;
}

bool FCONNECTIONMANAGER::ValidatePinDirection(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!SourcePin || !TargetPin)
    {
        return false;
    }

    // Source should be output, target should be input
    return (SourcePin->Direction == EGPD_Output && TargetPin->Direction == EGPD_Input);
}

bool FCONNECTIONMANAGER::ValidatePinCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin)
{
    if (!CompatibilityChecker.IsValid())
    {
        return true; // Assume compatible if no checker available
    }

    return CompatibilityChecker->CanPinsConnect(SourcePin, TargetPin);
}

bool FCONNECTIONMANAGER::ValidateConnectionStrategy(const FCONNECTIONREQUEST& Request)
{
    // All strategies are currently valid
    return true;
}

void FCONNECTIONMANAGER::LogConnectionActivity(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogConnectionManager, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogConnectionManager, Log, TEXT("%s"), *Message);
    }
}

void FCONNECTIONMANAGER::UpdateConnectionStatistics(const FString& Operation, const FString& ConnectionType)
{
    if (!bTrackStatistics)
    {
        return;
    }

    FString StatKey = FString::Printf(TEXT("%s_Count"), *Operation);
    if (ConnectionStatistics.Contains(StatKey))
    {
        ConnectionStatistics[StatKey]++;
    }
    else
    {
        ConnectionStatistics.Add(StatKey, 1);
    }

    if (!ConnectionType.IsEmpty())
    {
        FString TypeStatKey = FString::Printf(TEXT("%s_%s_Count"), *Operation, *ConnectionType);
        if (ConnectionStatistics.Contains(TypeStatKey))
        {
            ConnectionStatistics[TypeStatKey]++;
        }
        else
        {
            ConnectionStatistics.Add(TypeStatKey, 1);
        }
    }
} 