#include "BlueprintGeneration/BlueprintAssetCreator.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Factories/BlueprintFactory.h"
#include "Factories/BlueprintFunctionLibraryFactory.h"
#include "Factories/BlueprintInterfaceFactory.h"
#include "Factories/BlueprintMacroFactory.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_FunctionEntry.h"
#include "BlueprintGraph/Classes/K2Node_FunctionResult.h"
#include "Engine/SimpleConstructionScript.h"
#include "Components/ActorComponent.h"
#include "Components/SceneComponent.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerController.h"
#include "AIController.h"
#include "UObject/Package.h"
#include "Misc/PackageName.h"
#include "HAL/PlatformFilemanager.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "ContentBrowserModule.h"
#include "IContentBrowserSingleton.h"

DEFINE_LOG_CATEGORY(LogBlueprintAssetCreator);

FBlueprintAssetCreator::FBlueprintAssetCreator()
    : UniqueNameCounter(0)
    , bShowNotifications(true)
    , bAutoSave(true)
    , bAutoCompile(true)
{
    // Initialize default parent classes for each blueprint type
    DefaultParentClasses.Add(EBlueprintType::Actor, AActor::StaticClass());
    DefaultParentClasses.Add(EBlueprintType::Component, UActorComponent::StaticClass());
    DefaultParentClasses.Add(EBlueprintType::FunctionLibrary, UBlueprintFunctionLibrary::StaticClass());
    DefaultParentClasses.Add(EBlueprintType::Interface, UInterface::StaticClass());
    DefaultParentClasses.Add(EBlueprintType::MacroLibrary, UBlueprintMacroLibrary::StaticClass());
    DefaultParentClasses.Add(EBlueprintType::GameMode, AGameModeBase::StaticClass());
    DefaultParentClasses.Add(EBlueprintType::PlayerController, APlayerController::StaticClass());
    DefaultParentClasses.Add(EBlueprintType::AIController, AAIController::StaticClass());

    // Initialize blueprint factory classes
    BlueprintFactoryClasses.Add(EBlueprintType::Actor, UBlueprintFactory::StaticClass());
    BlueprintFactoryClasses.Add(EBlueprintType::Component, UBlueprintFactory::StaticClass());
    BlueprintFactoryClasses.Add(EBlueprintType::FunctionLibrary, UBlueprintFunctionLibraryFactory::StaticClass());
    BlueprintFactoryClasses.Add(EBlueprintType::Interface, UBlueprintInterfaceFactory::StaticClass());
    BlueprintFactoryClasses.Add(EBlueprintType::MacroLibrary, UBlueprintMacroFactory::StaticClass());
    BlueprintFactoryClasses.Add(EBlueprintType::GameMode, UBlueprintFactory::StaticClass());
    BlueprintFactoryClasses.Add(EBlueprintType::PlayerController, UBlueprintFactory::StaticClass());
    BlueprintFactoryClasses.Add(EBlueprintType::AIController, UBlueprintFactory::StaticClass());

    UE_LOG(LogBlueprintAssetCreator, Log, TEXT("BlueprintAssetCreator initialized"));
}

FBlueprintAssetCreator::~FBlueprintAssetCreator()
{
    UE_LOG(LogBlueprintAssetCreator, Log, TEXT("BlueprintAssetCreator destroyed"));
}

FBLUEPRINTCREATIONRESULT FBlueprintAssetCreator::CreateBlueprintAsset(const FBLUEPRINTCREATIONREQUEST& Request)
{
    FBLUEPRINTCREATIONRESULT Result;

    // Validate the creation request
    FString ErrorMessage;
    if (!ValidateCreationRequest(Request, ErrorMessage))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = ErrorMessage;
        LogBlueprintCreation(FString::Printf(TEXT("Blueprint creation failed: %s"), *ErrorMessage), true);
        return Result;
    }

    // Generate unique name if necessary
    FString FinalBlueprintName = Request.BlueprintName;
    if (DoesBlueprintExist(FinalBlueprintName, Request.FolderPath))
    {
        FinalBlueprintName = GenerateUniqueBlueprintName(Request.BlueprintName, Request.FolderPath);
        Result.WarningMessages.Add(FString::Printf(TEXT("Blueprint name '%s' already exists, using '%s' instead"), 
            *Request.BlueprintName, *FinalBlueprintName));
    }

    // Create the blueprint
    UBlueprint* CreatedBlueprint = CreateBlueprintOfType(FinalBlueprintName, Request.BlueprintType, Request.FolderPath);
    
    if (!CreatedBlueprint)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Failed to create blueprint asset");
        LogBlueprintCreation(FString::Printf(TEXT("Failed to create blueprint: %s"), *FinalBlueprintName), true);
        return Result;
    }

    // Set up blueprint properties based on request
    SetupBlueprintDefaults(CreatedBlueprint, Request.BlueprintType);

    // Set parent class if specified
    if (Request.ParentClass && Request.ParentClass != GetDefaultParentClass(Request.BlueprintType))
    {
        CreatedBlueprint->ParentClass = Request.ParentClass;
        FBlueprintEditorUtils::RefreshAllNodes(CreatedBlueprint);
    }

    // Save the blueprint if auto-save is enabled
    if (bAutoSave)
    {
        if (!SaveBlueprintAsset(CreatedBlueprint))
        {
            Result.WarningMessages.Add(TEXT("Blueprint created but failed to save"));
        }
    }

    // Compile the blueprint if auto-compile is enabled
    if (bAutoCompile)
    {
        if (!CompileBlueprintAsset(CreatedBlueprint))
        {
            Result.WarningMessages.Add(TEXT("Blueprint created but failed to compile"));
        }
    }

    // Refresh content browser
    RefreshContentBrowser();

    // Set up successful result
    Result.bSuccess = true;
    Result.CreatedBlueprint = CreatedBlueprint;
    Result.AssetPath = CreatedBlueprint->GetPathName();

    // Show success notification
    if (bShowNotifications)
    {
        ShowNotification(FString::Printf(TEXT("Successfully created blueprint: %s"), *FinalBlueprintName), true);
    }

    LogBlueprintCreation(FString::Printf(TEXT("Successfully created blueprint: %s at %s"), 
        *FinalBlueprintName, *Result.AssetPath));

    return Result;
}

UBlueprint* FBlueprintAssetCreator::CreateBlueprintOfType(const FString& BlueprintName, EBlueprintType BlueprintType, const FString& FolderPath)
{
    switch (BlueprintType)
    {
        case EBlueprintType::Actor:
            return CreateActorBlueprint(BlueprintName, nullptr, FolderPath);
        case EBlueprintType::Component:
            return CreateComponentBlueprint(BlueprintName, nullptr, FolderPath);
        case EBlueprintType::FunctionLibrary:
            return CreateFunctionLibraryBlueprint(BlueprintName, FolderPath);
        case EBlueprintType::Interface:
            return CreateInterfaceBlueprint(BlueprintName, FolderPath);
        case EBlueprintType::GameMode:
            return CreateActorBlueprint(BlueprintName, AGameModeBase::StaticClass(), FolderPath);
        case EBlueprintType::PlayerController:
            return CreateActorBlueprint(BlueprintName, APlayerController::StaticClass(), FolderPath);
        case EBlueprintType::AIController:
            return CreateActorBlueprint(BlueprintName, AAIController::StaticClass(), FolderPath);
        default:
            UE_LOG(LogBlueprintAssetCreator, Warning, TEXT("Unsupported blueprint type: %s"), *BlueprintTypeToString(BlueprintType));
            return nullptr;
    }
}

UBlueprint* FBlueprintAssetCreator::CreateActorBlueprint(const FString& BlueprintName, UClass* ParentClass, const FString& FolderPath)
{
    if (!ParentClass)
    {
        ParentClass = AActor::StaticClass();
    }

    // Create package
    UPackage* Package = CreatePackage(BlueprintName, FolderPath);
    if (!Package)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Failed to create package for blueprint: %s"), *BlueprintName);
        return nullptr;
    }

    // Create blueprint factory
    UBlueprintFactory* BlueprintFactory = NewObject<UBlueprintFactory>();
    BlueprintFactory->ParentClass = ParentClass;

    // Create the blueprint
    UBlueprint* NewBlueprint = Cast<UBlueprint>(BlueprintFactory->FactoryCreateNew(
        UBlueprint::StaticClass(),
        Package,
        FName(*BlueprintName),
        RF_Standalone | RF_Public,
        nullptr,
        GWarn
    ));

    if (NewBlueprint)
    {
        // Mark package as dirty
        Package->MarkPackageDirty();
        
        // Notify asset registry
        FAssetRegistryModule::AssetCreated(NewBlueprint);

        UE_LOG(LogBlueprintAssetCreator, Log, TEXT("Created Actor blueprint: %s"), *BlueprintName);
    }

    return NewBlueprint;
}

UBlueprint* FBlueprintAssetCreator::CreateComponentBlueprint(const FString& BlueprintName, UClass* ParentClass, const FString& FolderPath)
{
    if (!ParentClass)
    {
        ParentClass = UActorComponent::StaticClass();
    }

    // Create package
    UPackage* Package = CreatePackage(BlueprintName, FolderPath);
    if (!Package)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Failed to create package for component blueprint: %s"), *BlueprintName);
        return nullptr;
    }

    // Create blueprint factory
    UBlueprintFactory* BlueprintFactory = NewObject<UBlueprintFactory>();
    BlueprintFactory->ParentClass = ParentClass;

    // Create the blueprint
    UBlueprint* NewBlueprint = Cast<UBlueprint>(BlueprintFactory->FactoryCreateNew(
        UBlueprint::StaticClass(),
        Package,
        FName(*BlueprintName),
        RF_Standalone | RF_Public,
        nullptr,
        GWarn
    ));

    if (NewBlueprint)
    {
        // Mark package as dirty
        Package->MarkPackageDirty();
        
        // Notify asset registry
        FAssetRegistryModule::AssetCreated(NewBlueprint);

        UE_LOG(LogBlueprintAssetCreator, Log, TEXT("Created Component blueprint: %s"), *BlueprintName);
    }

    return NewBlueprint;
}

UBlueprint* FBlueprintAssetCreator::CreateFunctionLibraryBlueprint(const FString& BlueprintName, const FString& FolderPath)
{
    // Create package
    UPackage* Package = CreatePackage(BlueprintName, FolderPath);
    if (!Package)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Failed to create package for function library: %s"), *BlueprintName);
        return nullptr;
    }

    // Create function library factory
    UBlueprintFunctionLibraryFactory* FunctionLibraryFactory = NewObject<UBlueprintFunctionLibraryFactory>();

    // Create the blueprint
    UBlueprint* NewBlueprint = Cast<UBlueprint>(FunctionLibraryFactory->FactoryCreateNew(
        UBlueprint::StaticClass(),
        Package,
        FName(*BlueprintName),
        RF_Standalone | RF_Public,
        nullptr,
        GWarn
    ));

    if (NewBlueprint)
    {
        // Mark package as dirty
        Package->MarkPackageDirty();
        
        // Notify asset registry
        FAssetRegistryModule::AssetCreated(NewBlueprint);

        UE_LOG(LogBlueprintAssetCreator, Log, TEXT("Created Function Library blueprint: %s"), *BlueprintName);
    }

    return NewBlueprint;
}

UBlueprint* FBlueprintAssetCreator::CreateInterfaceBlueprint(const FString& BlueprintName, const FString& FolderPath)
{
    // Create package
    UPackage* Package = CreatePackage(BlueprintName, FolderPath);
    if (!Package)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Failed to create package for interface: %s"), *BlueprintName);
        return nullptr;
    }

    // Create interface factory
    UBlueprintInterfaceFactory* InterfaceFactory = NewObject<UBlueprintInterfaceFactory>();

    // Create the blueprint
    UBlueprint* NewBlueprint = Cast<UBlueprint>(InterfaceFactory->FactoryCreateNew(
        UBlueprint::StaticClass(),
        Package,
        FName(*BlueprintName),
        RF_Standalone | RF_Public,
        nullptr,
        GWarn
    ));

    if (NewBlueprint)
    {
        // Mark package as dirty
        Package->MarkPackageDirty();
        
        // Notify asset registry
        FAssetRegistryModule::AssetCreated(NewBlueprint);

        UE_LOG(LogBlueprintAssetCreator, Log, TEXT("Created Interface blueprint: %s"), *BlueprintName);
    }

    return NewBlueprint;
}

bool FBlueprintAssetCreator::SaveBlueprintAsset(UBlueprint* Blueprint)
{
    if (!Blueprint)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Cannot save null blueprint"));
        return false;
    }

    UPackage* Package = Blueprint->GetOutermost();
    if (!Package)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Blueprint has no package"));
        return false;
    }

    // Save the package
    FString PackageFileName = FPackageName::LongPackageNameToFilename(Package->GetName(), FPackageName::GetAssetPackageExtension());
    bool bSaved = UPackage::SavePackage(Package, Blueprint, EObjectFlags::RF_Public | EObjectFlags::RF_Standalone, 
        *PackageFileName, GError, nullptr, true, true, SAVE_NoError);

    if (bSaved)
    {
        UE_LOG(LogBlueprintAssetCreator, Log, TEXT("Successfully saved blueprint: %s"), *Blueprint->GetName());
    }
    else
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Failed to save blueprint: %s"), *Blueprint->GetName());
    }

    return bSaved;
}

bool FBlueprintAssetCreator::CompileBlueprintAsset(UBlueprint* Blueprint)
{
    if (!Blueprint)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Cannot compile null blueprint"));
        return false;
    }

    // Compile the blueprint
    FKismetEditorUtilities::CompileBlueprint(Blueprint);

    // Check compilation status
    bool bCompilationSuccessful = (Blueprint->Status == BS_UpToDate);

    if (bCompilationSuccessful)
    {
        UE_LOG(LogBlueprintAssetCreator, Log, TEXT("Successfully compiled blueprint: %s"), *Blueprint->GetName());
    }
    else
    {
        UE_LOG(LogBlueprintAssetCreator, Warning, TEXT("Blueprint compilation had issues: %s"), *Blueprint->GetName());
    }

    return bCompilationSuccessful;
}

bool FBlueprintAssetCreator::DeleteBlueprintAsset(UBlueprint* Blueprint)
{
    if (!Blueprint)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Cannot delete null blueprint"));
        return false;
    }

    FString BlueprintName = Blueprint->GetName();
    UPackage* Package = Blueprint->GetOutermost();

    // Remove from asset registry
    FAssetRegistryModule::AssetDeleted(Blueprint);

    // Mark for garbage collection
    Blueprint->MarkPendingKill();

    // Delete the package file
    if (Package)
    {
        FString PackageFileName = FPackageName::LongPackageNameToFilename(Package->GetName(), FPackageName::GetAssetPackageExtension());
        if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*PackageFileName))
        {
            FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*PackageFileName);
        }
    }

    UE_LOG(LogBlueprintAssetCreator, Log, TEXT("Deleted blueprint: %s"), *BlueprintName);
    return true;
}

void FBlueprintAssetCreator::RefreshContentBrowser()
{
    FContentBrowserModule& ContentBrowserModule = FModuleManager::LoadModuleChecked<FContentBrowserModule>("ContentBrowser");
    IContentBrowserSingleton& ContentBrowserSingleton = ContentBrowserModule.Get();
    ContentBrowserSingleton.SyncBrowserToAssets(TArray<FAssetData>());
}

bool FBlueprintAssetCreator::IsBlueprintNameValid(const FString& BlueprintName)
{
    // Check if name is empty
    if (BlueprintName.IsEmpty())
    {
        return false;
    }

    // Check for invalid characters
    FString InvalidChars = TEXT("\\/:*?\"<>|");
    for (int32 i = 0; i < InvalidChars.Len(); i++)
    {
        if (BlueprintName.Contains(FString::Chr(InvalidChars[i])))
        {
            return false;
        }
    }

    // Check if name starts with a number
    if (FChar::IsDigit(BlueprintName[0]))
    {
        return false;
    }

    // Check for reserved names
    TArray<FString> ReservedNames = { TEXT("CON"), TEXT("PRN"), TEXT("AUX"), TEXT("NUL") };
    for (const FString& ReservedName : ReservedNames)
    {
        if (BlueprintName.Equals(ReservedName, ESearchCase::IgnoreCase))
        {
            return false;
        }
    }

    return true;
}

bool FBlueprintAssetCreator::DoesBlueprintExist(const FString& BlueprintName, const FString& FolderPath)
{
    FString FullPath = FolderPath / BlueprintName;
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FName(*FullPath));
    return AssetData.IsValid();
}

FString FBlueprintAssetCreator::GenerateUniqueBlueprintName(const FString& BaseName, const FString& FolderPath)
{
    FString UniqueName = BaseName;
    int32 Counter = 1;

    while (DoesBlueprintExist(UniqueName, FolderPath))
    {
        UniqueName = FString::Printf(TEXT("%s_%d"), *BaseName, Counter);
        Counter++;
    }

    return UniqueName;
}

UClass* FBlueprintAssetCreator::GetDefaultParentClass(EBlueprintType BlueprintType)
{
    UClass** FoundClass = DefaultParentClasses.Find(BlueprintType);
    return FoundClass ? *FoundClass : AActor::StaticClass();
}

UClass* FBlueprintAssetCreator::GetBlueprintFactoryClass(EBlueprintType BlueprintType)
{
    UClass** FoundClass = BlueprintFactoryClasses.Find(BlueprintType);
    return FoundClass ? *FoundClass : UBlueprintFactory::StaticClass();
}

FString FBlueprintAssetCreator::BlueprintTypeToString(EBlueprintType BlueprintType)
{
    switch (BlueprintType)
    {
        case EBlueprintType::Actor: return TEXT("Actor");
        case EBlueprintType::Component: return TEXT("Component");
        case EBlueprintType::FunctionLibrary: return TEXT("Function Library");
        case EBlueprintType::Interface: return TEXT("Interface");
        case EBlueprintType::MacroLibrary: return TEXT("Macro Library");
        case EBlueprintType::AnimBlueprint: return TEXT("Animation Blueprint");
        case EBlueprintType::WidgetBlueprint: return TEXT("Widget Blueprint");
        case EBlueprintType::GameMode: return TEXT("Game Mode");
        case EBlueprintType::PlayerController: return TEXT("Player Controller");
        case EBlueprintType::AIController: return TEXT("AI Controller");
        default: return TEXT("Unknown");
    }
}

EBlueprintType FBlueprintAssetCreator::StringToBlueprintType(const FString& TypeString)
{
    if (TypeString.Equals(TEXT("Actor"), ESearchCase::IgnoreCase)) return EBlueprintType::Actor;
    if (TypeString.Equals(TEXT("Component"), ESearchCase::IgnoreCase)) return EBlueprintType::Component;
    if (TypeString.Equals(TEXT("Function Library"), ESearchCase::IgnoreCase)) return EBlueprintType::FunctionLibrary;
    if (TypeString.Equals(TEXT("Interface"), ESearchCase::IgnoreCase)) return EBlueprintType::Interface;
    if (TypeString.Equals(TEXT("Macro Library"), ESearchCase::IgnoreCase)) return EBlueprintType::MacroLibrary;
    if (TypeString.Equals(TEXT("Animation Blueprint"), ESearchCase::IgnoreCase)) return EBlueprintType::AnimBlueprint;
    if (TypeString.Equals(TEXT("Widget Blueprint"), ESearchCase::IgnoreCase)) return EBlueprintType::WidgetBlueprint;
    if (TypeString.Equals(TEXT("Game Mode"), ESearchCase::IgnoreCase)) return EBlueprintType::GameMode;
    if (TypeString.Equals(TEXT("Player Controller"), ESearchCase::IgnoreCase)) return EBlueprintType::PlayerController;
    if (TypeString.Equals(TEXT("AI Controller"), ESearchCase::IgnoreCase)) return EBlueprintType::AIController;
    
    return EBlueprintType::Actor; // Default fallback
}

FString FBlueprintAssetCreator::SanitizeBlueprintName(const FString& BlueprintName)
{
    FString SanitizedName = BlueprintName;

    // Remove invalid characters
    FString InvalidChars = TEXT("\\/:*?\"<>|");
    for (int32 i = 0; i < InvalidChars.Len(); i++)
    {
        SanitizedName = SanitizedName.Replace(*FString::Chr(InvalidChars[i]), TEXT("_"));
    }

    // Replace spaces with underscores
    SanitizedName = SanitizedName.Replace(TEXT(" "), TEXT("_"));

    // Ensure it doesn't start with a number
    if (!SanitizedName.IsEmpty() && FChar::IsDigit(SanitizedName[0]))
    {
        SanitizedName = TEXT("BP_") + SanitizedName;
    }

    // Ensure it's not empty
    if (SanitizedName.IsEmpty())
    {
        SanitizedName = TEXT("NewBlueprint");
    }

    return SanitizedName;
}

UPackage* FBlueprintAssetCreator::CreatePackage(const FString& BlueprintName, const FString& FolderPath)
{
    FString SanitizedName = SanitizeBlueprintName(BlueprintName);
    FString PackageName = FolderPath / SanitizedName;

    // Ensure package name is valid
    if (!FPackageName::IsValidLongPackageName(PackageName))
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Invalid package name: %s"), *PackageName);
        return nullptr;
    }

    // Create the package
    UPackage* Package = CreatePackage(nullptr, *PackageName);
    if (!Package)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("Failed to create package: %s"), *PackageName);
        return nullptr;
    }

    return Package;
}

void FBlueprintAssetCreator::SetupBlueprintDefaults(UBlueprint* Blueprint, EBlueprintType BlueprintType)
{
    if (!Blueprint)
    {
        return;
    }

    // Set blueprint description
    Blueprint->BlueprintDescription = FString::Printf(TEXT("Generated %s blueprint"), *BlueprintTypeToString(BlueprintType));

    // Set blueprint category
    switch (BlueprintType)
    {
        case EBlueprintType::Actor:
        case EBlueprintType::GameMode:
        case EBlueprintType::PlayerController:
        case EBlueprintType::AIController:
            Blueprint->BlueprintCategory = TEXT("Actors");
            break;
        case EBlueprintType::Component:
            Blueprint->BlueprintCategory = TEXT("Components");
            break;
        case EBlueprintType::FunctionLibrary:
            Blueprint->BlueprintCategory = TEXT("Function Libraries");
            break;
        case EBlueprintType::Interface:
            Blueprint->BlueprintCategory = TEXT("Interfaces");
            break;
        default:
            Blueprint->BlueprintCategory = TEXT("Generated");
            break;
    }

    // Mark as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);
}

bool FBlueprintAssetCreator::ValidateCreationRequest(const FBLUEPRINTCREATIONREQUEST& Request, FString& OutErrorMessage)
{
    // Validate blueprint name
    if (Request.BlueprintName.IsEmpty())
    {
        OutErrorMessage = TEXT("Blueprint name cannot be empty");
        return false;
    }

    if (!IsBlueprintNameValid(Request.BlueprintName))
    {
        OutErrorMessage = FString::Printf(TEXT("Invalid blueprint name: %s"), *Request.BlueprintName);
        return false;
    }

    // Validate folder path
    if (Request.FolderPath.IsEmpty())
    {
        OutErrorMessage = TEXT("Folder path cannot be empty");
        return false;
    }

    // Validate parent class if specified
    if (Request.ParentClass && !Request.ParentClass->IsValidLowLevel())
    {
        OutErrorMessage = TEXT("Invalid parent class specified");
        return false;
    }

    return true;
}

void FBlueprintAssetCreator::ShowNotification(const FString& Message, bool bIsSuccess)
{
    if (!bShowNotifications)
    {
        return;
    }

    FNotificationInfo Info(FText::FromString(Message));
    Info.bFireAndForget = true;
    Info.FadeOutDuration = 3.0f;
    Info.ExpireDuration = 5.0f;

    if (bIsSuccess)
    {
        Info.Image = FEditorStyle::GetBrush(TEXT("NotificationList.SuccessImage"));
    }
    else
    {
        Info.Image = FEditorStyle::GetBrush(TEXT("NotificationList.FailImage"));
    }

    FSlateNotificationManager::Get().AddNotification(Info);
}

void FBlueprintAssetCreator::LogBlueprintCreation(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogBlueprintAssetCreator, Error, TEXT("%s"), *Message);
    }
    else
    {
        UE_LOG(LogBlueprintAssetCreator, Log, TEXT("%s"), *Message);
    }
} 