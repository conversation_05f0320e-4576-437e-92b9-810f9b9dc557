#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "NLP/StructureExtractor.h"
#include "NLP/ComplexityEstimator.h"

DECLARE_LOG_CATEGORY_EXTERN(LogDependencyAnalyzer, Log, All);

/**
 * Dependency type enumeration for different kinds of blueprint element dependencies
 */
UENUM()
enum class EDependencyType : uint8
{
	/** No dependency */
	None = 0,
	
	/** Direct usage dependency (A uses B) */
	DirectUsage = 1,
	
	/** Function call dependency (A calls B) */
	FunctionCall = 2,
	
	/** Event trigger dependency (A triggers B) */
	EventTrigger = 3,
	
	/** Variable access dependency (A reads/writes B) */
	VariableAccess = 4,
	
	/** Component hierarchy dependency (A is child of B) */
	ComponentHierarchy = 5,
	
	/** Interface implementation dependency (A implements B) */
	InterfaceImplementation = 6,
	
	/** Inheritance dependency (A inherits from B) */
	Inheritance = 7,
	
	/** Compilation dependency (A must be compiled before B) */
	Compilation = 8,
	
	/** Runtime dependency (A must exist at runtime for B) */
	Runtime = 9
};

/**
 * Dependency strength enumeration indicating how critical the dependency is
 */
UENUM()
enum class EDependencyStrength : uint8
{
	/** Weak dependency - optional or easily replaceable */
	Weak = 0,
	
	/** Moderate dependency - important but not critical */
	Moderate = 1,
	
	/** Strong dependency - critical for functionality */
	Strong = 2,
	
	/** Critical dependency - absolutely required */
	Critical = 3
};

/**
 * Represents a dependency between two blueprint elements
 */
USTRUCT()
struct FBlueprintDependency
{
	GENERATED_BODY()

	/** Source element that depends on the target */
	UPROPERTY()
	FString SourceElement;

	/** Type of the source element (Variable, Function, Event, Component) */
	UPROPERTY()
	FString SourceType;

	/** Target element that is depended upon */
	UPROPERTY()
	FString TargetElement;

	/** Type of the target element */
	UPROPERTY()
	FString TargetType;

	/** Type of dependency relationship */
	UPROPERTY()
	EDependencyType DependencyType = EDependencyType::DirectUsage;

	/** Strength of the dependency */
	UPROPERTY()
	EDependencyStrength DependencyStrength = EDependencyStrength::Moderate;

	/** Description of the dependency */
	UPROPERTY()
	FString Description;

	/** Whether this dependency is bidirectional */
	UPROPERTY()
	bool bBidirectional = false;

	/** Whether this dependency can be resolved automatically */
	UPROPERTY()
	bool bCanAutoResolve = true;

	/** Confidence in this dependency detection (0.0-1.0) */
	UPROPERTY()
	float Confidence = 0.8f;

	FBlueprintDependency()
	{
		SourceElement = TEXT("");
		SourceType = TEXT("");
		TargetElement = TEXT("");
		TargetType = TEXT("");
		Description = TEXT("");
	}
};

/**
 * Represents a circular dependency chain
 */
USTRUCT()
struct FCircularDependency
{
	GENERATED_BODY()

	/** Elements involved in the circular dependency */
	UPROPERTY()
	TArray<FString> DependencyChain;

	/** Types of elements in the chain */
	UPROPERTY()
	TArray<FString> ElementTypes;

	/** Dependencies that form the cycle */
	UPROPERTY()
	TArray<FBlueprintDependency> CycleDependencies;

	/** Severity of the circular dependency */
	UPROPERTY()
	EDependencyStrength Severity = EDependencyStrength::Moderate;

	/** Suggested resolution strategies */
	UPROPERTY()
	TArray<FString> ResolutionStrategies;

	/** Whether this cycle can be automatically resolved */
	UPROPERTY()
	bool bCanAutoResolve = false;

	/** Impact on blueprint functionality if not resolved */
	UPROPERTY()
	FString ImpactDescription;
};

/**
 * Build order information for blueprint elements
 */
USTRUCT()
struct FBuildOrderInfo
{
	GENERATED_BODY()

	/** Element name */
	UPROPERTY()
	FString ElementName;

	/** Element type */
	UPROPERTY()
	FString ElementType;

	/** Build order priority (lower numbers built first) */
	UPROPERTY()
	int32 BuildPriority = 0;

	/** Elements that must be built before this one */
	UPROPERTY()
	TArray<FString> Prerequisites;

	/** Elements that depend on this one */
	UPROPERTY()
	TArray<FString> Dependents;

	/** Whether this element can be built in parallel with others */
	UPROPERTY()
	bool bCanBuildInParallel = true;

	/** Estimated build time for this element */
	UPROPERTY()
	float EstimatedBuildTime = 1.0f;

	/** Build group for parallel processing */
	UPROPERTY()
	int32 BuildGroup = 0;
};

/**
 * Complete dependency analysis result
 */
USTRUCT()
struct FDependencyAnalysisResult
{
	GENERATED_BODY()

	/** All detected dependencies */
	UPROPERTY()
	TArray<FBlueprintDependency> Dependencies;

	/** Circular dependencies found */
	UPROPERTY()
	TArray<FCircularDependency> CircularDependencies;

	/** Optimized build order */
	UPROPERTY()
	TArray<FBuildOrderInfo> BuildOrder;

	/** Elements with no dependencies (can be built first) */
	UPROPERTY()
	TArray<FString> IndependentElements;

	/** Elements with unresolved dependencies */
	UPROPERTY()
	TArray<FString> UnresolvedElements;

	/** Critical path elements (longest dependency chain) */
	UPROPERTY()
	TArray<FString> CriticalPath;

	/** Total estimated build time */
	UPROPERTY()
	float TotalBuildTime = 0.0f;

	/** Parallel build time (with optimal parallelization) */
	UPROPERTY()
	float ParallelBuildTime = 0.0f;

	/** Dependency complexity score (0.0-10.0) */
	UPROPERTY()
	float DependencyComplexity = 0.0f;

	/** Analysis warnings and recommendations */
	UPROPERTY()
	TArray<FString> AnalysisWarnings;

	/** Optimization suggestions */
	UPROPERTY()
	TArray<FString> OptimizationSuggestions;

	/** Whether the dependency structure is valid */
	UPROPERTY()
	bool bIsValidStructure = true;
};

/**
 * Dependency Analyzer - Maps blueprint element dependencies and optimizes build order
 * 
 * This component analyzes the relationships between blueprint elements to identify
 * dependencies, detect circular dependencies, and optimize the build order for
 * efficient blueprint generation.
 */
class UEBLUEPRINT_GENERATOR_API FDependencyAnalyzer
{
public:
	FDependencyAnalyzer();
	~FDependencyAnalyzer();

	// Main analysis methods
	
	/**
	 * Perform complete dependency analysis on extracted blueprint structure
	 * @param Structure The extracted blueprint structure to analyze
	 * @param ComplexityResult Complexity analysis result for context
	 * @return Complete dependency analysis result
	 */
	FDependencyAnalysisResult AnalyzeDependencies(const FExtractedBlueprintStructure& Structure,
		const FComplexityAnalysisResult& ComplexityResult);

	// Individual analysis methods
	
	/**
	 * Extract all dependencies from the blueprint structure
	 * @param Structure The blueprint structure to analyze
	 * @return Array of detected dependencies
	 */
	TArray<FBlueprintDependency> ExtractDependencies(const FExtractedBlueprintStructure& Structure);

	/**
	 * Detect circular dependencies in the dependency graph
	 * @param Dependencies Array of dependencies to analyze
	 * @return Array of circular dependencies found
	 */
	TArray<FCircularDependency> DetectCircularDependencies(const TArray<FBlueprintDependency>& Dependencies);

	/**
	 * Generate optimized build order for blueprint elements
	 * @param Structure The blueprint structure
	 * @param Dependencies Array of dependencies
	 * @param CircularDeps Array of circular dependencies
	 * @return Optimized build order information
	 */
	TArray<FBuildOrderInfo> GenerateBuildOrder(const FExtractedBlueprintStructure& Structure,
		const TArray<FBlueprintDependency>& Dependencies,
		const TArray<FCircularDependency>& CircularDeps);

	/**
	 * Calculate dependency complexity score
	 * @param Dependencies Array of dependencies
	 * @param CircularDeps Array of circular dependencies
	 * @return Complexity score (0.0-10.0)
	 */
	float CalculateDependencyComplexity(const TArray<FBlueprintDependency>& Dependencies,
		const TArray<FCircularDependency>& CircularDeps);

	/**
	 * Find critical path through the dependency graph
	 * @param BuildOrder Build order information
	 * @return Array of elements in the critical path
	 */
	TArray<FString> FindCriticalPath(const TArray<FBuildOrderInfo>& BuildOrder);

	// Dependency resolution methods
	
	/**
	 * Suggest resolution strategies for circular dependencies
	 * @param CircularDep The circular dependency to resolve
	 * @param Structure The blueprint structure for context
	 * @return Array of suggested resolution strategies
	 */
	TArray<FString> SuggestCircularDependencyResolution(const FCircularDependency& CircularDep,
		const FExtractedBlueprintStructure& Structure);

	/**
	 * Attempt to automatically resolve circular dependencies
	 * @param Dependencies Array of dependencies (modified in place)
	 * @param CircularDeps Array of circular dependencies to resolve
	 * @return Number of circular dependencies successfully resolved
	 */
	int32 AutoResolveCircularDependencies(TArray<FBlueprintDependency>& Dependencies,
		TArray<FCircularDependency>& CircularDeps);

	// Configuration and customization
	
	/**
	 * Set dependency strength threshold for different dependency types
	 * @param DependencyType Type of dependency
	 * @param Threshold Minimum strength threshold
	 */
	void SetDependencyStrengthThreshold(EDependencyType DependencyType, EDependencyStrength Threshold);

	/**
	 * Add custom dependency detection rule
	 * @param RuleName Name of the custom rule
	 * @param DetectionFunction Function to detect custom dependencies
	 */
	void AddCustomDependencyRule(const FString& RuleName,
		TFunction<TArray<FBlueprintDependency>(const FExtractedBlueprintStructure&)> DetectionFunction);

	/**
	 * Set build time estimation for different element types
	 * @param ElementType Type of element (Variable, Function, Event, Component)
	 * @param BaseTime Base build time in seconds
	 */
	void SetElementBuildTime(const FString& ElementType, float BaseTime);

	/**
	 * Get dependency analysis statistics
	 * @return Map of analysis statistics
	 */
	TMap<FString, int32> GetAnalysisStatistics() const;

private:
	// Configuration
	TMap<EDependencyType, EDependencyStrength> DependencyStrengthThresholds;
	TMap<FString, TFunction<TArray<FBlueprintDependency>(const FExtractedBlueprintStructure&)>> CustomDependencyRules;
	TMap<FString, float> ElementBuildTimes;
	
	// Analysis parameters
	float CircularDependencyThreshold;
	int32 MaxDependencyDepth;
	bool bEnableAutoResolution;
	
	// Statistics
	mutable TMap<FString, int32> AnalysisStats;
	
	// Initialization methods
	void InitializeDependencyStrengthThresholds();
	void InitializeElementBuildTimes();
	void InitializeAnalysisParameters();
	
	// Dependency extraction helpers
	TArray<FBlueprintDependency> ExtractVariableDependencies(const FExtractedBlueprintStructure& Structure);
	TArray<FBlueprintDependency> ExtractFunctionDependencies(const FExtractedBlueprintStructure& Structure);
	TArray<FBlueprintDependency> ExtractEventDependencies(const FExtractedBlueprintStructure& Structure);
	TArray<FBlueprintDependency> ExtractComponentDependencies(const FExtractedBlueprintStructure& Structure);
	TArray<FBlueprintDependency> ExtractRelationshipDependencies(const TArray<FElementRelationship>& Relationships);
	
	// Circular dependency detection helpers
	bool HasCircularDependency(const TArray<FBlueprintDependency>& Dependencies, const FString& StartElement, 
		TArray<FString>& VisitedElements, TArray<FString>& CurrentPath);
	FCircularDependency CreateCircularDependencyFromPath(const TArray<FString>& Path, 
		const TArray<FBlueprintDependency>& Dependencies);
	EDependencyStrength CalculateCircularDependencySeverity(const FCircularDependency& CircularDep);
	
	// Build order optimization helpers
	TArray<FString> TopologicalSort(const TArray<FBlueprintDependency>& Dependencies, 
		const TArray<FString>& AllElements);
	void AssignBuildPriorities(TArray<FBuildOrderInfo>& BuildOrder, const TArray<FBlueprintDependency>& Dependencies);
	void OptimizeParallelBuilds(TArray<FBuildOrderInfo>& BuildOrder);
	float CalculateTotalBuildTime(const TArray<FBuildOrderInfo>& BuildOrder, bool bParallel = false);
	
	// Critical path analysis helpers
	TArray<FString> FindLongestDependencyChain(const TArray<FBuildOrderInfo>& BuildOrder);
	float CalculatePathLength(const TArray<FString>& Path, const TArray<FBuildOrderInfo>& BuildOrder);
	
	// Resolution strategy helpers
	TArray<FString> GenerateInterfaceResolutionStrategy(const FCircularDependency& CircularDep);
	TArray<FString> GenerateEventBasedResolutionStrategy(const FCircularDependency& CircularDep);
	TArray<FString> GenerateRefactoringResolutionStrategy(const FCircularDependency& CircularDep);
	TArray<FString> GenerateWeakReferenceResolutionStrategy(const FCircularDependency& CircularDep);
	
	// Dependency strength calculation
	EDependencyStrength CalculateDependencyStrength(const FBlueprintDependency& Dependency, 
		const FExtractedBlueprintStructure& Structure);
	bool IsCriticalDependency(const FBlueprintDependency& Dependency, const FExtractedBlueprintStructure& Structure);
	
	// Analysis helpers
	TArray<FString> GenerateAnalysisWarnings(const FDependencyAnalysisResult& Result);
	TArray<FString> GenerateOptimizationSuggestions(const FDependencyAnalysisResult& Result);
	bool ValidateDependencyStructure(const FDependencyAnalysisResult& Result);
	
	// Utility methods
	FString GetElementKey(const FString& ElementName, const FString& ElementType);
	TArray<FString> GetAllElementNames(const FExtractedBlueprintStructure& Structure);
	bool DependencyExists(const TArray<FBlueprintDependency>& Dependencies, const FString& Source, const FString& Target);
	void UpdateAnalysisStatistics(const FDependencyAnalysisResult& Result) const;
	
	// Validation methods
	bool ValidateAnalysisResult(const FDependencyAnalysisResult& Result) const;
	TArray<FString> GetAnalysisWarnings(const FDependencyAnalysisResult& Result) const;
}; 