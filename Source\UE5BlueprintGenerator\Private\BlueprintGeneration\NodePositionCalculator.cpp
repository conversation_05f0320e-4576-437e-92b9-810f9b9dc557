#include "BlueprintGeneration/NodePositionCalculator.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "EdGraph/EdGraph.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

DEFINE_LOG_CATEGORY(LogNodePositionCalculator);

FNodePositionCalculator::FNodePositionCalculator()
    : bIsInitialized(false)
    , bVerboseLogging(false)
    , bTrackPositionStatistics(true)
    , TotalLayoutCalculations(0)
    , TotalCollisionDetections(0)
    , LastLayoutTimeMs(0.0f)
{
    UE_LOG(LogNodePositionCalculator, Log, TEXT("NodePositionCalculator created"));
}

FNodePositionCalculator::~FNodePositionCalculator()
{
    Shutdown();
    UE_LOG(LogNodePositionCalculator, Log, TEXT("NodePositionCalculator destroyed"));
}

bool FNodePositionCalculator::Initialize()
{
    if (bIsInitialized)
    {
        UE_LOG(LogNodePositionCalculator, Warning, TEXT("NodePositionCalculator already initialized"));
        return true;
    }

    UE_LOG(LogNodePositionCalculator, Log, TEXT("Initializing NodePositionCalculator..."));

    // Clear existing data
    PositionStatistics.Empty();
    LayoutPresets.Empty();

    // Set preset directory
    PresetDirectory = FPaths::ProjectPluginsDir() / TEXT("UE5BlueprintGenerator") / TEXT("Presets") / TEXT("Layouts");

    // Create preset directory if it doesn't exist
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*PresetDirectory))
    {
        PlatformFile.CreateDirectoryTree(*PresetDirectory);
    }

    // Initialize default configurations
    InitializeDefaultConfigurations();

    bIsInitialized = true;
    
    UE_LOG(LogNodePositionCalculator, Log, TEXT("NodePositionCalculator initialized successfully"));
    LogPositionActivity(TEXT("Position calculator initialized"));

    return true;
}

void FNodePositionCalculator::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    UE_LOG(LogNodePositionCalculator, Log, TEXT("Shutting down NodePositionCalculator..."));

    // Clear all data
    PositionStatistics.Empty();
    LayoutPresets.Empty();

    bIsInitialized = false;
    
    UE_LOG(LogNodePositionCalculator, Log, TEXT("NodePositionCalculator shutdown complete"));
}

FLAYOUTCALCULATIONRESULT FNodePositionCalculator::CalculateLayout(UEdGraph* Graph, const FLAYOUTCONFIGURATION& Configuration)
{
    FLAYOUTCALCULATIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || !Graph)
    {
        Result.ErrorMessages.Add(TEXT("Invalid graph or calculator not initialized"));
        Result.CalculationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Get all nodes from the graph
    TArray<UK2Node*> Nodes;
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (UK2Node* K2Node = Cast<UK2Node>(Node))
        {
            Nodes.Add(K2Node);
        }
    }

    if (Nodes.Num() == 0)
    {
        Result.bSuccess = true;
        Result.CalculationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        Result.WarningMessages.Add(TEXT("No nodes found in graph"));
        return Result;
    }

    // Calculate layout for the nodes
    Result = CalculateLayoutForNodes(Nodes, Configuration);
    
    LogPositionActivity(FString::Printf(TEXT("Calculated layout for %d nodes in %.2fms"), 
        Nodes.Num(), Result.CalculationTimeMs));
    UpdatePositionStatistics(TEXT("CalculateLayout"), Nodes.Num());

    return Result;
}

FLAYOUTCALCULATIONRESULT FNodePositionCalculator::CalculateLayoutForNodes(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    FLAYOUTCALCULATIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || Nodes.Num() == 0)
    {
        Result.ErrorMessages.Add(TEXT("Invalid nodes or calculator not initialized"));
        Result.CalculationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    // Apply the specified layout algorithm
    switch (Configuration.Algorithm)
    {
        case ENodeLayoutAlgorithm::Linear:
            Result = ApplyLinearLayout(Nodes, Configuration);
            break;
        case ENodeLayoutAlgorithm::Grid:
            Result = ApplyGridLayout(Nodes, Configuration);
            break;
        case ENodeLayoutAlgorithm::Hierarchical:
            Result = ApplyHierarchicalLayout(Nodes, Configuration);
            break;
        case ENodeLayoutAlgorithm::Force:
            Result = ApplyForceDirectedLayout(Nodes, Configuration);
            break;
        case ENodeLayoutAlgorithm::Tree:
            Result = ApplyTreeLayout(Nodes, Configuration);
            break;
        default:
            Result = ApplyHierarchicalLayout(Nodes, Configuration); // Default fallback
            break;
    }

    // Detect and resolve collisions if requested
    if (Configuration.bAvoidOverlaps && Result.bSuccess)
    {
        FCOLLISIONDETECTIONRESULT CollisionResult = DetectCollisions(Nodes);
        if (CollisionResult.bHasCollisions)
        {
            bool bResolved = ResolveOverlaps(Nodes, Configuration);
            if (!bResolved)
            {
                Result.WarningMessages.Add(TEXT("Some node overlaps could not be resolved"));
            }
        }
    }

    // Calculate layout quality
    if (Result.bSuccess)
    {
        Result.LayoutQuality = EvaluateLayoutQuality(Nodes, Configuration);
    }

    Result.CalculationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    LastLayoutTimeMs = Result.CalculationTimeMs;
    TotalLayoutCalculations++;

    return Result;
}

FLAYOUTCALCULATIONRESULT FNodePositionCalculator::ApplyLinearLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    FLAYOUTCALCULATIONRESULT Result;
    
    if (Nodes.Num() == 0)
    {
        Result.bSuccess = true;
        return Result;
    }

    // Create position info for each node
    TArray<FNODEPOSITIONINFO> NodePositions;
    for (int32 i = 0; i < Nodes.Num(); i++)
    {
        FNODEPOSITIONINFO PositionInfo;
        PositionInfo.Node = Nodes[i];
        PositionInfo.OriginalPosition = FVector2D(Nodes[i]->NodePosX, Nodes[i]->NodePosY);
        PositionInfo.Priority = 1.0f;
        PositionInfo.bIsFixed = false;
        NodePositions.Add(PositionInfo);
    }

    // Apply linear layout
    ImplementLinearLayout(NodePositions, Configuration);

    // Apply positions to nodes
    for (const FNODEPOSITIONINFO& PositionInfo : NodePositions)
    {
        if (PositionInfo.Node)
        {
            PositionInfo.Node->NodePosX = PositionInfo.NewPosition.X;
            PositionInfo.Node->NodePosY = PositionInfo.NewPosition.Y;
        }
    }

    Result.bSuccess = true;
    Result.ProcessedNodeCount = Nodes.Num();
    
    LogPositionActivity(FString::Printf(TEXT("Applied linear layout to %d nodes"), Nodes.Num()));
    
    return Result;
}

FLAYOUTCALCULATIONRESULT FNodePositionCalculator::ApplyGridLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    FLAYOUTCALCULATIONRESULT Result;
    
    if (Nodes.Num() == 0)
    {
        Result.bSuccess = true;
        return Result;
    }

    // Create position info for each node
    TArray<FNODEPOSITIONINFO> NodePositions;
    for (int32 i = 0; i < Nodes.Num(); i++)
    {
        FNODEPOSITIONINFO PositionInfo;
        PositionInfo.Node = Nodes[i];
        PositionInfo.OriginalPosition = FVector2D(Nodes[i]->NodePosX, Nodes[i]->NodePosY);
        PositionInfo.Priority = 1.0f;
        PositionInfo.bIsFixed = false;
        NodePositions.Add(PositionInfo);
    }

    // Apply grid layout
    ImplementGridLayout(NodePositions, Configuration);

    // Apply positions to nodes
    for (const FNODEPOSITIONINFO& PositionInfo : NodePositions)
    {
        if (PositionInfo.Node)
        {
            PositionInfo.Node->NodePosX = PositionInfo.NewPosition.X;
            PositionInfo.Node->NodePosY = PositionInfo.NewPosition.Y;
        }
    }

    Result.bSuccess = true;
    Result.ProcessedNodeCount = Nodes.Num();
    
    LogPositionActivity(FString::Printf(TEXT("Applied grid layout to %d nodes"), Nodes.Num()));
    
    return Result;
}

FLAYOUTCALCULATIONRESULT FNodePositionCalculator::ApplyHierarchicalLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    FLAYOUTCALCULATIONRESULT Result;
    
    if (Nodes.Num() == 0)
    {
        Result.bSuccess = true;
        return Result;
    }

    // Create position info for each node
    TArray<FNODEPOSITIONINFO> NodePositions;
    for (int32 i = 0; i < Nodes.Num(); i++)
    {
        FNODEPOSITIONINFO PositionInfo;
        PositionInfo.Node = Nodes[i];
        PositionInfo.OriginalPosition = FVector2D(Nodes[i]->NodePosX, Nodes[i]->NodePosY);
        PositionInfo.Priority = 1.0f;
        PositionInfo.bIsFixed = false;
        NodePositions.Add(PositionInfo);
    }

    // Apply hierarchical layout
    ImplementHierarchicalLayout(NodePositions, Configuration);

    // Apply positions to nodes
    for (const FNODEPOSITIONINFO& PositionInfo : NodePositions)
    {
        if (PositionInfo.Node)
        {
            PositionInfo.Node->NodePosX = PositionInfo.NewPosition.X;
            PositionInfo.Node->NodePosY = PositionInfo.NewPosition.Y;
        }
    }

    Result.bSuccess = true;
    Result.ProcessedNodeCount = Nodes.Num();
    
    LogPositionActivity(FString::Printf(TEXT("Applied hierarchical layout to %d nodes"), Nodes.Num()));
    
    return Result;
}

FLAYOUTCALCULATIONRESULT FNodePositionCalculator::ApplyForceDirectedLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    FLAYOUTCALCULATIONRESULT Result;
    
    if (Nodes.Num() == 0)
    {
        Result.bSuccess = true;
        return Result;
    }

    // Create position info for each node
    TArray<FNODEPOSITIONINFO> NodePositions;
    for (int32 i = 0; i < Nodes.Num(); i++)
    {
        FNODEPOSITIONINFO PositionInfo;
        PositionInfo.Node = Nodes[i];
        PositionInfo.OriginalPosition = FVector2D(Nodes[i]->NodePosX, Nodes[i]->NodePosY);
        PositionInfo.Priority = 1.0f;
        PositionInfo.bIsFixed = false;
        NodePositions.Add(PositionInfo);
    }

    // Apply force-directed layout
    ImplementForceDirectedLayout(NodePositions, Configuration);

    // Apply positions to nodes
    for (const FNODEPOSITIONINFO& PositionInfo : NodePositions)
    {
        if (PositionInfo.Node)
        {
            PositionInfo.Node->NodePosX = PositionInfo.NewPosition.X;
            PositionInfo.Node->NodePosY = PositionInfo.NewPosition.Y;
        }
    }

    Result.bSuccess = true;
    Result.ProcessedNodeCount = Nodes.Num();
    
    LogPositionActivity(FString::Printf(TEXT("Applied force-directed layout to %d nodes"), Nodes.Num()));
    
    return Result;
}

FLAYOUTCALCULATIONRESULT FNodePositionCalculator::ApplyTreeLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    FLAYOUTCALCULATIONRESULT Result;
    
    if (Nodes.Num() == 0)
    {
        Result.bSuccess = true;
        return Result;
    }

    // For now, use hierarchical layout as tree layout implementation
    Result = ApplyHierarchicalLayout(Nodes, Configuration);
    
    LogPositionActivity(FString::Printf(TEXT("Applied tree layout to %d nodes"), Nodes.Num()));
    
    return Result;
}

FCOLLISIONDETECTIONRESULT FNodePositionCalculator::DetectCollisions(const TArray<UK2Node*>& Nodes)
{
    FCOLLISIONDETECTIONRESULT Result;
    
    if (!bIsInitialized || Nodes.Num() < 2)
    {
        return Result;
    }

    // Check each pair of nodes for overlaps
    for (int32 i = 0; i < Nodes.Num(); i++)
    {
        for (int32 j = i + 1; j < Nodes.Num(); j++)
        {
            if (DoNodesOverlap(Nodes[i], Nodes[j], 10.0f)) // 10 pixel margin
            {
                Result.bHasCollisions = true;
                Result.CollidingNodePairs.Add(TPair<UK2Node*, UK2Node*>(Nodes[i], Nodes[j]));
            }
        }
    }

    Result.CollisionCount = Result.CollidingNodePairs.Num();
    TotalCollisionDetections++;
    
    LogPositionActivity(FString::Printf(TEXT("Detected %d collisions among %d nodes"), 
        Result.CollisionCount, Nodes.Num()));
    
    return Result;
}

bool FNodePositionCalculator::ResolveOverlaps(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    if (!bIsInitialized || Nodes.Num() < 2)
    {
        return true;
    }

    bool bAllResolved = true;
    int32 MaxIterations = 10;
    int32 Iteration = 0;

    while (Iteration < MaxIterations)
    {
        FCOLLISIONDETECTIONRESULT CollisionResult = DetectCollisions(Nodes);
        
        if (!CollisionResult.bHasCollisions)
        {
            break; // All overlaps resolved
        }

        // Resolve each collision by moving nodes apart
        for (const auto& CollidingPair : CollisionResult.CollidingNodePairs)
        {
            UK2Node* Node1 = CollidingPair.Key;
            UK2Node* Node2 = CollidingPair.Value;
            
            if (!Node1 || !Node2)
            {
                continue;
            }

            // Calculate separation vector
            FVector2D Node1Pos(Node1->NodePosX, Node1->NodePosY);
            FVector2D Node2Pos(Node2->NodePosX, Node2->NodePosY);
            FVector2D Separation = Node2Pos - Node1Pos;
            
            if (Separation.SizeSquared() < SMALL_NUMBER)
            {
                // Nodes are at the same position, use default separation
                Separation = FVector2D(Configuration.HorizontalSpacing, 0.0f);
            }
            else
            {
                Separation.Normalize();
                Separation *= Configuration.HorizontalSpacing;
            }

            // Move nodes apart
            Node1->NodePosX = Node1Pos.X - Separation.X * 0.5f;
            Node1->NodePosY = Node1Pos.Y - Separation.Y * 0.5f;
            Node2->NodePosX = Node2Pos.X + Separation.X * 0.5f;
            Node2->NodePosY = Node2Pos.Y + Separation.Y * 0.5f;
        }

        Iteration++;
    }

    if (Iteration >= MaxIterations)
    {
        bAllResolved = false;
        LogPositionActivity(TEXT("Could not resolve all overlaps within maximum iterations"), true);
    }

    return bAllResolved;
}

bool FNodePositionCalculator::DoNodesOverlap(UK2Node* Node1, UK2Node* Node2, float Margin)
{
    if (!Node1 || !Node2)
    {
        return false;
    }

    FVector2D Node1Size = GetNodeSize(Node1);
    FVector2D Node2Size = GetNodeSize(Node2);
    
    FVector2D Node1Pos(Node1->NodePosX, Node1->NodePosY);
    FVector2D Node2Pos(Node2->NodePosX, Node2->NodePosY);

    // Calculate bounding boxes with margin
    FVector4 Node1Bounds(
        Node1Pos.X - Margin,
        Node1Pos.Y - Margin,
        Node1Pos.X + Node1Size.X + Margin,
        Node1Pos.Y + Node1Size.Y + Margin
    );

    FVector4 Node2Bounds(
        Node2Pos.X - Margin,
        Node2Pos.Y - Margin,
        Node2Pos.X + Node2Size.X + Margin,
        Node2Pos.Y + Node2Size.Y + Margin
    );

    // Check for overlap
    return !(Node1Bounds.Z < Node2Bounds.X || Node2Bounds.Z < Node1Bounds.X ||
             Node1Bounds.W < Node2Bounds.Y || Node2Bounds.W < Node1Bounds.Y);
}

FVector2D FNodePositionCalculator::GetNodeSize(UK2Node* Node)
{
    if (!Node)
    {
        return FVector2D(120.0f, 60.0f); // Default size
    }

    // This is a simplified implementation
    // In a full implementation, you would get the actual node size from the editor
    return FVector2D(120.0f, 60.0f);
}

float FNodePositionCalculator::EvaluateLayoutQuality(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration)
{
    if (Nodes.Num() == 0)
    {
        return 1.0f;
    }

    float QualityScore = 1.0f;

    // Calculate connection length penalty
    float ConnectionPenalty = CalculateConnectionLengthPenalty(Nodes);
    QualityScore -= ConnectionPenalty * 0.3f;

    // Calculate overlap penalty
    float OverlapPenalty = CalculateOverlapPenalty(Nodes);
    QualityScore -= OverlapPenalty * 0.4f;

    // Calculate alignment score
    float AlignmentScore = CalculateAlignmentScore(Nodes);
    QualityScore += AlignmentScore * 0.3f;

    return FMath::Clamp(QualityScore, 0.0f, 1.0f);
}

// Implementation helper methods

void FNodePositionCalculator::ImplementLinearLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration)
{
    if (NodePositions.Num() == 0)
    {
        return;
    }

    FVector2D StartPosition = Configuration.LayoutPadding;
    FVector2D Direction(1.0f, 0.0f); // Horizontal by default

    for (int32 i = 0; i < NodePositions.Num(); i++)
    {
        NodePositions[i].NewPosition = StartPosition + Direction * (i * Configuration.HorizontalSpacing);
    }
}

void FNodePositionCalculator::ImplementGridLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration)
{
    if (NodePositions.Num() == 0)
    {
        return;
    }

    // Calculate grid dimensions
    int32 GridWidth = FMath::CeilToInt(FMath::Sqrt(static_cast<float>(NodePositions.Num())));
    int32 GridHeight = FMath::CeilToInt(static_cast<float>(NodePositions.Num()) / GridWidth);

    FVector2D StartPosition = Configuration.LayoutPadding;

    for (int32 i = 0; i < NodePositions.Num(); i++)
    {
        int32 Row = i / GridWidth;
        int32 Col = i % GridWidth;
        
        NodePositions[i].NewPosition = StartPosition + FVector2D(
            Col * Configuration.HorizontalSpacing,
            Row * Configuration.VerticalSpacing
        );
    }
}

void FNodePositionCalculator::ImplementHierarchicalLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration)
{
    if (NodePositions.Num() == 0)
    {
        return;
    }

    // Simple hierarchical layout - arrange nodes in layers
    FVector2D StartPosition = Configuration.LayoutPadding;
    int32 NodesPerLayer = FMath::Max(1, FMath::CeilToInt(FMath::Sqrt(static_cast<float>(NodePositions.Num()))));

    for (int32 i = 0; i < NodePositions.Num(); i++)
    {
        int32 Layer = i / NodesPerLayer;
        int32 PositionInLayer = i % NodesPerLayer;
        
        NodePositions[i].NewPosition = StartPosition + FVector2D(
            PositionInLayer * Configuration.HorizontalSpacing,
            Layer * Configuration.VerticalSpacing
        );
    }
}

void FNodePositionCalculator::ImplementForceDirectedLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration, int32 MaxIterations)
{
    if (NodePositions.Num() == 0)
    {
        return;
    }

    // Initialize positions if not set
    for (int32 i = 0; i < NodePositions.Num(); i++)
    {
        if (NodePositions[i].NewPosition.IsZero())
        {
            NodePositions[i].NewPosition = NodePositions[i].OriginalPosition;
        }
    }

    // Force-directed algorithm
    for (int32 Iteration = 0; Iteration < MaxIterations; Iteration++)
    {
        TArray<FVector2D> Forces;
        Forces.SetNum(NodePositions.Num());

        // Calculate forces for each node
        for (int32 i = 0; i < NodePositions.Num(); i++)
        {
            FVector2D TotalForce = FVector2D::ZeroVector;

            // Repulsive forces from other nodes
            for (int32 j = 0; j < NodePositions.Num(); j++)
            {
                if (i != j)
                {
                    FVector2D RepulsiveForce = CalculateRepulsiveForce(NodePositions[i], NodePositions[j]);
                    TotalForce += RepulsiveForce;
                }
            }

            Forces[i] = TotalForce;
        }

        // Apply forces
        ApplyForces(NodePositions, Forces);
    }
}

FVector2D FNodePositionCalculator::CalculateRepulsiveForce(const FNODEPOSITIONINFO& Node1, const FNODEPOSITIONINFO& Node2)
{
    FVector2D Direction = Node1.NewPosition - Node2.NewPosition;
    float Distance = Direction.Size();
    
    if (Distance < SMALL_NUMBER)
    {
        Direction = FVector2D(1.0f, 0.0f); // Default direction
        Distance = 1.0f;
    }
    else
    {
        Direction.Normalize();
    }

    // Repulsive force inversely proportional to distance squared
    float ForceStrength = 10000.0f / (Distance * Distance);
    return Direction * ForceStrength;
}

FVector2D FNodePositionCalculator::CalculateAttractiveForce(const FNODEPOSITIONINFO& Node1, const FNODEPOSITIONINFO& Node2)
{
    FVector2D Direction = Node2.NewPosition - Node1.NewPosition;
    float Distance = Direction.Size();
    
    if (Distance < SMALL_NUMBER)
    {
        return FVector2D::ZeroVector;
    }

    Direction.Normalize();

    // Attractive force proportional to distance
    float ForceStrength = Distance * 0.1f;
    return Direction * ForceStrength;
}

void FNodePositionCalculator::ApplyForces(TArray<FNODEPOSITIONINFO>& NodePositions, const TArray<FVector2D>& Forces, float DampingFactor)
{
    for (int32 i = 0; i < NodePositions.Num(); i++)
    {
        if (!NodePositions[i].bIsFixed)
        {
            NodePositions[i].NewPosition += Forces[i] * DampingFactor;
        }
    }
}

float FNodePositionCalculator::CalculateConnectionLengthPenalty(const TArray<UK2Node*>& Nodes)
{
    // Simplified implementation - would analyze actual connections in full version
    return 0.0f;
}

float FNodePositionCalculator::CalculateOverlapPenalty(const TArray<UK2Node*>& Nodes)
{
    FCOLLISIONDETECTIONRESULT CollisionResult = DetectCollisions(Nodes);
    
    if (Nodes.Num() == 0)
    {
        return 0.0f;
    }

    return static_cast<float>(CollisionResult.CollisionCount) / static_cast<float>(Nodes.Num());
}

float FNodePositionCalculator::CalculateAlignmentScore(const TArray<UK2Node*>& Nodes)
{
    // Simplified implementation - would analyze node alignment in full version
    return 0.5f;
}

void FNodePositionCalculator::InitializeDefaultConfigurations()
{
    // Initialize default layout configurations for different graph types
    FLAYOUTCONFIGURATION DefaultConfig;
    DefaultConfig.Algorithm = ENodeLayoutAlgorithm::Hierarchical;
    DefaultConfig.HorizontalSpacing = 400.0f;
    DefaultConfig.VerticalSpacing = 300.0f;
    DefaultConfig.bAvoidOverlaps = true;
    
    LayoutPresets.Add(TEXT("Default"), DefaultConfig);
    
    LogPositionActivity(TEXT("Initialized default layout configurations"));
}

void FNodePositionCalculator::LogPositionActivity(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogNodePositionCalculator, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogNodePositionCalculator, Log, TEXT("%s"), *Message);
    }
}

void FNodePositionCalculator::UpdatePositionStatistics(const FString& Operation, int32 NodeCount)
{
    if (!bTrackPositionStatistics)
    {
        return;
    }

    FString StatKey = FString::Printf(TEXT("%s_Count"), *Operation);
    if (PositionStatistics.Contains(StatKey))
    {
        PositionStatistics[StatKey]++;
    }
    else
    {
        PositionStatistics.Add(StatKey, 1);
    }
} 