#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "EdGraph/EdGraph.h"
#include "BlueprintGeneration/PinCompatibilityChecker.h"
#include "BlueprintGeneration/ConnectionManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogGraphValidator, Log, All);

// Forward declarations
class UBlueprint;
class UEdGraphNode;
class UEdGraphPin;
class UEdGraph;

/**
 * Enumeration for validation severity levels
 */
UENUM(BlueprintType)
enum class EValidationSeverity : uint8
{
    Info,           // Informational message
    Warning,        // Warning that should be addressed
    Error,          // Error that prevents compilation
    Critical        // Critical error that breaks functionality
};

/**
 * Enumeration for validation categories
 */
UENUM(BlueprintType)
enum class EValidationCategory : uint8
{
    Structure,      // Graph structure validation
    Connections,    // Pin connection validation
    Nodes,          // Individual node validation
    Logic,          // Logic flow validation
    Performance,    // Performance-related validation
    Compliance,     // UE5 standards compliance
    Compilation,    // Compilation-related validation
    Runtime         // Runtime behavior validation
};

/**
 * Enumeration for validation rule types
 */
UENUM(BlueprintType)
enum class EValidationRuleType : uint8
{
    Required,       // Must pass for valid blueprint
    Recommended,    // Should pass for best practices
    Optional,       // Nice to have but not required
    Performance,    // Performance optimization suggestions
    Style           // Code style and organization
};

/**
 * Structure representing a validation issue
 */
USTRUCT(BlueprintType)
struct FVALIDATIONISSUE
{
    GENERATED_BODY()

    /** Severity of the validation issue */
    UPROPERTY()
    EValidationSeverity Severity = EValidationSeverity::Info;

    /** Category of the validation issue */
    UPROPERTY()
    EValidationCategory Category = EValidationCategory::Structure;

    /** Type of validation rule that was violated */
    UPROPERTY()
    EValidationRuleType RuleType = EValidationRuleType::Required;

    /** Short description of the issue */
    UPROPERTY()
    FString Title;

    /** Detailed description of the issue */
    UPROPERTY()
    FString Description;

    /** Suggested fix for the issue */
    UPROPERTY()
    FString SuggestedFix;

    /** Node associated with the issue (if any) */
    UPROPERTY()
    UEdGraphNode* Node = nullptr;

    /** Pin associated with the issue (if any) */
    UPROPERTY()
    UEdGraphPin* Pin = nullptr;

    /** Additional context information */
    UPROPERTY()
    TMap<FString, FString> Context;

    /** Rule ID that was violated */
    UPROPERTY()
    FString RuleId;

    /** Whether this issue can be auto-fixed */
    UPROPERTY()
    bool bCanAutoFix = false;

    FVALIDATIONISSUE()
    {
        Severity = EValidationSeverity::Info;
        Category = EValidationCategory::Structure;
        RuleType = EValidationRuleType::Required;
        Node = nullptr;
        Pin = nullptr;
        bCanAutoFix = false;
    }
};

/**
 * Structure for validation configuration
 */
USTRUCT(BlueprintType)
struct FVALIDATIONCONFIG
{
    GENERATED_BODY()

    /** Whether to validate graph structure */
    UPROPERTY()
    bool bValidateStructure = true;

    /** Whether to validate pin connections */
    UPROPERTY()
    bool bValidateConnections = true;

    /** Whether to validate individual nodes */
    UPROPERTY()
    bool bValidateNodes = true;

    /** Whether to validate logic flow */
    UPROPERTY()
    bool bValidateLogic = true;

    /** Whether to validate performance aspects */
    UPROPERTY()
    bool bValidatePerformance = true;

    /** Whether to validate UE5 compliance */
    UPROPERTY()
    bool bValidateCompliance = true;

    /** Whether to validate compilation requirements */
    UPROPERTY()
    bool bValidateCompilation = true;

    /** Whether to validate runtime behavior */
    UPROPERTY()
    bool bValidateRuntime = true;

    /** Minimum severity level to report */
    UPROPERTY()
    EValidationSeverity MinSeverityLevel = EValidationSeverity::Info;

    /** Maximum number of issues to report per category */
    UPROPERTY()
    int32 MaxIssuesPerCategory = 50;

    /** Whether to stop validation on first critical error */
    UPROPERTY()
    bool bStopOnCriticalError = false;

    /** Whether to include performance suggestions */
    UPROPERTY()
    bool bIncludePerformanceSuggestions = true;

    /** Whether to include style suggestions */
    UPROPERTY()
    bool bIncludeStyleSuggestions = false;

    FVALIDATIONCONFIG()
    {
        bValidateStructure = true;
        bValidateConnections = true;
        bValidateNodes = true;
        bValidateLogic = true;
        bValidatePerformance = true;
        bValidateCompliance = true;
        bValidateCompilation = true;
        bValidateRuntime = true;
        MinSeverityLevel = EValidationSeverity::Info;
        MaxIssuesPerCategory = 50;
        bStopOnCriticalError = false;
        bIncludePerformanceSuggestions = true;
        bIncludeStyleSuggestions = false;
    }
};

/**
 * Structure for validation results
 */
USTRUCT(BlueprintType)
struct FVALIDATIONRESULT
{
    GENERATED_BODY()

    /** Whether the validation passed overall */
    UPROPERTY()
    bool bIsValid = true;

    /** Whether the graph can be compiled */
    UPROPERTY()
    bool bCanCompile = true;

    /** All validation issues found */
    UPROPERTY()
    TArray<FVALIDATIONISSUE> Issues;

    /** Number of issues by severity */
    UPROPERTY()
    TMap<EValidationSeverity, int32> IssueCountBySeverity;

    /** Number of issues by category */
    UPROPERTY()
    TMap<EValidationCategory, int32> IssueCountByCategory;

    /** Time taken for validation (in milliseconds) */
    UPROPERTY()
    float ValidationTimeMs = 0.0f;

    /** Number of nodes validated */
    UPROPERTY()
    int32 NodesValidated = 0;

    /** Number of connections validated */
    UPROPERTY()
    int32 ConnectionsValidated = 0;

    /** Overall quality score (0.0-1.0) */
    UPROPERTY()
    float QualityScore = 1.0f;

    /** Performance score (0.0-1.0) */
    UPROPERTY()
    float PerformanceScore = 1.0f;

    /** Compliance score (0.0-1.0) */
    UPROPERTY()
    float ComplianceScore = 1.0f;

    /** Summary of validation results */
    UPROPERTY()
    FString Summary;

    FVALIDATIONRESULT()
    {
        bIsValid = true;
        bCanCompile = true;
        ValidationTimeMs = 0.0f;
        NodesValidated = 0;
        ConnectionsValidated = 0;
        QualityScore = 1.0f;
        PerformanceScore = 1.0f;
        ComplianceScore = 1.0f;
    }
};

/**
 * Structure for validation rule definition
 */
USTRUCT(BlueprintType)
struct FVALIDATIONRULE
{
    GENERATED_BODY()

    /** Unique identifier for the rule */
    UPROPERTY()
    FString RuleId;

    /** Human-readable name for the rule */
    UPROPERTY()
    FString RuleName;

    /** Description of what the rule checks */
    UPROPERTY()
    FString Description;

    /** Category of the rule */
    UPROPERTY()
    EValidationCategory Category = EValidationCategory::Structure;

    /** Type of the rule */
    UPROPERTY()
    EValidationRuleType RuleType = EValidationRuleType::Required;

    /** Default severity for violations of this rule */
    UPROPERTY()
    EValidationSeverity DefaultSeverity = EValidationSeverity::Warning;

    /** Whether this rule is enabled by default */
    UPROPERTY()
    bool bEnabledByDefault = true;

    /** Whether violations of this rule can be auto-fixed */
    UPROPERTY()
    bool bCanAutoFix = false;

    /** Tags for organizing rules */
    UPROPERTY()
    TArray<FString> Tags;

    FVALIDATIONRULE()
    {
        Category = EValidationCategory::Structure;
        RuleType = EValidationRuleType::Required;
        DefaultSeverity = EValidationSeverity::Warning;
        bEnabledByDefault = true;
        bCanAutoFix = false;
    }
};

/**
 * Graph Validator class for validating blueprint graph integrity and compliance
 */
class FGRAPHVALIDATOR
{
public:
    /** Constructor */
    FGRAPHVALIDATOR();

    /** Destructor */
    ~FGRAPHVALIDATOR();

    // Initialization and Configuration

    /** Initialize the graph validator */
    bool Initialize();

    /** Shutdown the graph validator */
    void Shutdown();

    /** Check if the graph validator is initialized */
    bool IsInitialized() const { return bIsInitialized; }

    /** Set the pin compatibility checker to use */
    void SetPinCompatibilityChecker(TSharedPtr<FPinCompatibilityChecker> InCompatibilityChecker);

    /** Set the connection manager to use */
    void SetConnectionManager(TSharedPtr<FCONNECTIONMANAGER> InConnectionManager);

    // Validation Operations

    /** Validate a complete blueprint graph */
    FVALIDATIONRESULT ValidateGraph(UEdGraph* Graph, const FVALIDATIONCONFIG& Config = FVALIDATIONCONFIG());

    /** Validate a specific node */
    TArray<FVALIDATIONISSUE> ValidateNode(UEdGraphNode* Node, const FVALIDATIONCONFIG& Config = FVALIDATIONCONFIG());

    /** Validate a specific connection */
    TArray<FVALIDATIONISSUE> ValidateConnection(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, const FVALIDATIONCONFIG& Config = FVALIDATIONCONFIG());

    /** Validate graph structure */
    TArray<FVALIDATIONISSUE> ValidateGraphStructure(UEdGraph* Graph, const FVALIDATIONCONFIG& Config);

    /** Validate all connections in a graph */
    TArray<FVALIDATIONISSUE> ValidateGraphConnections(UEdGraph* Graph, const FVALIDATIONCONFIG& Config);

    /** Validate logic flow in a graph */
    TArray<FVALIDATIONISSUE> ValidateLogicFlow(UEdGraph* Graph, const FVALIDATIONCONFIG& Config);

    /** Validate performance aspects */
    TArray<FVALIDATIONISSUE> ValidatePerformance(UEdGraph* Graph, const FVALIDATIONCONFIG& Config);

    /** Validate UE5 compliance */
    TArray<FVALIDATIONISSUE> ValidateCompliance(UEdGraph* Graph, const FVALIDATIONCONFIG& Config);

    // Rule Management

    /** Add a custom validation rule */
    void AddValidationRule(const FVALIDATIONRULE& Rule);

    /** Remove a validation rule */
    void RemoveValidationRule(const FString& RuleId);

    /** Enable or disable a validation rule */
    void SetRuleEnabled(const FString& RuleId, bool bEnabled);

    /** Get all validation rules */
    TArray<FVALIDATIONRULE> GetValidationRules() const;

    /** Get validation rules by category */
    TArray<FVALIDATIONRULE> GetValidationRulesByCategory(EValidationCategory Category) const;

    // Auto-Fix Operations

    /** Attempt to auto-fix validation issues */
    int32 AutoFixIssues(UEdGraph* Graph, const TArray<FVALIDATIONISSUE>& Issues);

    /** Check if an issue can be auto-fixed */
    bool CanAutoFixIssue(const FVALIDATIONISSUE& Issue);

    /** Auto-fix a specific issue */
    bool AutoFixIssue(UEdGraph* Graph, const FVALIDATIONISSUE& Issue);

    // Analysis and Reporting

    /** Generate a detailed validation report */
    FString GenerateValidationReport(const FVALIDATIONRESULT& Result);

    /** Calculate quality metrics for a graph */
    void CalculateQualityMetrics(UEdGraph* Graph, FVALIDATIONRESULT& Result);

    /** Get validation statistics */
    TMap<FString, FString> GetValidationStatistics();

    // Utility Functions

    /** Set verbose logging */
    void SetVerboseLogging(bool bEnabled);

    /** Set statistics tracking */
    void SetTrackStatistics(bool bEnabled);

    /** Reset statistics */
    void ResetStatistics();

private:
    // Internal state
    bool bIsInitialized;
    bool bVerboseLogging;
    bool bTrackStatistics;

    // Dependencies
    TSharedPtr<FPinCompatibilityChecker> CompatibilityChecker;
    TSharedPtr<FCONNECTIONMANAGER> ConnectionManager;

    // Validation rules
    TMap<FString, FVALIDATIONRULE> ValidationRules;
    TSet<FString> DisabledRules;

    // Statistics
    int32 TotalValidations;
    int32 TotalIssuesFound;
    int32 TotalAutoFixes;
    float LastValidationTimeMs;
    TMap<FString, int32> ValidationStatistics;

    // Internal validation methods
    TArray<FVALIDATIONISSUE> ValidateNodeInternal(UEdGraphNode* Node, const FVALIDATIONCONFIG& Config);
    TArray<FVALIDATIONISSUE> ValidateConnectionInternal(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, const FVALIDATIONCONFIG& Config);

    // Specific validation checks
    TArray<FVALIDATIONISSUE> CheckNodeConfiguration(UEdGraphNode* Node);
    TArray<FVALIDATIONISSUE> CheckPinConnections(UEdGraphNode* Node);
    TArray<FVALIDATIONISSUE> CheckExecutionFlow(UEdGraph* Graph);
    TArray<FVALIDATIONISSUE> CheckCircularDependencies(UEdGraph* Graph);
    TArray<FVALIDATIONISSUE> CheckUnconnectedPins(UEdGraph* Graph);
    TArray<FVALIDATIONISSUE> CheckNodeNaming(UEdGraph* Graph);
    TArray<FVALIDATIONISSUE> CheckPerformanceIssues(UEdGraph* Graph);
    TArray<FVALIDATIONISSUE> CheckCompilationRequirements(UEdGraph* Graph);

    // Auto-fix implementations
    bool AutoFixUnconnectedPin(UEdGraph* Graph, UEdGraphPin* Pin);
    bool AutoFixInvalidConnection(UEdGraph* Graph, UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);
    bool AutoFixNodeNaming(UEdGraphNode* Node);
    bool AutoFixPerformanceIssue(UEdGraph* Graph, const FVALIDATIONISSUE& Issue);

    // Helper methods
    void InitializeDefaultRules();
    FVALIDATIONISSUE CreateValidationIssue(const FString& RuleId, EValidationSeverity Severity, const FString& Title, const FString& Description, UEdGraphNode* Node = nullptr, UEdGraphPin* Pin = nullptr);
    bool ShouldReportIssue(const FVALIDATIONISSUE& Issue, const FVALIDATIONCONFIG& Config);
    void LogValidationActivity(const FString& Message, bool bIsError = false);
    void UpdateValidationStatistics(const FString& Operation, const FString& Category = FString());
}; 