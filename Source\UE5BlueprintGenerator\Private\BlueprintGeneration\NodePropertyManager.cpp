#include "BlueprintGeneration/NodePropertyManager.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

DEFINE_LOG_CATEGORY(LogNodePropertyManager);

FNodePropertyManager::FNodePropertyManager()
    : bIsInitialized(false)
    , bVerboseLogging(false)
    , bTrackPropertyStatistics(true)
    , TotalPropertyOperations(0)
    , TotalValidationOperations(0)
{
    UE_LOG(LogNodePropertyManager, Log, TEXT("NodePropertyManager created"));
}

FNodePropertyManager::~FNodePropertyManager()
{
    Shutdown();
    UE_LOG(LogNodePropertyManager, Log, TEXT("NodePropertyManager destroyed"));
}

bool FNodePropertyManager::Initialize()
{
    if (bIsInitialized)
    {
        UE_LOG(LogNodePropertyManager, Warning, TEXT("NodePropertyManager already initialized"));
        return true;
    }

    UE_LOG(LogNodePropertyManager, Log, TEXT("Initializing NodePropertyManager..."));

    // Clear existing data
    ValidationRules.Empty();
    PropertyStatistics.Empty();

    // Set template directory
    TemplateDirectory = FPaths::ProjectPluginsDir() / TEXT("UE5BlueprintGenerator") / TEXT("Templates") / TEXT("Properties");

    // Create template directory if it doesn't exist
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*TemplateDirectory))
    {
        PlatformFile.CreateDirectoryTree(*TemplateDirectory);
    }

    // Initialize default validation rules
    InitializeDefaultValidationRules();

    bIsInitialized = true;
    
    UE_LOG(LogNodePropertyManager, Log, TEXT("NodePropertyManager initialized successfully"));
    LogPropertyActivity(TEXT("Property manager initialized"));

    return true;
}

void FNodePropertyManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    UE_LOG(LogNodePropertyManager, Log, TEXT("Shutting down NodePropertyManager..."));

    // Clear all data
    ValidationRules.Empty();
    PropertyStatistics.Empty();

    bIsInitialized = false;
    
    UE_LOG(LogNodePropertyManager, Log, TEXT("NodePropertyManager shutdown complete"));
}

TArray<FNODEPROPERTYINFO> FNodePropertyManager::GetNodeProperties(UClass* NodeClass)
{
    TArray<FNODEPROPERTYINFO> Properties;
    
    if (!bIsInitialized || !NodeClass)
    {
        UE_LOG(LogNodePropertyManager, Error, TEXT("Cannot get node properties - manager not initialized or invalid class"));
        return Properties;
    }

    // Iterate through all properties of the class
    for (FProperty* Property = NodeClass->PropertyLink; Property; Property = Property->PropertyLinkNext)
    {
        // Skip properties that shouldn't be exposed
        if (Property->HasAnyPropertyFlags(CPF_DisableEditOnInstance | CPF_DisableEditOnTemplate))
        {
            continue;
        }

        FNODEPROPERTYINFO PropertyInfo = AnalyzeProperty(Property);
        Properties.Add(PropertyInfo);
    }

    LogPropertyActivity(FString::Printf(TEXT("Retrieved %d properties for class %s"), 
        Properties.Num(), NodeClass ? *NodeClass->GetName() : TEXT("Unknown")));
    UpdatePropertyStatistics(TEXT("GetProperties"));

    return Properties;
}

TArray<FNODEPROPERTYINFO> FNodePropertyManager::GetNodeInstanceProperties(UK2Node* Node)
{
    TArray<FNODEPROPERTYINFO> Properties;
    
    if (!bIsInitialized || !Node)
    {
        UE_LOG(LogNodePropertyManager, Error, TEXT("Cannot get node instance properties - manager not initialized or invalid node"));
        return Properties;
    }

    UClass* NodeClass = Node->GetClass();
    
    // Iterate through all properties of the node's class
    for (FProperty* Property = NodeClass->PropertyLink; Property; Property = Property->PropertyLinkNext)
    {
        // Skip properties that shouldn't be exposed
        if (Property->HasAnyPropertyFlags(CPF_DisableEditOnInstance | CPF_DisableEditOnTemplate))
        {
            continue;
        }

        FNODEPROPERTYINFO PropertyInfo = AnalyzeProperty(Property, Node);
        Properties.Add(PropertyInfo);
    }

    LogPropertyActivity(FString::Printf(TEXT("Retrieved %d instance properties for node %s"), 
        Properties.Num(), Node ? *Node->GetName() : TEXT("Unknown")));
    UpdatePropertyStatistics(TEXT("GetInstanceProperties"));

    return Properties;
}

FNODEPROPERTYINFO FNodePropertyManager::GetNodeProperty(UK2Node* Node, const FString& PropertyName)
{
    FNODEPROPERTYINFO PropertyInfo;
    
    if (!bIsInitialized || !Node || PropertyName.IsEmpty())
    {
        UE_LOG(LogNodePropertyManager, Error, TEXT("Cannot get node property - invalid parameters"));
        return PropertyInfo;
    }

    UClass* NodeClass = Node->GetClass();
    FProperty* Property = NodeClass->FindPropertyByName(*PropertyName);
    
    if (Property)
    {
        PropertyInfo = AnalyzeProperty(Property, Node);
        LogPropertyActivity(FString::Printf(TEXT("Retrieved property %s for node %s"), 
            *PropertyName, *Node->GetName()));
    }
    else
    {
        UE_LOG(LogNodePropertyManager, Warning, TEXT("Property '%s' not found on node '%s'"), 
            *PropertyName, *Node->GetName());
    }

    UpdatePropertyStatistics(TEXT("GetProperty"), PropertyName);
    return PropertyInfo;
}

bool FNodePropertyManager::HasNodeProperty(UK2Node* Node, const FString& PropertyName)
{
    if (!bIsInitialized || !Node || PropertyName.IsEmpty())
    {
        return false;
    }

    UClass* NodeClass = Node->GetClass();
    FProperty* Property = NodeClass->FindPropertyByName(*PropertyName);
    
    return Property != nullptr;
}

FPROPERTYCONFIGURATIONRESULT FNodePropertyManager::ConfigureNodeProperties(const FPROPERTYCONFIGUATIONREQUEST& ConfigRequest)
{
    FPROPERTYCONFIGURATIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || !ConfigRequest.TargetNode)
    {
        Result.ErrorMessages.Add(TEXT("Invalid configuration request - manager not initialized or invalid node"));
        Result.ConfigurationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    UK2Node* Node = ConfigRequest.TargetNode;
    int32 SuccessCount = 0;
    int32 FailureCount = 0;

    // Validate before applying if requested
    if (ConfigRequest.bValidateBeforeApply)
    {
        FPROPERTYVALIDATIONRESULT ValidationResult = ValidateNodeProperties(Node);
        if (!ValidationResult.bIsValid)
        {
            Result.ErrorMessages.Add(TEXT("Pre-configuration validation failed"));
            Result.ErrorMessages.Append(ValidationResult.ErrorMessages);
            Result.ConfigurationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
            return Result;
        }
    }

    // Apply each property configuration
    for (const auto& PropertyPair : ConfigRequest.PropertyValues)
    {
        const FString& PropertyName = PropertyPair.Key;
        const FString& PropertyValue = PropertyPair.Value;

        bool bSuccess = SetNodeProperty(Node, PropertyName, PropertyValue, ConfigRequest.bValidateBeforeApply);
        
        if (bSuccess)
        {
            Result.ConfiguredProperties.Add(PropertyName);
            SuccessCount++;
        }
        else
        {
            Result.FailedProperties.Add(PropertyName);
            Result.ErrorMessages.Add(FString::Printf(TEXT("Failed to set property '%s' to value '%s'"), 
                *PropertyName, *PropertyValue));
            FailureCount++;
        }
    }

    // Apply defaults if requested
    if (ConfigRequest.bApplyDefaults)
    {
        int32 DefaultsApplied = ApplyDefaultValues(Node);
        if (DefaultsApplied > 0)
        {
            Result.WarningMessages.Add(FString::Printf(TEXT("Applied default values to %d properties"), DefaultsApplied));
        }
    }

    // Reconstruct node if requested and any properties were changed
    if (ConfigRequest.bReconstructAfterApply && SuccessCount > 0)
    {
        Node->ReconstructNode();
        Result.WarningMessages.Add(TEXT("Node reconstructed after property changes"));
    }

    Result.bSuccess = (FailureCount == 0);
    Result.ConfigurationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;

    LogPropertyActivity(FString::Printf(TEXT("Configured %d properties (%d success, %d failed) in %.2fms"), 
        ConfigRequest.PropertyValues.Num(), SuccessCount, FailureCount, Result.ConfigurationTimeMs));
    UpdatePropertyStatistics(TEXT("ConfigureProperties"));

    return Result;
}

bool FNodePropertyManager::SetNodeProperty(UK2Node* Node, const FString& PropertyName, const FString& PropertyValue, bool bValidate)
{
    if (!bIsInitialized || !Node || PropertyName.IsEmpty())
    {
        UE_LOG(LogNodePropertyManager, Error, TEXT("Cannot set node property - invalid parameters"));
        return false;
    }

    UClass* NodeClass = Node->GetClass();
    FProperty* Property = NodeClass->FindPropertyByName(*PropertyName);
    
    if (!Property)
    {
        UE_LOG(LogNodePropertyManager, Error, TEXT("Property '%s' not found on node '%s'"), 
            *PropertyName, *Node->GetName());
        return false;
    }

    // Validate property value if requested
    if (bValidate)
    {
        FNODEPROPERTYINFO PropertyInfo = AnalyzeProperty(Property, Node);
        FPROPERTYVALIDATIONRESULT ValidationResult = ValidatePropertyValue(PropertyInfo, PropertyValue);
        
        if (!ValidationResult.bIsValid)
        {
            UE_LOG(LogNodePropertyManager, Error, TEXT("Property validation failed for '%s': %s"), 
                *PropertyName, ValidationResult.ErrorMessages.Num() > 0 ? *ValidationResult.ErrorMessages[0] : TEXT("Unknown error"));
            return false;
        }
    }

    // Set property based on type
    bool bSuccess = false;
    
    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
    {
        bSuccess = SetBooleanProperty(BoolProp, Node, PropertyValue);
    }
    else if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
    {
        bSuccess = SetIntegerProperty(IntProp, Node, PropertyValue);
    }
    else if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
    {
        bSuccess = SetFloatProperty(FloatProp, Node, PropertyValue);
    }
    else if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
    {
        bSuccess = SetStringProperty(StrProp, Node, PropertyValue);
    }
    else if (FObjectProperty* ObjProp = CastField<FObjectProperty>(Property))
    {
        bSuccess = SetObjectProperty(ObjProp, Node, PropertyValue);
    }
    else
    {
        UE_LOG(LogNodePropertyManager, Warning, TEXT("Unsupported property type for '%s'"), *PropertyName);
        bSuccess = false;
    }

    if (bSuccess)
    {
        LogPropertyActivity(FString::Printf(TEXT("Set property '%s' to '%s' on node '%s'"), 
            *PropertyName, *PropertyValue, *Node->GetName()));
    }

    UpdatePropertyStatistics(TEXT("SetProperty"), PropertyName);
    return bSuccess;
}

FString FNodePropertyManager::GetNodePropertyValue(UK2Node* Node, const FString& PropertyName)
{
    if (!bIsInitialized || !Node || PropertyName.IsEmpty())
    {
        return FString();
    }

    UClass* NodeClass = Node->GetClass();
    FProperty* Property = NodeClass->FindPropertyByName(*PropertyName);
    
    if (!Property)
    {
        return FString();
    }

    return GetPropertyCurrentValue(Property, Node);
}

bool FNodePropertyManager::ResetNodeProperty(UK2Node* Node, const FString& PropertyName)
{
    if (!bIsInitialized || !Node || PropertyName.IsEmpty())
    {
        return false;
    }

    UClass* NodeClass = Node->GetClass();
    FProperty* Property = NodeClass->FindPropertyByName(*PropertyName);
    
    if (!Property)
    {
        return false;
    }

    FString DefaultValue = GetPropertyDefaultValue(Property, Node);
    return SetNodeProperty(Node, PropertyName, DefaultValue, false);
}

int32 FNodePropertyManager::ApplyDefaultValues(UK2Node* Node)
{
    if (!bIsInitialized || !Node)
    {
        return 0;
    }

    int32 DefaultsApplied = 0;
    UClass* NodeClass = Node->GetClass();
    
    for (FProperty* Property = NodeClass->PropertyLink; Property; Property = Property->PropertyLinkNext)
    {
        if (Property->HasAnyPropertyFlags(CPF_DisableEditOnInstance | CPF_DisableEditOnTemplate))
        {
            continue;
        }

        FString DefaultValue = GetPropertyDefaultValue(Property, Node);
        if (!DefaultValue.IsEmpty())
        {
            FString PropertyName = Property->GetName();
            if (SetNodeProperty(Node, PropertyName, DefaultValue, false))
            {
                DefaultsApplied++;
            }
        }
    }

    LogPropertyActivity(FString::Printf(TEXT("Applied default values to %d properties on node '%s'"), 
        DefaultsApplied, *Node->GetName()));
    UpdatePropertyStatistics(TEXT("ApplyDefaults"));

    return DefaultsApplied;
}

FPROPERTYVALIDATIONRESULT FNodePropertyManager::ValidatePropertyValue(const FNODEPROPERTYINFO& PropertyInfo, const FString& Value)
{
    FPROPERTYVALIDATIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    // Basic validation based on data type
    switch (PropertyInfo.DataType)
    {
        case EPropertyDataType::Boolean:
            Result = ValidateBooleanValue(Value, PropertyInfo);
            break;
        case EPropertyDataType::Integer:
        case EPropertyDataType::Float:
            Result = ValidateNumericValue(Value, PropertyInfo);
            break;
        case EPropertyDataType::String:
        case EPropertyDataType::Name:
        case EPropertyDataType::Text:
            Result = ValidateStringValue(Value, PropertyInfo);
            break;
        default:
            Result.bIsValid = true; // Default to valid for unsupported types
            break;
    }

    // Apply custom validation rules if specified
    if (PropertyInfo.ValidationRules.Num() > 0)
    {
        FPROPERTYVALIDATIONRESULT CustomResult = ValidateWithCustomRules(Value, PropertyInfo.ValidationRules);
        if (!CustomResult.bIsValid)
        {
            Result.bIsValid = false;
            Result.ErrorMessages.Append(CustomResult.ErrorMessages);
        }
    }

    Result.ValidationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    TotalValidationOperations++;

    return Result;
}

FPROPERTYVALIDATIONRESULT FNodePropertyManager::ValidateNodeProperties(UK2Node* Node)
{
    FPROPERTYVALIDATIONRESULT Result;
    
    if (!bIsInitialized || !Node)
    {
        Result.bIsValid = false;
        Result.ErrorMessages.Add(TEXT("Invalid node for validation"));
        return Result;
    }

    TArray<FNODEPROPERTYINFO> Properties = GetNodeInstanceProperties(Node);
    
    for (const FNODEPROPERTYINFO& PropertyInfo : Properties)
    {
        FPROPERTYVALIDATIONRESULT PropertyResult = ValidatePropertyValue(PropertyInfo, PropertyInfo.CurrentValue);
        
        if (!PropertyResult.bIsValid)
        {
            Result.bIsValid = false;
            for (const FString& Error : PropertyResult.ErrorMessages)
            {
                Result.ErrorMessages.Add(FString::Printf(TEXT("Property '%s': %s"), 
                    *PropertyInfo.PropertyName, *Error));
            }
        }
    }

    return Result;
}

// Helper method implementations

FNODEPROPERTYINFO FNodePropertyManager::AnalyzeProperty(FProperty* Property, UK2Node* Node)
{
    FNODEPROPERTYINFO PropertyInfo;
    
    if (!Property)
    {
        return PropertyInfo;
    }

    PropertyInfo.PropertyName = Property->GetName();
    PropertyInfo.DisplayName = Property->GetDisplayNameText().ToString();
    PropertyInfo.Description = Property->GetToolTipText().ToString();
    PropertyInfo.DataType = GetPropertyDataType(Property);
    PropertyInfo.bIsRequired = Property->HasAnyPropertyFlags(CPF_RequiredParm);
    PropertyInfo.bIsReadOnly = Property->HasAnyPropertyFlags(CPF_BlueprintReadOnly);
    PropertyInfo.bIsAdvanced = Property->HasAnyPropertyFlags(CPF_AdvancedDisplay);

    if (Node)
    {
        PropertyInfo.CurrentValue = GetPropertyCurrentValue(Property, Node);
    }
    
    PropertyInfo.DefaultValue = GetPropertyDefaultValue(Property, Node);

    return PropertyInfo;
}

EPropertyDataType FNodePropertyManager::GetPropertyDataType(FProperty* Property)
{
    if (!Property)
    {
        return EPropertyDataType::Unknown;
    }

    if (CastField<FBoolProperty>(Property))
    {
        return EPropertyDataType::Boolean;
    }
    else if (CastField<FIntProperty>(Property))
    {
        return EPropertyDataType::Integer;
    }
    else if (CastField<FFloatProperty>(Property))
    {
        return EPropertyDataType::Float;
    }
    else if (CastField<FStrProperty>(Property))
    {
        return EPropertyDataType::String;
    }
    else if (CastField<FNameProperty>(Property))
    {
        return EPropertyDataType::Name;
    }
    else if (CastField<FTextProperty>(Property))
    {
        return EPropertyDataType::Text;
    }
    else if (CastField<FObjectProperty>(Property))
    {
        return EPropertyDataType::Object;
    }
    else if (CastField<FArrayProperty>(Property))
    {
        return EPropertyDataType::Array;
    }

    return EPropertyDataType::Unknown;
}

FString FNodePropertyManager::GetPropertyDefaultValue(FProperty* Property, UK2Node* Node)
{
    // This is a simplified implementation
    // In a full implementation, you would extract the actual default value from the property
    return FString();
}

FString FNodePropertyManager::GetPropertyCurrentValue(FProperty* Property, UK2Node* Node)
{
    if (!Property || !Node)
    {
        return FString();
    }

    // Get the property value from the node instance
    const void* PropertyValue = Property->ContainerPtrToValuePtr<void>(Node);
    return ConvertPropertyTypeToString(PropertyValue, GetPropertyDataType(Property));
}

bool FNodePropertyManager::SetBooleanProperty(FBoolProperty* Property, UK2Node* Node, const FString& Value)
{
    if (!Property || !Node)
    {
        return false;
    }

    bool BoolValue = Value.ToBool();
    Property->SetPropertyValue_InContainer(Node, BoolValue);
    return true;
}

bool FNodePropertyManager::SetIntegerProperty(FIntProperty* Property, UK2Node* Node, const FString& Value)
{
    if (!Property || !Node)
    {
        return false;
    }

    int32 IntValue = FCString::Atoi(*Value);
    Property->SetPropertyValue_InContainer(Node, IntValue);
    return true;
}

bool FNodePropertyManager::SetFloatProperty(FFloatProperty* Property, UK2Node* Node, const FString& Value)
{
    if (!Property || !Node)
    {
        return false;
    }

    float FloatValue = FCString::Atof(*Value);
    Property->SetPropertyValue_InContainer(Node, FloatValue);
    return true;
}

bool FNodePropertyManager::SetStringProperty(FStrProperty* Property, UK2Node* Node, const FString& Value)
{
    if (!Property || !Node)
    {
        return false;
    }

    Property->SetPropertyValue_InContainer(Node, Value);
    return true;
}

bool FNodePropertyManager::SetObjectProperty(FObjectProperty* Property, UK2Node* Node, const FString& Value)
{
    if (!Property || !Node)
    {
        return false;
    }

    // This is a simplified implementation
    // In a full implementation, you would resolve the object reference from the string
    return false;
}

FString FNodePropertyManager::ConvertPropertyTypeToString(const void* PropertyValue, EPropertyDataType DataType)
{
    if (!PropertyValue)
    {
        return FString();
    }

    switch (DataType)
    {
        case EPropertyDataType::Boolean:
            return *(static_cast<const bool*>(PropertyValue)) ? TEXT("true") : TEXT("false");
        case EPropertyDataType::Integer:
            return FString::FromInt(*(static_cast<const int32*>(PropertyValue)));
        case EPropertyDataType::Float:
            return FString::SanitizeFloat(*(static_cast<const float*>(PropertyValue)));
        case EPropertyDataType::String:
            return *(static_cast<const FString*>(PropertyValue));
        default:
            return FString();
    }
}

FPROPERTYVALIDATIONRESULT FNodePropertyManager::ValidateBooleanValue(const FString& Value, const FNODEPROPERTYINFO& PropertyInfo)
{
    FPROPERTYVALIDATIONRESULT Result;
    
    if (Value.Equals(TEXT("true"), ESearchCase::IgnoreCase) || 
        Value.Equals(TEXT("false"), ESearchCase::IgnoreCase) ||
        Value.Equals(TEXT("1")) || Value.Equals(TEXT("0")))
    {
        Result.bIsValid = true;
    }
    else
    {
        Result.bIsValid = false;
        Result.ErrorMessages.Add(FString::Printf(TEXT("Invalid boolean value '%s'. Expected 'true', 'false', '1', or '0'"), *Value));
    }

    return Result;
}

FPROPERTYVALIDATIONRESULT FNodePropertyManager::ValidateNumericValue(const FString& Value, const FNODEPROPERTYINFO& PropertyInfo)
{
    FPROPERTYVALIDATIONRESULT Result;
    
    if (PropertyInfo.DataType == EPropertyDataType::Integer)
    {
        if (Value.IsNumeric())
        {
            int32 IntValue = FCString::Atoi(*Value);
            
            // Check min/max if specified
            if (!PropertyInfo.MinValue.IsEmpty())
            {
                int32 MinValue = FCString::Atoi(*PropertyInfo.MinValue);
                if (IntValue < MinValue)
                {
                    Result.bIsValid = false;
                    Result.ErrorMessages.Add(FString::Printf(TEXT("Value %d is below minimum %d"), IntValue, MinValue));
                }
            }
            
            if (!PropertyInfo.MaxValue.IsEmpty())
            {
                int32 MaxValue = FCString::Atoi(*PropertyInfo.MaxValue);
                if (IntValue > MaxValue)
                {
                    Result.bIsValid = false;
                    Result.ErrorMessages.Add(FString::Printf(TEXT("Value %d is above maximum %d"), IntValue, MaxValue));
                }
            }
            
            if (Result.ErrorMessages.Num() == 0)
            {
                Result.bIsValid = true;
            }
        }
        else
        {
            Result.bIsValid = false;
            Result.ErrorMessages.Add(FString::Printf(TEXT("Invalid integer value '%s'"), *Value));
        }
    }
    else if (PropertyInfo.DataType == EPropertyDataType::Float)
    {
        if (Value.IsNumeric())
        {
            float FloatValue = FCString::Atof(*Value);
            
            // Check min/max if specified
            if (!PropertyInfo.MinValue.IsEmpty())
            {
                float MinValue = FCString::Atof(*PropertyInfo.MinValue);
                if (FloatValue < MinValue)
                {
                    Result.bIsValid = false;
                    Result.ErrorMessages.Add(FString::Printf(TEXT("Value %.2f is below minimum %.2f"), FloatValue, MinValue));
                }
            }
            
            if (!PropertyInfo.MaxValue.IsEmpty())
            {
                float MaxValue = FCString::Atof(*PropertyInfo.MaxValue);
                if (FloatValue > MaxValue)
                {
                    Result.bIsValid = false;
                    Result.ErrorMessages.Add(FString::Printf(TEXT("Value %.2f is above maximum %.2f"), FloatValue, MaxValue));
                }
            }
            
            if (Result.ErrorMessages.Num() == 0)
            {
                Result.bIsValid = true;
            }
        }
        else
        {
            Result.bIsValid = false;
            Result.ErrorMessages.Add(FString::Printf(TEXT("Invalid float value '%s'"), *Value));
        }
    }

    return Result;
}

FPROPERTYVALIDATIONRESULT FNodePropertyManager::ValidateStringValue(const FString& Value, const FNODEPROPERTYINFO& PropertyInfo)
{
    FPROPERTYVALIDATIONRESULT Result;
    Result.bIsValid = true;

    // Check allowed values if specified
    if (PropertyInfo.AllowedValues.Num() > 0)
    {
        if (!PropertyInfo.AllowedValues.Contains(Value))
        {
            Result.bIsValid = false;
            Result.ErrorMessages.Add(FString::Printf(TEXT("Value '%s' is not in allowed values list"), *Value));
        }
    }

    return Result;
}

FPROPERTYVALIDATIONRESULT FNodePropertyManager::ValidateWithCustomRules(const FString& Value, const TArray<FString>& Rules)
{
    FPROPERTYVALIDATIONRESULT Result;
    Result.bIsValid = true;

    // This is a simplified implementation
    // In a full implementation, you would parse and apply custom validation rules
    
    return Result;
}

void FNodePropertyManager::InitializeDefaultValidationRules()
{
    // Initialize default validation rules for different data types
    // This is a simplified implementation
}

void FNodePropertyManager::LogPropertyActivity(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogNodePropertyManager, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogNodePropertyManager, Log, TEXT("%s"), *Message);
    }
}

void FNodePropertyManager::UpdatePropertyStatistics(const FString& Operation, const FString& PropertyName)
{
    if (!bTrackPropertyStatistics)
    {
        return;
    }

    FString StatKey = FString::Printf(TEXT("%s_Count"), *Operation);
    if (PropertyStatistics.Contains(StatKey))
    {
        PropertyStatistics[StatKey]++;
    }
    else
    {
        PropertyStatistics.Add(StatKey, 1);
    }

    TotalPropertyOperations++;
}

TMap<FString, FString> FNodePropertyManager::GetPropertyManagerStatistics()
{
    TMap<FString, FString> Stats;
    
    Stats.Add(TEXT("TotalOperations"), FString::FromInt(TotalPropertyOperations));
    Stats.Add(TEXT("TotalValidations"), FString::FromInt(TotalValidationOperations));
    Stats.Add(TEXT("IsInitialized"), bIsInitialized ? TEXT("true") : TEXT("false"));
    
    for (const auto& StatPair : PropertyStatistics)
    {
        Stats.Add(StatPair.Key, FString::FromInt(StatPair.Value));
    }
    
    return Stats;
} 