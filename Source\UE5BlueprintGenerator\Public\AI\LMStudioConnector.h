#pragma once

#include "CoreMinimal.h"
#include "AIModelConnector.h"
#include "Http.h"
#include "Dom/JsonObject.h"

/**
 * LMStudio-specific AI model connector implementation
 * Handles communication with LMStudio API endpoints (OpenAI-compatible)
 */
class UE5BLUEPRINTGENERATOR_API FLMStudioConnector : public IAIModelConnector
{
public:
	FLMStudioConnector();
	virtual ~FLMStudioConnector() override;

	// IAIModelConnector interface
	virtual bool SendRequest(const FAIModelRequest& Request, TFunction<void(const FAIModelResponse&)> OnResponse) override;
	virtual bool TestConnection(const FAIModelSettings& Settings, TFunction<void(bool, const FString&)> OnResponse) override;
	virtual bool GetAvailableModels(const FAIModelSettings& Settings, TFunction<void(const TArray<FString>&)> OnResponse) override;
	virtual void CancelPendingRequests() override;
	virtual EAIModelProvider GetProviderType() const override { return EAIModelProvider::LMStudio; }

private:
	// HTTP request handling
	TArray<TSharedPtr<IHttpRequest, ESPMode::ThreadSafe>> PendingRequests;
	
	// Request creation methods
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateChatCompletionRequest(const FAIModelRequest& Request);
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateTestRequest(const FAIModelSettings& Settings);
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> CreateModelsRequest(const FAIModelSettings& Settings);
	
	// Response handling methods
	void HandleChatCompletionResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const FAIModelResponse&)> OnResponse);
	void HandleTestResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(bool, const FString&)> OnResponse);
	void HandleModelsResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const TArray<FString>&)> OnResponse);
	
	// JSON processing methods
	TSharedPtr<FJsonObject> CreateChatCompletionRequestJson(const FAIModelRequest& Request);
	FAIModelResponse ParseChatCompletionResponse(const FString& ResponseContent);
	TArray<FString> ParseModelsResponse(const FString& ResponseContent);
	
	// Utility methods
	FString GetChatCompletionEndpoint(const FAIModelSettings& Settings);
	FString GetModelsEndpoint(const FAIModelSettings& Settings);
	void RemoveCompletedRequest(FHttpRequestPtr Request);
	
	// Error handling
	FAIModelResponse CreateErrorResponse(const FString& ErrorMessage);
	FString GetHttpErrorMessage(int32 ResponseCode);
}; 