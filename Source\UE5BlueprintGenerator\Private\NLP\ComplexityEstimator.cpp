#include "NLP/ComplexityEstimator.h"
#include "UE5BlueprintGeneratorModule.h"

DEFINE_LOG_CATEGORY_EXTERN(LogComplexityEstimator, Log, All);
DEFINE_LOG_CATEGORY(LogComplexityEstimator);

FComplexityEstimator::FComplexityEstimator()
	: TrivialThreshold(1.0f)
	, SimpleT<PERSON>eshold(2.5f)
	, <PERSON>rate<PERSON><PERSON>eshold(4.5f)
	, ComplexThreshold(6.5f)
	, VeryComplexThreshold(8.5f)
{
	InitializeComplexityWeights();
	InitializePerformanceWeights();
	InitializeComplexityThresholds();
	InitializePerformanceThresholds();
	
	UE_LOG(LogComplexityEstimator, Log, TEXT("Complexity estimator initialized with %d element weights, %d performance weights"), 
		ElementComplexityWeights.Num(), PerformanceWeights.Num());
}

FComplexityEstimator::~FComplexityEstimator()
{
	UE_LOG(LogComplexityEstimator, Log, TEXT("Complexity estimator destroyed"));
}

FComplexityAnalysisResult FComplexityEstimator::AnalyzeComplexity(const FExtractedBlueprintStructure& Structure,
	const FIntentAnalysisResult& IntentResult,
	const EBlueprintPatternType PatternType)
{
	FComplexityAnalysisResult Result;
	
	UE_LOG(LogComplexityEstimator, VeryVerbose, TEXT("Analyzing complexity for structure with %d variables, %d functions, %d events, %d components"), 
		Structure.Variables.Num(), Structure.Functions.Num(), Structure.Events.Num(), Structure.Components.Num());
	
	// Calculate overall complexity score
	Result.ComplexityScore = CalculateComplexityScore(Structure);
	Result.ComplexityLevel = DetermineComplexityLevel(Result.ComplexityScore);
	
	// Calculate complexity factors
	Result.ComplexityFactors = CalculateComplexityFactors(Structure);
	
	// Estimate resource requirements
	Result.ResourceRequirements = EstimateResourceRequirements(Structure, PatternType);
	
	// Analyze performance impact
	Result.PerformanceAnalysis = AnalyzePerformanceImpact(Structure, Result.ResourceRequirements);
	
	// Assess user capability requirements
	Result.UserCapabilityAssessment = AssessUserCapability(Result.ComplexityLevel, Structure);
	
	// Generate analysis notes and recommendations
	Result.AnalysisNotes = GenerateAnalysisNotes(Result);
	Result.ComplexityRecommendations = GenerateComplexityRecommendations(Result);
	
	// Assess feasibility
	Result.bIsFeasible = AssessFeasibility(Result);
	if (!Result.bIsFeasible)
	{
		Result.FeasibilityIssues = IdentifyFeasibilityIssues(Result);
	}
	
	// Calculate assessment confidence
	Result.AssessmentConfidence = CalculateAssessmentConfidence(Structure, Result);
	
	// Update statistics
	UpdateAnalysisStatistics(Result);
	
	UE_LOG(LogComplexityEstimator, Log, TEXT("Complexity analysis complete: Level=%d, Score=%.2f, Confidence=%.2f"), 
		(int32)Result.ComplexityLevel, Result.ComplexityScore, Result.AssessmentConfidence);
	
	return Result;
}

float FComplexityEstimator::CalculateComplexityScore(const FExtractedBlueprintStructure& Structure)
{
	float TotalScore = 0.0f;
	
	// Calculate individual complexity components
	float VariableComplexity = CalculateVariableComplexity(Structure.Variables);
	float FunctionComplexity = CalculateFunctionComplexity(Structure.Functions);
	float EventComplexity = CalculateEventComplexity(Structure.Events);
	float ComponentComplexity = CalculateComponentComplexity(Structure.Components);
	float RelationshipComplexity = CalculateRelationshipComplexity(Structure.Relationships);
	
	// Apply weights and sum
	TotalScore += VariableComplexity * ElementComplexityWeights.FindRef(TEXT("Variable"));
	TotalScore += FunctionComplexity * ElementComplexityWeights.FindRef(TEXT("Function"));
	TotalScore += EventComplexity * ElementComplexityWeights.FindRef(TEXT("Event"));
	TotalScore += ComponentComplexity * ElementComplexityWeights.FindRef(TEXT("Component"));
	TotalScore += RelationshipComplexity * ElementComplexityWeights.FindRef(TEXT("Relationship"));
	
	// Apply custom complexity factors
	for (const auto& FactorPair : CustomComplexityFactors)
	{
		float FactorValue = FactorPair.Value(Structure);
		TotalScore += FactorValue;
	}
	
	// Normalize the score
	return NormalizeComplexityScore(TotalScore);
}

EComplexityLevel FComplexityEstimator::DetermineComplexityLevel(float ComplexityScore)
{
	if (ComplexityScore <= TrivialThreshold)
	{
		return EComplexityLevel::Trivial;
	}
	else if (ComplexityScore <= SimpleThreshold)
	{
		return EComplexityLevel::Simple;
	}
	else if (ComplexityScore <= ModerateThreshold)
	{
		return EComplexityLevel::Moderate;
	}
	else if (ComplexityScore <= ComplexThreshold)
	{
		return EComplexityLevel::Complex;
	}
	else if (ComplexityScore <= VeryComplexThreshold)
	{
		return EComplexityLevel::VeryComplex;
	}
	else
	{
		return EComplexityLevel::ExtremelyComplex;
	}
}

FResourceRequirements FComplexityEstimator::EstimateResourceRequirements(const FExtractedBlueprintStructure& Structure,
	const EBlueprintPatternType PatternType)
{
	FResourceRequirements Requirements;
	
	// Estimate memory usage
	Requirements.EstimatedMemoryMB = EstimateMemoryUsage(Structure);
	
	// Estimate CPU usage
	Requirements.EstimatedCPUUsage = EstimateCPUUsage(Structure, PatternType);
	
	// Estimate GPU usage
	Requirements.EstimatedGPUUsage = EstimateGPUUsage(Structure);
	
	// Estimate network usage
	Requirements.EstimatedNetworkKBps = EstimateNetworkUsage(Structure);
	
	// Estimate node and connection counts
	Requirements.EstimatedNodeCount = EstimateNodeCount(Structure);
	Requirements.EstimatedConnectionCount = EstimateConnectionCount(Structure);
	
	// Estimate compilation time
	Requirements.EstimatedCompileTime = EstimateCompileTime(Structure);
	
	return Requirements;
}

FPerformanceAnalysis FComplexityEstimator::AnalyzePerformanceImpact(const FExtractedBlueprintStructure& Structure,
	const FResourceRequirements& ResourceReqs)
{
	FPerformanceAnalysis Analysis;
	
	// Calculate individual performance impacts
	Analysis.RuntimeImpact = CalculateRuntimeImpact(Structure);
	Analysis.MemoryImpact = CalculateMemoryImpact(ResourceReqs);
	Analysis.NetworkImpact = CalculateNetworkImpact(Structure);
	Analysis.RenderingImpact = CalculateRenderingImpact(Structure);
	Analysis.CompilationImpact = CalculateCompilationImpact(ResourceReqs);
	
	// Calculate overall impact (weighted average)
	float OverallScore = 0.0f;
	OverallScore += (float)Analysis.RuntimeImpact * PerformanceWeights.FindRef(TEXT("Runtime"));
	OverallScore += (float)Analysis.MemoryImpact * PerformanceWeights.FindRef(TEXT("Memory"));
	OverallScore += (float)Analysis.NetworkImpact * PerformanceWeights.FindRef(TEXT("Network"));
	OverallScore += (float)Analysis.RenderingImpact * PerformanceWeights.FindRef(TEXT("Rendering"));
	OverallScore += (float)Analysis.CompilationImpact * PerformanceWeights.FindRef(TEXT("Compilation"));
	
	Analysis.OverallImpact = ScoreToPerformanceImpact(OverallScore, PerformanceThresholds.FindRef(TEXT("Overall")));
	
	// Generate performance notes and optimization suggestions
	Analysis.PerformanceNotes = GeneratePerformanceNotes(Structure);
	Analysis.OptimizationSuggestions = GenerateOptimizationSuggestions(Analysis);
	
	return Analysis;
}

FUserCapabilityAssessment FComplexityEstimator::AssessUserCapability(EComplexityLevel ComplexityLevel,
	const FExtractedBlueprintStructure& Structure)
{
	FUserCapabilityAssessment Assessment;
	
	// Determine required skill level
	Assessment.RequiredSkillLevel = DetermineRequiredSkillLevel(ComplexityLevel, Structure);
	
	// Estimate development time
	Assessment.EstimatedDevelopmentHours = EstimateDevelopmentTime(ComplexityLevel, Structure);
	
	// Identify required knowledge areas
	Assessment.RequiredKnowledgeAreas = IdentifyRequiredKnowledgeAreas(Structure);
	
	// Generate recommended resources
	Assessment.RecommendedResources = GenerateRecommendedResources(Assessment.RequiredKnowledgeAreas);
	
	// Identify prerequisites
	Assessment.Prerequisites = IdentifyPrerequisites(ComplexityLevel);
	
	// Generate difficulty warnings
	Assessment.DifficultyWarnings = GenerateDifficultyWarnings(Structure);
	
	// Suggest simpler alternatives
	Assessment.SimplerAlternatives = SuggestSimplerAlternatives(Structure);
	
	return Assessment;
}

void FComplexityEstimator::InitializeComplexityWeights()
{
	// Element complexity weights
	ElementComplexityWeights.Add(TEXT("Variable"), 0.15f);
	ElementComplexityWeights.Add(TEXT("Function"), 0.35f);
	ElementComplexityWeights.Add(TEXT("Event"), 0.25f);
	ElementComplexityWeights.Add(TEXT("Component"), 0.15f);
	ElementComplexityWeights.Add(TEXT("Relationship"), 0.10f);
}

void FComplexityEstimator::InitializePerformanceWeights()
{
	// Performance impact weights
	PerformanceWeights.Add(TEXT("Runtime"), 0.30f);
	PerformanceWeights.Add(TEXT("Memory"), 0.25f);
	PerformanceWeights.Add(TEXT("Network"), 0.15f);
	PerformanceWeights.Add(TEXT("Rendering"), 0.20f);
	PerformanceWeights.Add(TEXT("Compilation"), 0.10f);
}

void FComplexityEstimator::InitializeComplexityThresholds()
{
	// Complexity level thresholds (already set in constructor)
	// These can be adjusted based on empirical data
}

void FComplexityEstimator::InitializePerformanceThresholds()
{
	// Performance impact thresholds for different categories
	PerformanceThresholds.Add(TEXT("Overall"), { 0.5f, 1.0f, 2.0f, 3.5f, 5.0f });
	PerformanceThresholds.Add(TEXT("Runtime"), { 0.3f, 0.8f, 1.5f, 2.5f, 4.0f });
	PerformanceThresholds.Add(TEXT("Memory"), { 1.0f, 5.0f, 15.0f, 50.0f, 150.0f });
	PerformanceThresholds.Add(TEXT("Network"), { 0.1f, 1.0f, 5.0f, 20.0f, 100.0f });
	PerformanceThresholds.Add(TEXT("Rendering"), { 0.2f, 1.0f, 3.0f, 8.0f, 20.0f });
	PerformanceThresholds.Add(TEXT("Compilation"), { 0.5f, 2.0f, 8.0f, 30.0f, 120.0f });
}

float FComplexityEstimator::CalculateVariableComplexity(const TArray<FExtractedVariable>& Variables)
{
	if (Variables.Num() == 0)
	{
		return 0.0f;
	}
	
	float Complexity = 0.0f;
	
	// Base complexity from variable count
	Complexity += Variables.Num() * 0.1f;
	
	// Additional complexity based on variable types
	for (const FExtractedVariable& Variable : Variables)
	{
		if (Variable.Type == TEXT("vector") || Variable.Type == TEXT("rotator") || Variable.Type == TEXT("transform"))
		{
			Complexity += 0.2f; // Complex data types
		}
		else if (Variable.Type == TEXT("array") || Variable.Type == TEXT("map") || Variable.Type == TEXT("set"))
		{
			Complexity += 0.3f; // Collection types
		}
		else if (Variable.Type == TEXT("object") || Variable.Type == TEXT("class"))
		{
			Complexity += 0.25f; // Object references
		}
		else
		{
			Complexity += 0.1f; // Basic types
		}
		
		// Additional complexity for editor-exposed variables
		if (Variable.bEditable)
		{
			Complexity += 0.05f;
		}
	}
	
	return Complexity;
}

float FComplexityEstimator::CalculateFunctionComplexity(const TArray<FExtractedFunction>& Functions)
{
	if (Functions.Num() == 0)
	{
		return 0.0f;
	}
	
	float Complexity = 0.0f;
	
	for (const FExtractedFunction& Function : Functions)
	{
		// Base complexity from function existence
		Complexity += 0.5f;
		
		// Parameter complexity
		Complexity += Function.InputParameters.Num() * 0.1f;
		Complexity += Function.OutputParameters.Num() * 0.15f;
		
		// Function-specific complexity
		Complexity += Function.Complexity;
		
		// Pure functions are generally simpler
		if (Function.bPure)
		{
			Complexity *= 0.8f;
		}
	}
	
	return Complexity;
}

float FComplexityEstimator::CalculateEventComplexity(const TArray<FExtractedEvent>& Events)
{
	if (Events.Num() == 0)
	{
		return 0.0f;
	}
	
	float Complexity = 0.0f;
	
	for (const FExtractedEvent& Event : Events)
	{
		// Base complexity from event existence
		Complexity += 0.3f;
		
		// Custom events are more complex
		if (Event.bCustomEvent)
		{
			Complexity += 0.2f;
		}
		
		// Network events add complexity
		if (Event.bReliable || Event.bCallable)
		{
			Complexity += 0.3f;
		}
		
		// Parameter complexity
		Complexity += Event.Parameters.Num() * 0.1f;
	}
	
	return Complexity;
}

float FComplexityEstimator::CalculateComponentComplexity(const TArray<FExtractedComponent>& Components)
{
	if (Components.Num() == 0)
	{
		return 0.0f;
	}
	
	float Complexity = 0.0f;
	
	for (const FExtractedComponent& Component : Components)
	{
		// Base complexity from component existence
		Complexity += 0.2f;
		
		// Component-specific complexity
		if (Component.ComponentClass.Contains(TEXT("Mesh")))
		{
			Complexity += 0.3f; // Mesh components
		}
		else if (Component.ComponentClass.Contains(TEXT("Collision")))
		{
			Complexity += 0.2f; // Collision components
		}
		else if (Component.ComponentClass.Contains(TEXT("Movement")))
		{
			Complexity += 0.4f; // Movement components
		}
		else if (Component.ComponentClass.Contains(TEXT("Audio")))
		{
			Complexity += 0.25f; // Audio components
		}
		else if (Component.ComponentClass.Contains(TEXT("Light")))
		{
			Complexity += 0.3f; // Light components
		}
		else
		{
			Complexity += 0.15f; // Other components
		}
		
		// Property complexity
		Complexity += Component.Properties.Num() * 0.05f;
	}
	
	return Complexity;
}

float FComplexityEstimator::CalculateRelationshipComplexity(const TArray<FElementRelationship>& Relationships)
{
	if (Relationships.Num() == 0)
	{
		return 0.0f;
	}
	
	float Complexity = 0.0f;
	
	// Base complexity from relationship count
	Complexity += Relationships.Num() * 0.1f;
	
	// Additional complexity based on relationship types
	for (const FElementRelationship& Relationship : Relationships)
	{
		if (Relationship.RelationshipType == TEXT("Calls"))
		{
			Complexity += 0.15f; // Function calls
		}
		else if (Relationship.RelationshipType == TEXT("Triggers"))
		{
			Complexity += 0.2f; // Event triggers
		}
		else if (Relationship.RelationshipType == TEXT("Modifies"))
		{
			Complexity += 0.1f; // Variable modifications
		}
		else
		{
			Complexity += 0.05f; // Other relationships
		}
	}
	
	return Complexity;
}

// ... continue with remaining implementation methods ... 