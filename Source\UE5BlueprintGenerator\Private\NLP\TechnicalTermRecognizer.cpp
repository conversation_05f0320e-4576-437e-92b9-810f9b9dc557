#include "NLP/TechnicalTermRecognizer.h"
#include "UE5BlueprintGeneratorModule.h"
#include "Misc/Char.h"
#include "Misc/CString.h"

DEFINE_LOG_CATEGORY_EXTERN(LogTechnicalTermRecognizer, Log, All);
DEFINE_LOG_CATEGORY(LogTechnicalTermRecognizer);

FTechnicalTermRecognizer::FTechnicalTermRecognizer()
	: MinimumTermLength(3)
	, bCaseSensitive(false)
	, ComplexityThreshold(0.3f)
{
	LoadTermDatabase();
	UE_LOG(LogTechnicalTermRecognizer, Log, TEXT("Technical term recognizer initialized with %d terms"), TermDatabase.Num());
}

FTechnicalTermRecognizer::~FTechnicalTermRecognizer()
{
	UE_LOG(LogTechnicalTermRecognizer, Log, TEXT("Technical term recognizer destroyed"));
}

FTermRecognitionResult FTechnicalTermRecognizer::RecognizeTerms(const FString& InputText)
{
	FTermRecognitionResult Result;
	
	if (InputText.IsEmpty())
	{
		return Result;
	}
	
	// Get all recognized terms
	Result.RecognizedTerms = GetRecognizedTerms(InputText);
	
	// Extract categories
	TSet<FString> UniqueCategories;
	for (const FTechnicalTerm& Term : Result.RecognizedTerms)
	{
		UniqueCategories.Add(Term.Category);
		
		// Check for specific term types
		if (Term.Category == TEXT("Blueprint") || Term.Category == TEXT("Blueprint Node"))
		{
			Result.bContainsBlueprintTerms = true;
		}
		if (Term.Category == TEXT("UE5 Class") || Term.Category == TEXT("UE5 System"))
		{
			Result.bContainsUE5Terms = true;
		}
	}
	Result.Categories = UniqueCategories.Array();
	
	// Calculate technical complexity
	Result.TechnicalComplexity = CalculateTechnicalComplexity(InputText);
	
	UE_LOG(LogTechnicalTermRecognizer, VeryVerbose, TEXT("Recognized %d terms in %d categories"), 
		Result.RecognizedTerms.Num(), Result.Categories.Num());
	
	return Result;
}

TArray<FTechnicalTerm> FTechnicalTermRecognizer::GetRecognizedTerms(const FString& InputText)
{
	TArray<FTechnicalTerm> RecognizedTerms;
	
	if (InputText.IsEmpty())
	{
		return RecognizedTerms;
	}
	
	// Tokenize input for better matching
	TArray<FString> InputTokens = TokenizeInput(InputText);
	FString LowerInput = bCaseSensitive ? InputText : InputText.ToLower();
	
	// Check each term in the database
	for (const FTechnicalTerm& Term : TermDatabase)
	{
		if (IsTermInInput(LowerInput, Term.Term))
		{
			RecognizedTerms.Add(Term);
		}
		
		// Also check synonyms
		for (const FString& Synonym : Term.Synonyms)
		{
			if (IsTermInInput(LowerInput, Synonym))
			{
				RecognizedTerms.Add(Term);
				break; // Don't add the same term multiple times
			}
		}
	}
	
	// Sort by relevance (weight)
	RecognizedTerms.Sort([](const FTechnicalTerm& A, const FTechnicalTerm& B) {
		return A.Weight > B.Weight;
	});
	
	return RecognizedTerms;
}

bool FTechnicalTermRecognizer::ContainsTechnicalTerms(const FString& InputText) const
{
	if (InputText.IsEmpty())
	{
		return false;
	}
	
	FString LowerInput = bCaseSensitive ? InputText : InputText.ToLower();
	
	// Quick check - look for any term in the database
	for (const FTechnicalTerm& Term : TermDatabase)
	{
		if (IsTermInInput(LowerInput, Term.Term))
		{
			return true;
		}
		
		// Check synonyms
		for (const FString& Synonym : Term.Synonyms)
		{
			if (IsTermInInput(LowerInput, Synonym))
			{
				return true;
			}
		}
	}
	
	return false;
}

TArray<FString> FTechnicalTermRecognizer::GetRecognizedCategories(const FString& InputText)
{
	TSet<FString> Categories;
	TArray<FTechnicalTerm> RecognizedTerms = GetRecognizedTerms(InputText);
	
	for (const FTechnicalTerm& Term : RecognizedTerms)
	{
		Categories.Add(Term.Category);
	}
	
	return Categories.Array();
}

bool FTechnicalTermRecognizer::ContainsCategory(const FString& InputText, const FString& Category) const
{
	if (InputText.IsEmpty() || Category.IsEmpty())
	{
		return false;
	}
	
	FString LowerInput = bCaseSensitive ? InputText : InputText.ToLower();
	
	// Check terms in the specified category
	if (const TArray<int32>* CategoryTerms = CategoryMap.Find(Category))
	{
		for (int32 TermIndex : *CategoryTerms)
		{
			if (TermIndex < TermDatabase.Num())
			{
				const FTechnicalTerm& Term = TermDatabase[TermIndex];
				if (IsTermInInput(LowerInput, Term.Term))
				{
					return true;
				}
				
				// Check synonyms
				for (const FString& Synonym : Term.Synonyms)
				{
					if (IsTermInInput(LowerInput, Synonym))
					{
						return true;
					}
				}
			}
		}
	}
	
	return false;
}

float FTechnicalTermRecognizer::GetCategoryWeight(const FString& InputText, const FString& Category)
{
	if (InputText.IsEmpty() || Category.IsEmpty())
	{
		return 0.0f;
	}
	
	float TotalWeight = 0.0f;
	FString LowerInput = bCaseSensitive ? InputText : InputText.ToLower();
	
	// Sum weights of all terms in the category found in the input
	if (const TArray<int32>* CategoryTerms = CategoryMap.Find(Category))
	{
		for (int32 TermIndex : *CategoryTerms)
		{
			if (TermIndex < TermDatabase.Num())
			{
				const FTechnicalTerm& Term = TermDatabase[TermIndex];
				if (IsTermInInput(LowerInput, Term.Term))
				{
					TotalWeight += Term.Weight;
				}
				
				// Check synonyms
				for (const FString& Synonym : Term.Synonyms)
				{
					if (IsTermInInput(LowerInput, Synonym))
					{
						TotalWeight += Term.Weight * 0.8f; // Slightly lower weight for synonyms
						break;
					}
				}
			}
		}
	}
	
	return TotalWeight;
}

float FTechnicalTermRecognizer::CalculateTechnicalComplexity(const FString& InputText)
{
	if (InputText.IsEmpty())
	{
		return 0.0f;
	}
	
	TArray<FTechnicalTerm> RecognizedTerms = GetRecognizedTerms(InputText);
	
	if (RecognizedTerms.Num() == 0)
	{
		return 0.0f;
	}
	
	// Calculate complexity based on:
	// 1. Number of technical terms
	// 2. Diversity of categories
	// 3. Weight of terms
	// 4. Density of technical terms in the text
	
	float TermCountFactor = FMath::Clamp(RecognizedTerms.Num() / 5.0f, 0.0f, 1.0f);
	
	TSet<FString> UniqueCategories;
	float TotalWeight = 0.0f;
	for (const FTechnicalTerm& Term : RecognizedTerms)
	{
		UniqueCategories.Add(Term.Category);
		TotalWeight += Term.Weight;
	}
	
	float CategoryDiversityFactor = FMath::Clamp(UniqueCategories.Num() / 4.0f, 0.0f, 1.0f);
	float WeightFactor = FMath::Clamp(TotalWeight / 10.0f, 0.0f, 1.0f);
	
	// Calculate density (technical terms per 100 words)
	TArray<FString> Words = TokenizeInput(InputText);
	float DensityFactor = Words.Num() > 0 ? FMath::Clamp((RecognizedTerms.Num() * 100.0f) / Words.Num() / 20.0f, 0.0f, 1.0f) : 0.0f;
	
	// Weighted combination
	float Complexity = (TermCountFactor * 0.3f) + (CategoryDiversityFactor * 0.25f) + (WeightFactor * 0.25f) + (DensityFactor * 0.2f);
	
	return FMath::Clamp(Complexity, 0.0f, 1.0f);
}

bool FTechnicalTermRecognizer::IsHighlyTechnical(const FString& InputText) const
{
	return const_cast<FTechnicalTermRecognizer*>(this)->CalculateTechnicalComplexity(InputText) >= ComplexityThreshold;
}

void FTechnicalTermRecognizer::LoadTermDatabase()
{
	TermDatabase.Empty();
	TermIndexMap.Empty();
	CategoryMap.Empty();
	
	// Initialize all term categories
	InitializeBlueprintTerms();
	InitializeDataTypeTerms();
	InitializeUE5ClassTerms();
	InitializeComponentTerms();
	InitializeEventTerms();
	InitializeFunctionTerms();
	InitializeNodeTerms();
	InitializeSystemTerms();
	
	// Rebuild index maps for fast lookup
	RebuildIndexMaps();
	
	UE_LOG(LogTechnicalTermRecognizer, Log, TEXT("Loaded %d technical terms in %d categories"), 
		TermDatabase.Num(), CategoryMap.Num());
}

void FTechnicalTermRecognizer::AddCustomTerm(const FTechnicalTerm& Term)
{
	// Check if term already exists
	if (TermIndexMap.Contains(NormalizeTermForComparison(Term.Term)))
	{
		UE_LOG(LogTechnicalTermRecognizer, Warning, TEXT("Term '%s' already exists in database"), *Term.Term);
		return;
	}
	
	TermDatabase.Add(Term);
	RebuildIndexMaps();
	
	UE_LOG(LogTechnicalTermRecognizer, Log, TEXT("Added custom term: %s (%s)"), *Term.Term, *Term.Category);
}

void FTechnicalTermRecognizer::RemoveTerm(const FString& TermName)
{
	FString NormalizedTerm = NormalizeTermForComparison(TermName);
	
	if (int32* IndexPtr = TermIndexMap.Find(NormalizedTerm))
	{
		int32 Index = *IndexPtr;
		if (Index < TermDatabase.Num())
		{
			TermDatabase.RemoveAt(Index);
			RebuildIndexMaps();
			UE_LOG(LogTechnicalTermRecognizer, Log, TEXT("Removed term: %s"), *TermName);
		}
	}
}

TArray<FTechnicalTerm> FTechnicalTermRecognizer::GetTermsByCategory(const FString& Category) const
{
	TArray<FTechnicalTerm> CategoryTerms;
	
	if (const TArray<int32>* TermIndices = CategoryMap.Find(Category))
	{
		for (int32 Index : *TermIndices)
		{
			if (Index < TermDatabase.Num())
			{
				CategoryTerms.Add(TermDatabase[Index]);
			}
		}
	}
	
	return CategoryTerms;
}

TArray<FString> FTechnicalTermRecognizer::GetAllCategories() const
{
	TArray<FString> Categories;
	CategoryMap.GetKeys(Categories);
	return Categories;
}

bool FTechnicalTermRecognizer::IsTermInDatabase(const FString& Term) const
{
	return TermIndexMap.Contains(NormalizeTermForComparison(Term));
}

FTechnicalTerm FTechnicalTermRecognizer::GetTermInfo(const FString& Term) const
{
	FString NormalizedTerm = NormalizeTermForComparison(Term);
	
	if (const int32* IndexPtr = TermIndexMap.Find(NormalizedTerm))
	{
		int32 Index = *IndexPtr;
		if (Index < TermDatabase.Num())
		{
			return TermDatabase[Index];
		}
	}
	
	return FTechnicalTerm(); // Return empty term if not found
}

TArray<FString> FTechnicalTermRecognizer::GetSimilarTerms(const FString& Term) const
{
	TArray<FString> SimilarTerms;
	FString LowerTerm = Term.ToLower();
	
	// Find terms that contain the input term or vice versa
	for (const FTechnicalTerm& DatabaseTerm : TermDatabase)
	{
		FString LowerDatabaseTerm = DatabaseTerm.Term.ToLower();
		
		if (LowerDatabaseTerm.Contains(LowerTerm) || LowerTerm.Contains(LowerDatabaseTerm))
		{
			SimilarTerms.Add(DatabaseTerm.Term);
		}
		
		// Check synonyms
		for (const FString& Synonym : DatabaseTerm.Synonyms)
		{
			FString LowerSynonym = Synonym.ToLower();
			if (LowerSynonym.Contains(LowerTerm) || LowerTerm.Contains(LowerSynonym))
			{
				SimilarTerms.AddUnique(DatabaseTerm.Term);
			}
		}
	}
	
	return SimilarTerms;
}

void FTechnicalTermRecognizer::SetMinimumTermLength(int32 MinLength)
{
	MinimumTermLength = FMath::Max(1, MinLength);
}

void FTechnicalTermRecognizer::SetCaseSensitive(bool bCaseSensitive)
{
	this->bCaseSensitive = bCaseSensitive;
}

void FTechnicalTermRecognizer::SetComplexityThreshold(float Threshold)
{
	ComplexityThreshold = FMath::Clamp(Threshold, 0.0f, 1.0f);
}

bool FTechnicalTermRecognizer::IsTermInInput(const FString& InputText, const FString& Term) const
{
	if (InputText.IsEmpty() || Term.IsEmpty() || Term.Len() < MinimumTermLength)
	{
		return false;
	}
	
	FString SearchTerm = bCaseSensitive ? Term : Term.ToLower();
	FString SearchText = bCaseSensitive ? InputText : InputText.ToLower();
	
	// Look for whole word matches
	int32 FoundIndex = SearchText.Find(SearchTerm);
	while (FoundIndex != INDEX_NONE)
	{
		// Check if this is a whole word (word boundaries)
		bool bIsWholeWord = true;
		
		// Check character before
		if (FoundIndex > 0 && !IsWordBoundary(SearchText, FoundIndex - 1))
		{
			bIsWholeWord = false;
		}
		
		// Check character after
		int32 EndIndex = FoundIndex + SearchTerm.Len();
		if (EndIndex < SearchText.Len() && !IsWordBoundary(SearchText, EndIndex))
		{
			bIsWholeWord = false;
		}
		
		if (bIsWholeWord)
		{
			return true;
		}
		
		// Continue searching
		FoundIndex = SearchText.Find(SearchTerm, FoundIndex + 1);
	}
	
	return false;
}

TArray<FString> FTechnicalTermRecognizer::ExtractPotentialTerms(const FString& InputText) const
{
	return TokenizeInput(InputText);
}

float FTechnicalTermRecognizer::CalculateTermRelevance(const FString& Term, const FString& InputText) const
{
	if (!IsTermInInput(InputText, Term))
	{
		return 0.0f;
	}
	
	// Base relevance
	float Relevance = 1.0f;
	
	// Boost relevance based on term frequency
	FString LowerInput = InputText.ToLower();
	FString LowerTerm = Term.ToLower();
	int32 Frequency = 0;
	int32 SearchIndex = 0;
	
	while ((SearchIndex = LowerInput.Find(LowerTerm, SearchIndex)) != INDEX_NONE)
	{
		Frequency++;
		SearchIndex += LowerTerm.Len();
	}
	
	Relevance *= (1.0f + (Frequency - 1) * 0.2f); // Diminishing returns for frequency
	
	return FMath::Clamp(Relevance, 0.0f, 2.0f);
}

void FTechnicalTermRecognizer::InitializeBlueprintTerms()
{
	// Core Blueprint concepts
	TermDatabase.Add(FTechnicalTerm(TEXT("Blueprint"), TEXT("Blueprint"), TEXT("A visual scripting system in UE5"), 2.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Event Graph"), TEXT("Blueprint"), TEXT("The main graph where event-driven logic is implemented"), 1.8f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Construction Script"), TEXT("Blueprint"), TEXT("A graph that runs when the blueprint is created or modified"), 1.6f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Function"), TEXT("Blueprint"), TEXT("A reusable collection of nodes that can be called from multiple places"), 1.5f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Macro"), TEXT("Blueprint"), TEXT("A reusable collection of nodes that are expanded at compile time"), 1.4f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Variable"), TEXT("Blueprint"), TEXT("A container for a value that can be accessed and modified"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Component"), TEXT("Blueprint"), TEXT("A reusable piece of functionality that can be added to a blueprint"), 1.5f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Interface"), TEXT("Blueprint"), TEXT("A collection of functions that can be implemented by different blueprints"), 1.4f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Event Dispatcher"), TEXT("Blueprint"), TEXT("A mechanism for triggering multiple events from a single source"), 1.3f));
	
	// Add synonyms
	TermDatabase.Last().Synonyms.Add(TEXT("Dispatcher"));
	TermDatabase.Last().Synonyms.Add(TEXT("Event Delegate"));
}

void FTechnicalTermRecognizer::InitializeDataTypeTerms()
{
	// Basic data types
	TermDatabase.Add(FTechnicalTerm(TEXT("Boolean"), TEXT("Data Type"), TEXT("A true/false value"), 1.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Integer"), TEXT("Data Type"), TEXT("A whole number value"), 1.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Float"), TEXT("Data Type"), TEXT("A decimal number value"), 1.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("String"), TEXT("Data Type"), TEXT("A text value"), 1.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Text"), TEXT("Data Type"), TEXT("Localized text value"), 1.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Name"), TEXT("Data Type"), TEXT("An optimized string for identifiers"), 1.0f));
	
	// Struct types
	TermDatabase.Add(FTechnicalTerm(TEXT("Vector"), TEXT("Data Type"), TEXT("A 3D coordinate or direction"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Rotator"), TEXT("Data Type"), TEXT("A 3D rotation"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Transform"), TEXT("Data Type"), TEXT("A combination of location, rotation, and scale"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Color"), TEXT("Data Type"), TEXT("A color value"), 1.0f));
	
	// Collection types
	TermDatabase.Add(FTechnicalTerm(TEXT("Array"), TEXT("Data Type"), TEXT("A collection of items of the same type"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Set"), TEXT("Data Type"), TEXT("A collection of unique items of the same type"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Map"), TEXT("Data Type"), TEXT("A collection of key-value pairs"), 1.2f));
	
	// Add synonyms for common types
	TermDatabase[TermDatabase.Num()-13].Synonyms.Add(TEXT("Bool")); // Boolean
	TermDatabase[TermDatabase.Num()-12].Synonyms.Add(TEXT("Int")); // Integer
	TermDatabase[TermDatabase.Num()-12].Synonyms.Add(TEXT("Int32"));
	TermDatabase[TermDatabase.Num()-11].Synonyms.Add(TEXT("Number")); // Float
	TermDatabase[TermDatabase.Num()-11].Synonyms.Add(TEXT("Double"));
}

void FTechnicalTermRecognizer::InitializeUE5ClassTerms()
{
	// Core UE5 classes
	TermDatabase.Add(FTechnicalTerm(TEXT("Actor"), TEXT("UE5 Class"), TEXT("A base class for objects that can be placed in the world"), 1.8f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Pawn"), TEXT("UE5 Class"), TEXT("An Actor that can be possessed by a Controller"), 1.6f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Character"), TEXT("UE5 Class"), TEXT("A Pawn with a CharacterMovementComponent and a SkeletalMeshComponent"), 1.7f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Controller"), TEXT("UE5 Class"), TEXT("An Actor that can possess a Pawn"), 1.5f));
	TermDatabase.Add(FTechnicalTerm(TEXT("PlayerController"), TEXT("UE5 Class"), TEXT("A Controller that is controlled by a player"), 1.6f));
	TermDatabase.Add(FTechnicalTerm(TEXT("AIController"), TEXT("UE5 Class"), TEXT("A Controller that is controlled by AI"), 1.5f));
	TermDatabase.Add(FTechnicalTerm(TEXT("GameMode"), TEXT("UE5 Class"), TEXT("A class that defines the rules of the game"), 1.4f));
	TermDatabase.Add(FTechnicalTerm(TEXT("GameState"), TEXT("UE5 Class"), TEXT("A class that tracks the state of the game"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("PlayerState"), TEXT("UE5 Class"), TEXT("A class that tracks the state of a player"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("HUD"), TEXT("UE5 Class"), TEXT("A class that displays information to the player"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Widget"), TEXT("UE5 Class"), TEXT("A UI element"), 1.4f));
	TermDatabase.Add(FTechnicalTerm(TEXT("UserWidget"), TEXT("UE5 Class"), TEXT("A custom UI widget"), 1.3f));
}

void FTechnicalTermRecognizer::InitializeComponentTerms()
{
	// Common components
	TermDatabase.Add(FTechnicalTerm(TEXT("StaticMeshComponent"), TEXT("Component"), TEXT("A component for rendering static meshes"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("SkeletalMeshComponent"), TEXT("Component"), TEXT("A component for rendering skeletal meshes"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("CameraComponent"), TEXT("Component"), TEXT("A component that provides a camera view"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("LightComponent"), TEXT("Component"), TEXT("A component that emits light"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("AudioComponent"), TEXT("Component"), TEXT("A component for playing audio"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("CollisionComponent"), TEXT("Component"), TEXT("A component for collision detection"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("MovementComponent"), TEXT("Component"), TEXT("A component that handles movement"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("CharacterMovementComponent"), TEXT("Component"), TEXT("A specialized movement component for characters"), 1.4f));
	
	// Add synonyms
	TermDatabase.Last().Synonyms.Add(TEXT("Character Movement"));
	TermDatabase[TermDatabase.Num()-2].Synonyms.Add(TEXT("Movement"));
}

void FTechnicalTermRecognizer::InitializeEventTerms()
{
	// Common events
	TermDatabase.Add(FTechnicalTerm(TEXT("Begin Play"), TEXT("Event"), TEXT("Called when the game starts or when spawned"), 1.5f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Tick"), TEXT("Event"), TEXT("Called every frame"), 1.4f));
	TermDatabase.Add(FTechnicalTerm(TEXT("End Play"), TEXT("Event"), TEXT("Called when the actor is destroyed"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Overlap"), TEXT("Event"), TEXT("Called when objects overlap"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Hit"), TEXT("Event"), TEXT("Called when objects collide"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Input"), TEXT("Event"), TEXT("Called when input is received"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Destroyed"), TEXT("Event"), TEXT("Called when the actor is destroyed"), 1.2f));
	
	// Add synonyms
	TermDatabase[TermDatabase.Num()-6].Synonyms.Add(TEXT("BeginPlay"));
	TermDatabase[TermDatabase.Num()-5].Synonyms.Add(TEXT("Update"));
	TermDatabase[TermDatabase.Num()-4].Synonyms.Add(TEXT("EndPlay"));
	TermDatabase[TermDatabase.Num()-3].Synonyms.Add(TEXT("OnOverlap"));
	TermDatabase[TermDatabase.Num()-2].Synonyms.Add(TEXT("OnHit"));
	TermDatabase[TermDatabase.Num()-2].Synonyms.Add(TEXT("Collision"));
}

void FTechnicalTermRecognizer::InitializeFunctionTerms()
{
	// Common function types
	TermDatabase.Add(FTechnicalTerm(TEXT("Pure Function"), TEXT("Function"), TEXT("A function that doesn't modify state"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Impure Function"), TEXT("Function"), TEXT("A function that can modify state"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Static Function"), TEXT("Function"), TEXT("A function that doesn't require an instance"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Override"), TEXT("Function"), TEXT("A function that overrides a parent function"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Virtual Function"), TEXT("Function"), TEXT("A function that can be overridden"), 1.1f));
	
	// Function-related terms
	TermDatabase.Add(FTechnicalTerm(TEXT("Parameter"), TEXT("Function"), TEXT("An input value to a function"), 1.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Return Value"), TEXT("Function"), TEXT("An output value from a function"), 1.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Local Variable"), TEXT("Function"), TEXT("A variable that exists only within a function"), 1.0f));
	
	// Add synonyms
	TermDatabase[TermDatabase.Num()-3].Synonyms.Add(TEXT("Input"));
	TermDatabase[TermDatabase.Num()-3].Synonyms.Add(TEXT("Argument"));
	TermDatabase[TermDatabase.Num()-2].Synonyms.Add(TEXT("Output"));
	TermDatabase[TermDatabase.Num()-2].Synonyms.Add(TEXT("Result"));
}

void FTechnicalTermRecognizer::InitializeNodeTerms()
{
	// Blueprint nodes
	TermDatabase.Add(FTechnicalTerm(TEXT("Cast"), TEXT("Blueprint Node"), TEXT("A node that attempts to convert one object type to another"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Branch"), TEXT("Blueprint Node"), TEXT("A node that executes one of two paths based on a boolean condition"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Sequence"), TEXT("Blueprint Node"), TEXT("A node that executes a series of operations in order"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("For Loop"), TEXT("Blueprint Node"), TEXT("A node that repeats a section of logic a specified number of times"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("While Loop"), TEXT("Blueprint Node"), TEXT("A node that repeats a section of logic while a condition is true"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Switch"), TEXT("Blueprint Node"), TEXT("A node that selects one of multiple execution paths based on an input value"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Timeline"), TEXT("Blueprint Node"), TEXT("A node that interpolates values over time"), 1.4f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Delay"), TEXT("Blueprint Node"), TEXT("A node that delays execution"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Print String"), TEXT("Blueprint Node"), TEXT("A node that prints text to the screen"), 1.0f));
	
	// Add synonyms
	TermDatabase[TermDatabase.Num()-7].Synonyms.Add(TEXT("If"));
	TermDatabase[TermDatabase.Num()-6].Synonyms.Add(TEXT("Then"));
	TermDatabase[TermDatabase.Num()-5].Synonyms.Add(TEXT("Loop"));
	TermDatabase[TermDatabase.Num()-4].Synonyms.Add(TEXT("Loop"));
	TermDatabase[TermDatabase.Num()-3].Synonyms.Add(TEXT("Select"));
	TermDatabase[TermDatabase.Num()-1].Synonyms.Add(TEXT("Print"));
	TermDatabase[TermDatabase.Num()-1].Synonyms.Add(TEXT("Log"));
}

void FTechnicalTermRecognizer::InitializeSystemTerms()
{
	// UE5 system terms
	TermDatabase.Add(FTechnicalTerm(TEXT("Unreal Engine"), TEXT("UE5 System"), TEXT("The game engine"), 2.0f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Level"), TEXT("UE5 System"), TEXT("A game world or scene"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("World"), TEXT("UE5 System"), TEXT("The game world container"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Scene"), TEXT("UE5 System"), TEXT("A collection of objects in the world"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Mesh"), TEXT("UE5 System"), TEXT("A 3D model"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Material"), TEXT("UE5 System"), TEXT("A surface shader"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Texture"), TEXT("UE5 System"), TEXT("An image used in materials"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Animation"), TEXT("UE5 System"), TEXT("Movement or transformation over time"), 1.3f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Physics"), TEXT("UE5 System"), TEXT("Physical simulation"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Collision"), TEXT("UE5 System"), TEXT("Physical interaction between objects"), 1.2f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Rendering"), TEXT("UE5 System"), TEXT("The process of drawing graphics"), 1.1f));
	TermDatabase.Add(FTechnicalTerm(TEXT("Lighting"), TEXT("UE5 System"), TEXT("Illumination in the scene"), 1.1f));
	
	// Add synonyms
	TermDatabase[TermDatabase.Num()-12].Synonyms.Add(TEXT("UE5"));
	TermDatabase[TermDatabase.Num()-12].Synonyms.Add(TEXT("Engine"));
	TermDatabase[TermDatabase.Num()-11].Synonyms.Add(TEXT("Map"));
	TermDatabase[TermDatabase.Num()-8].Synonyms.Add(TEXT("Model"));
	TermDatabase[TermDatabase.Num()-8].Synonyms.Add(TEXT("Geometry"));
	TermDatabase[TermDatabase.Num()-7].Synonyms.Add(TEXT("Shader"));
	TermDatabase[TermDatabase.Num()-6].Synonyms.Add(TEXT("Image"));
	TermDatabase[TermDatabase.Num()-5].Synonyms.Add(TEXT("Anim"));
	TermDatabase[TermDatabase.Num()-3].Synonyms.Add(TEXT("Hit"));
	TermDatabase[TermDatabase.Num()-2].Synonyms.Add(TEXT("Render"));
	TermDatabase[TermDatabase.Num()-1].Synonyms.Add(TEXT("Light"));
}

FString FTechnicalTermRecognizer::NormalizeTermForComparison(const FString& Term) const
{
	return bCaseSensitive ? Term : Term.ToLower();
}

bool FTechnicalTermRecognizer::DoesTermMatch(const FString& InputTerm, const FString& DatabaseTerm) const
{
	FString NormalizedInput = NormalizeTermForComparison(InputTerm);
	FString NormalizedDatabase = NormalizeTermForComparison(DatabaseTerm);
	
	return NormalizedInput == NormalizedDatabase;
}

void FTechnicalTermRecognizer::RebuildIndexMaps()
{
	TermIndexMap.Empty();
	CategoryMap.Empty();
	
	for (int32 i = 0; i < TermDatabase.Num(); i++)
	{
		const FTechnicalTerm& Term = TermDatabase[i];
		
		// Add to term index map
		FString NormalizedTerm = NormalizeTermForComparison(Term.Term);
		TermIndexMap.Add(NormalizedTerm, i);
		
		// Add to category map
		if (!CategoryMap.Contains(Term.Category))
		{
			CategoryMap.Add(Term.Category, TArray<int32>());
		}
		CategoryMap[Term.Category].Add(i);
		
		// Add synonyms to index map
		for (const FString& Synonym : Term.Synonyms)
		{
			FString NormalizedSynonym = NormalizeTermForComparison(Synonym);
			if (!TermIndexMap.Contains(NormalizedSynonym))
			{
				TermIndexMap.Add(NormalizedSynonym, i);
			}
		}
	}
}

TArray<FString> FTechnicalTermRecognizer::TokenizeInput(const FString& InputText) const
{
	TArray<FString> Tokens;
	
	if (InputText.IsEmpty())
	{
		return Tokens;
	}
	
	FString CurrentToken;
	for (int32 i = 0; i < InputText.Len(); i++)
	{
		TCHAR Char = InputText[i];
		
		if (FChar::IsAlnum(Char) || Char == '\'' || Char == '-')
		{
			CurrentToken.AppendChar(Char);
		}
		else
		{
			if (!CurrentToken.IsEmpty() && CurrentToken.Len() >= MinimumTermLength)
			{
				Tokens.Add(CurrentToken);
			}
			CurrentToken.Empty();
		}
	}
	
	// Add the last token if any
	if (!CurrentToken.IsEmpty() && CurrentToken.Len() >= MinimumTermLength)
	{
		Tokens.Add(CurrentToken);
	}
	
	return Tokens;
}

bool FTechnicalTermRecognizer::IsWordBoundary(const FString& Text, int32 Position) const
{
	if (Position < 0 || Position >= Text.Len())
	{
		return true;
	}
	
	TCHAR Char = Text[Position];
	return !FChar::IsAlnum(Char) && Char != '\'' && Char != '-';
} 