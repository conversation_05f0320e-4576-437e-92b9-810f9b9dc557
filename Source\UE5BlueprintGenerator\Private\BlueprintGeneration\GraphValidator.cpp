#include "BlueprintGeneration/GraphValidator.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "EdGraph/EdGraph.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"

DEFINE_LOG_CATEGORY(LogGraphValidator);

FGRAPHVALIDATOR::FGRAPHVALIDATOR()
    : bIsInitialized(false)
    , bVerboseLogging(false)
    , bTrackStatistics(true)
    , TotalValidations(0)
    , TotalIssuesFound(0)
    , TotalAutoFixes(0)
    , LastValidationTimeMs(0.0f)
{
    UE_LOG(LogGraphValidator, Log, TEXT("GraphValidator created"));
}

FGRAPHVALIDATOR::~FGRAPHVALIDATOR()
{
    Shutdown();
    UE_LOG(LogGraphValidator, Log, TEXT("GraphValidator destroyed"));
}

bool FGRAPHVALIDATOR::Initialize()
{
    if (bIsInitialized)
    {
        UE_LOG(LogGraphValidator, Warning, TEXT("GraphValidator already initialized"));
        return true;
    }

    UE_LOG(LogGraphValidator, Log, TEXT("Initializing GraphValidator..."));

    // Clear existing data
    ValidationRules.Empty();
    DisabledRules.Empty();
    ValidationStatistics.Empty();

    // Initialize default validation rules
    InitializeDefaultRules();

    // Initialize default dependencies if none provided
    if (!CompatibilityChecker.IsValid())
    {
        CompatibilityChecker = MakeShared<FPinCompatibilityChecker>();
        if (!CompatibilityChecker->Initialize())
        {
            UE_LOG(LogGraphValidator, Error, TEXT("Failed to initialize default pin compatibility checker"));
            return false;
        }
    }

    if (!ConnectionManager.IsValid())
    {
        ConnectionManager = MakeShared<FCONNECTIONMANAGER>();
        if (!ConnectionManager->Initialize())
        {
            UE_LOG(LogGraphValidator, Error, TEXT("Failed to initialize default connection manager"));
            return false;
        }
    }

    bIsInitialized = true;
    
    UE_LOG(LogGraphValidator, Log, TEXT("GraphValidator initialized successfully"));
    LogValidationActivity(TEXT("Graph validator initialized"));

    return true;
}

void FGRAPHVALIDATOR::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    UE_LOG(LogGraphValidator, Log, TEXT("Shutting down GraphValidator..."));

    // Clear all data
    ValidationRules.Empty();
    DisabledRules.Empty();
    ValidationStatistics.Empty();
    CompatibilityChecker.Reset();
    ConnectionManager.Reset();

    bIsInitialized = false;
    
    UE_LOG(LogGraphValidator, Log, TEXT("GraphValidator shutdown complete"));
}

void FGRAPHVALIDATOR::SetPinCompatibilityChecker(TSharedPtr<FPinCompatibilityChecker> InCompatibilityChecker)
{
    CompatibilityChecker = InCompatibilityChecker;
    LogValidationActivity(TEXT("Pin compatibility checker updated"));
}

void FGRAPHVALIDATOR::SetConnectionManager(TSharedPtr<FCONNECTIONMANAGER> InConnectionManager)
{
    ConnectionManager = InConnectionManager;
    LogValidationActivity(TEXT("Connection manager updated"));
}

// Validation Operations

FVALIDATIONRESULT FGRAPHVALIDATOR::ValidateGraph(UEdGraph* Graph, const FVALIDATIONCONFIG& Config)
{
    FVALIDATIONRESULT Result;
    double StartTime = FPlatformTime::Seconds();

    if (!bIsInitialized || !Graph)
    {
        FVALIDATIONISSUE Issue = CreateValidationIssue(TEXT("GRAPH_001"), EValidationSeverity::Critical, 
            TEXT("Invalid Graph"), TEXT("Graph validator not initialized or invalid graph provided"));
        Result.Issues.Add(Issue);
        Result.bIsValid = false;
        Result.bCanCompile = false;
        Result.ValidationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        return Result;
    }

    LogValidationActivity(FString::Printf(TEXT("Starting validation of graph '%s'"), *Graph->GetName()));

    // Initialize result counters
    Result.IssueCountBySeverity.Add(EValidationSeverity::Info, 0);
    Result.IssueCountBySeverity.Add(EValidationSeverity::Warning, 0);
    Result.IssueCountBySeverity.Add(EValidationSeverity::Error, 0);
    Result.IssueCountBySeverity.Add(EValidationSeverity::Critical, 0);

    Result.IssueCountByCategory.Add(EValidationCategory::Structure, 0);
    Result.IssueCountByCategory.Add(EValidationCategory::Connections, 0);
    Result.IssueCountByCategory.Add(EValidationCategory::Nodes, 0);
    Result.IssueCountByCategory.Add(EValidationCategory::Logic, 0);
    Result.IssueCountByCategory.Add(EValidationCategory::Performance, 0);
    Result.IssueCountByCategory.Add(EValidationCategory::Compliance, 0);
    Result.IssueCountByCategory.Add(EValidationCategory::Compilation, 0);
    Result.IssueCountByCategory.Add(EValidationCategory::Runtime, 0);

    // Validate graph structure
    if (Config.bValidateStructure)
    {
        TArray<FVALIDATIONISSUE> StructureIssues = ValidateGraphStructure(Graph, Config);
        Result.Issues.Append(StructureIssues);
    }

    // Validate connections
    if (Config.bValidateConnections)
    {
        TArray<FVALIDATIONISSUE> ConnectionIssues = ValidateGraphConnections(Graph, Config);
        Result.Issues.Append(ConnectionIssues);
    }

    // Validate individual nodes
    if (Config.bValidateNodes)
    {
        for (UEdGraphNode* Node : Graph->Nodes)
        {
            if (Node)
            {
                TArray<FVALIDATIONISSUE> NodeIssues = ValidateNode(Node, Config);
                Result.Issues.Append(NodeIssues);
                Result.NodesValidated++;
            }
        }
    }

    // Validate logic flow
    if (Config.bValidateLogic)
    {
        TArray<FVALIDATIONISSUE> LogicIssues = ValidateLogicFlow(Graph, Config);
        Result.Issues.Append(LogicIssues);
    }

    // Validate performance
    if (Config.bValidatePerformance)
    {
        TArray<FVALIDATIONISSUE> PerformanceIssues = ValidatePerformance(Graph, Config);
        Result.Issues.Append(PerformanceIssues);
    }

    // Validate compliance
    if (Config.bValidateCompliance)
    {
        TArray<FVALIDATIONISSUE> ComplianceIssues = ValidateCompliance(Graph, Config);
        Result.Issues.Append(ComplianceIssues);
    }

    // Filter issues based on configuration
    TArray<FVALIDATIONISSUE> FilteredIssues;
    for (const FVALIDATIONISSUE& Issue : Result.Issues)
    {
        if (ShouldReportIssue(Issue, Config))
        {
            FilteredIssues.Add(Issue);
            
            // Update counters
            Result.IssueCountBySeverity[Issue.Severity]++;
            Result.IssueCountByCategory[Issue.Category]++;
            
            // Check for critical errors
            if (Issue.Severity == EValidationSeverity::Critical)
            {
                Result.bCanCompile = false;
                if (Config.bStopOnCriticalError)
                {
                    break;
                }
            }
        }
    }
    Result.Issues = FilteredIssues;

    // Calculate quality metrics
    CalculateQualityMetrics(Graph, Result);

    // Determine overall validity
    Result.bIsValid = (Result.IssueCountBySeverity[EValidationSeverity::Error] == 0 && 
                      Result.IssueCountBySeverity[EValidationSeverity::Critical] == 0);

    // Generate summary
    Result.Summary = FString::Printf(TEXT("Validation completed: %d issues found (%d errors, %d warnings)"), 
        Result.Issues.Num(), 
        Result.IssueCountBySeverity[EValidationSeverity::Error] + Result.IssueCountBySeverity[EValidationSeverity::Critical],
        Result.IssueCountBySeverity[EValidationSeverity::Warning]);

    Result.ValidationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    LastValidationTimeMs = Result.ValidationTimeMs;
    TotalValidations++;
    TotalIssuesFound += Result.Issues.Num();

    LogValidationActivity(FString::Printf(TEXT("Graph validation completed: %s"), *Result.Summary));
    UpdateValidationStatistics(TEXT("ValidateGraph"));

    return Result;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::ValidateNode(UEdGraphNode* Node, const FVALIDATIONCONFIG& Config)
{
    TArray<FVALIDATIONISSUE> Issues;

    if (!bIsInitialized || !Node)
    {
        return Issues;
    }

    return ValidateNodeInternal(Node, Config);
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::ValidateConnection(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, const FVALIDATIONCONFIG& Config)
{
    TArray<FVALIDATIONISSUE> Issues;

    if (!bIsInitialized || !SourcePin || !TargetPin)
    {
        return Issues;
    }

    return ValidateConnectionInternal(SourcePin, TargetPin, Config);
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::ValidateGraphStructure(UEdGraph* Graph, const FVALIDATIONCONFIG& Config)
{
    TArray<FVALIDATIONISSUE> Issues;

    if (!Graph)
    {
        return Issues;
    }

    // Check for empty graph
    if (Graph->Nodes.Num() == 0)
    {
        Issues.Add(CreateValidationIssue(TEXT("STRUCT_001"), EValidationSeverity::Warning, 
            TEXT("Empty Graph"), TEXT("Graph contains no nodes")));
    }

    // Check for disconnected nodes
    TArray<FVALIDATIONISSUE> UnconnectedIssues = CheckUnconnectedPins(Graph);
    Issues.Append(UnconnectedIssues);

    // Check for circular dependencies
    TArray<FVALIDATIONISSUE> CircularIssues = CheckCircularDependencies(Graph);
    Issues.Append(CircularIssues);

    // Check execution flow
    TArray<FVALIDATIONISSUE> ExecutionIssues = CheckExecutionFlow(Graph);
    Issues.Append(ExecutionIssues);

    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::ValidateGraphConnections(UEdGraph* Graph, const FVALIDATIONCONFIG& Config)
{
    TArray<FVALIDATIONISSUE> Issues;

    if (!Graph || !CompatibilityChecker.IsValid())
    {
        return Issues;
    }

    // Validate all connections using the compatibility checker
    TArray<FString> ConnectionErrors = CompatibilityChecker->ValidateGraphConnections(Graph);
    
    for (const FString& Error : ConnectionErrors)
    {
        Issues.Add(CreateValidationIssue(TEXT("CONN_001"), EValidationSeverity::Error, 
            TEXT("Invalid Connection"), Error));
    }

    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::ValidateLogicFlow(UEdGraph* Graph, const FVALIDATIONCONFIG& Config)
{
    TArray<FVALIDATIONISSUE> Issues;

    if (!Graph)
    {
        return Issues;
    }

    // Check for unreachable nodes
    TSet<UEdGraphNode*> ReachableNodes;
    TArray<UEdGraphNode*> StartNodes;

    // Find entry points (event nodes)
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (Node && Node->IsA<UK2Node_Event>())
        {
            StartNodes.Add(Node);
        }
    }

    // Trace reachable nodes from entry points
    for (UEdGraphNode* StartNode : StartNodes)
    {
        TSet<UEdGraphNode*> Visited;
        TraceReachableNodes(StartNode, ReachableNodes, Visited);
    }

    // Report unreachable nodes
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (Node && !ReachableNodes.Contains(Node) && !Node->IsA<UK2Node_Event>())
        {
            Issues.Add(CreateValidationIssue(TEXT("LOGIC_001"), EValidationSeverity::Warning, 
                TEXT("Unreachable Node"), TEXT("Node is not reachable from any entry point"), Node));
        }
    }

    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::ValidatePerformance(UEdGraph* Graph, const FVALIDATIONCONFIG& Config)
{
    TArray<FVALIDATIONISSUE> Issues;

    if (!Graph || !Config.bIncludePerformanceSuggestions)
    {
        return Issues;
    }

    TArray<FVALIDATIONISSUE> PerformanceIssues = CheckPerformanceIssues(Graph);
    Issues.Append(PerformanceIssues);

    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::ValidateCompliance(UEdGraph* Graph, const FVALIDATIONCONFIG& Config)
{
    TArray<FVALIDATIONISSUE> Issues;

    if (!Graph)
    {
        return Issues;
    }

    // Check node naming conventions
    TArray<FVALIDATIONISSUE> NamingIssues = CheckNodeNaming(Graph);
    Issues.Append(NamingIssues);

    // Check compilation requirements
    TArray<FVALIDATIONISSUE> CompilationIssues = CheckCompilationRequirements(Graph);
    Issues.Append(CompilationIssues);

    return Issues;
}

// Rule Management

void FGRAPHVALIDATOR::AddValidationRule(const FVALIDATIONRULE& Rule)
{
    ValidationRules.Add(Rule.RuleId, Rule);
    LogValidationActivity(FString::Printf(TEXT("Added validation rule: %s"), *Rule.RuleId));
}

void FGRAPHVALIDATOR::RemoveValidationRule(const FString& RuleId)
{
    ValidationRules.Remove(RuleId);
    DisabledRules.Remove(RuleId);
    LogValidationActivity(FString::Printf(TEXT("Removed validation rule: %s"), *RuleId));
}

void FGRAPHVALIDATOR::SetRuleEnabled(const FString& RuleId, bool bEnabled)
{
    if (bEnabled)
    {
        DisabledRules.Remove(RuleId);
    }
    else
    {
        DisabledRules.Add(RuleId);
    }
    LogValidationActivity(FString::Printf(TEXT("Rule %s %s"), *RuleId, bEnabled ? TEXT("enabled") : TEXT("disabled")));
}

// Utility Functions

TMap<FString, FString> FGRAPHVALIDATOR::GetValidationStatistics()
{
    TMap<FString, FString> Stats;
    
    Stats.Add(TEXT("TotalValidations"), FString::FromInt(TotalValidations));
    Stats.Add(TEXT("TotalIssuesFound"), FString::FromInt(TotalIssuesFound));
    Stats.Add(TEXT("TotalAutoFixes"), FString::FromInt(TotalAutoFixes));
    Stats.Add(TEXT("LastValidationTimeMs"), FString::SanitizeFloat(LastValidationTimeMs));
    Stats.Add(TEXT("IsInitialized"), bIsInitialized ? TEXT("true") : TEXT("false"));
    Stats.Add(TEXT("ValidationRulesCount"), FString::FromInt(ValidationRules.Num()));
    Stats.Add(TEXT("DisabledRulesCount"), FString::FromInt(DisabledRules.Num()));
    
    for (const auto& StatPair : ValidationStatistics)
    {
        Stats.Add(StatPair.Key, FString::FromInt(StatPair.Value));
    }
    
    return Stats;
}

void FGRAPHVALIDATOR::SetVerboseLogging(bool bEnabled)
{
    bVerboseLogging = bEnabled;
    LogValidationActivity(FString::Printf(TEXT("Verbose logging %s"), bEnabled ? TEXT("enabled") : TEXT("disabled")));
}

void FGRAPHVALIDATOR::SetTrackStatistics(bool bEnabled)
{
    bTrackStatistics = bEnabled;
    LogValidationActivity(FString::Printf(TEXT("Statistics tracking %s"), bEnabled ? TEXT("enabled") : TEXT("disabled")));
}

void FGRAPHVALIDATOR::ResetStatistics()
{
    TotalValidations = 0;
    TotalIssuesFound = 0;
    TotalAutoFixes = 0;
    LastValidationTimeMs = 0.0f;
    ValidationStatistics.Empty();
    
    LogValidationActivity(TEXT("Statistics reset"));
}

// Private Helper Methods

void FGRAPHVALIDATOR::InitializeDefaultRules()
{
    // Structure validation rules
    FVALIDATIONRULE EmptyGraphRule;
    EmptyGraphRule.RuleId = TEXT("STRUCT_001");
    EmptyGraphRule.RuleName = TEXT("Empty Graph Check");
    EmptyGraphRule.Description = TEXT("Checks for empty graphs with no nodes");
    EmptyGraphRule.Category = EValidationCategory::Structure;
    EmptyGraphRule.RuleType = EValidationRuleType::Recommended;
    EmptyGraphRule.DefaultSeverity = EValidationSeverity::Warning;
    ValidationRules.Add(EmptyGraphRule.RuleId, EmptyGraphRule);

    // Connection validation rules
    FVALIDATIONRULE InvalidConnectionRule;
    InvalidConnectionRule.RuleId = TEXT("CONN_001");
    InvalidConnectionRule.RuleName = TEXT("Invalid Connection Check");
    InvalidConnectionRule.Description = TEXT("Checks for invalid pin connections");
    InvalidConnectionRule.Category = EValidationCategory::Connections;
    InvalidConnectionRule.RuleType = EValidationRuleType::Required;
    InvalidConnectionRule.DefaultSeverity = EValidationSeverity::Error;
    ValidationRules.Add(InvalidConnectionRule.RuleId, InvalidConnectionRule);

    // Logic validation rules
    FVALIDATIONRULE UnreachableNodeRule;
    UnreachableNodeRule.RuleId = TEXT("LOGIC_001");
    UnreachableNodeRule.RuleName = TEXT("Unreachable Node Check");
    UnreachableNodeRule.Description = TEXT("Checks for nodes that are not reachable from entry points");
    UnreachableNodeRule.Category = EValidationCategory::Logic;
    UnreachableNodeRule.RuleType = EValidationRuleType::Recommended;
    UnreachableNodeRule.DefaultSeverity = EValidationSeverity::Warning;
    ValidationRules.Add(UnreachableNodeRule.RuleId, UnreachableNodeRule);

    LogValidationActivity(FString::Printf(TEXT("Initialized %d default validation rules"), ValidationRules.Num()));
}

FVALIDATIONISSUE FGRAPHVALIDATOR::CreateValidationIssue(const FString& RuleId, EValidationSeverity Severity, const FString& Title, const FString& Description, UEdGraphNode* Node, UEdGraphPin* Pin)
{
    FVALIDATIONISSUE Issue;
    Issue.RuleId = RuleId;
    Issue.Severity = Severity;
    Issue.Title = Title;
    Issue.Description = Description;
    Issue.Node = Node;
    Issue.Pin = Pin;

    // Set category and type from rule if available
    if (ValidationRules.Contains(RuleId))
    {
        const FVALIDATIONRULE& Rule = ValidationRules[RuleId];
        Issue.Category = Rule.Category;
        Issue.RuleType = Rule.RuleType;
        Issue.bCanAutoFix = Rule.bCanAutoFix;
    }

    return Issue;
}

bool FGRAPHVALIDATOR::ShouldReportIssue(const FVALIDATIONISSUE& Issue, const FVALIDATIONCONFIG& Config)
{
    // Check severity level
    if (Issue.Severity < Config.MinSeverityLevel)
    {
        return false;
    }

    // Check if rule is disabled
    if (DisabledRules.Contains(Issue.RuleId))
    {
        return false;
    }

    // Check category limits
    if (Config.MaxIssuesPerCategory > 0)
    {
        // This would require tracking per-category counts during validation
        // For now, we'll allow all issues through
    }

    return true;
}

void FGRAPHVALIDATOR::LogValidationActivity(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogGraphValidator, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogGraphValidator, Log, TEXT("%s"), *Message);
    }
}

void FGRAPHVALIDATOR::UpdateValidationStatistics(const FString& Operation, const FString& Category)
{
    if (!bTrackStatistics)
    {
        return;
    }

    FString StatKey = FString::Printf(TEXT("%s_Count"), *Operation);
    if (ValidationStatistics.Contains(StatKey))
    {
        ValidationStatistics[StatKey]++;
    }
    else
    {
        ValidationStatistics.Add(StatKey, 1);
    }

    if (!Category.IsEmpty())
    {
        FString CategoryStatKey = FString::Printf(TEXT("%s_%s_Count"), *Operation, *Category);
        if (ValidationStatistics.Contains(CategoryStatKey))
        {
            ValidationStatistics[CategoryStatKey]++;
        }
        else
        {
            ValidationStatistics.Add(CategoryStatKey, 1);
        }
    }
}

// Specific validation check implementations (simplified for brevity)

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::CheckUnconnectedPins(UEdGraph* Graph)
{
    TArray<FVALIDATIONISSUE> Issues;
    
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (!Node) continue;
        
        for (UEdGraphPin* Pin : Node->Pins)
        {
            if (Pin && Pin->LinkedTo.Num() == 0 && !Pin->bOptionalPin)
            {
                Issues.Add(CreateValidationIssue(TEXT("STRUCT_002"), EValidationSeverity::Warning, 
                    TEXT("Unconnected Pin"), TEXT("Required pin is not connected"), Node, Pin));
            }
        }
    }
    
    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::CheckCircularDependencies(UEdGraph* Graph)
{
    TArray<FVALIDATIONISSUE> Issues;
    
    // Simplified circular dependency check
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (Node && HasCircularDependency(Node, Graph))
        {
            Issues.Add(CreateValidationIssue(TEXT("STRUCT_003"), EValidationSeverity::Error, 
                TEXT("Circular Dependency"), TEXT("Node has circular dependency"), Node));
        }
    }
    
    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::CheckExecutionFlow(UEdGraph* Graph)
{
    TArray<FVALIDATIONISSUE> Issues;
    
    // Check for execution flow issues
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (!Node) continue;
        
        // Check for nodes with execution input but no execution output
        bool bHasExecInput = false;
        bool bHasExecOutput = false;
        
        for (UEdGraphPin* Pin : Node->Pins)
        {
            if (Pin && Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Exec)
            {
                if (Pin->Direction == EGPD_Input)
                {
                    bHasExecInput = true;
                }
                else if (Pin->Direction == EGPD_Output)
                {
                    bHasExecOutput = true;
                }
            }
        }
        
        if (bHasExecInput && !bHasExecOutput && !Node->IsA<UK2Node_Event>())
        {
            Issues.Add(CreateValidationIssue(TEXT("FLOW_001"), EValidationSeverity::Warning, 
                TEXT("Execution Dead End"), TEXT("Node has execution input but no execution output"), Node));
        }
    }
    
    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::CheckPerformanceIssues(UEdGraph* Graph)
{
    TArray<FVALIDATIONISSUE> Issues;
    
    // Check for performance issues like excessive node count
    if (Graph->Nodes.Num() > 100)
    {
        Issues.Add(CreateValidationIssue(TEXT("PERF_001"), EValidationSeverity::Info, 
            TEXT("Large Graph"), TEXT("Graph has many nodes which may impact performance")));
    }
    
    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::CheckNodeNaming(UEdGraph* Graph)
{
    TArray<FVALIDATIONISSUE> Issues;
    
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (Node && Node->GetNodeTitle(ENodeTitleType::ListView).ToString().IsEmpty())
        {
            Issues.Add(CreateValidationIssue(TEXT("STYLE_001"), EValidationSeverity::Info, 
                TEXT("Unnamed Node"), TEXT("Node has no descriptive name"), Node));
        }
    }
    
    return Issues;
}

TArray<FVALIDATIONISSUE> FGRAPHVALIDATOR::CheckCompilationRequirements(UEdGraph* Graph)
{
    TArray<FVALIDATIONISSUE> Issues;
    
    // Basic compilation requirement checks
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (Node && !Node->IsNodeEnabled())
        {
            Issues.Add(CreateValidationIssue(TEXT("COMP_001"), EValidationSeverity::Warning, 
                TEXT("Disabled Node"), TEXT("Node is disabled and will not execute"), Node));
        }
    }
    
    return Issues;
}

void FGRAPHVALIDATOR::CalculateQualityMetrics(UEdGraph* Graph, FVALIDATIONRESULT& Result)
{
    if (!Graph)
    {
        return;
    }

    // Calculate quality score based on issues
    float QualityPenalty = 0.0f;
    QualityPenalty += Result.IssueCountBySeverity[EValidationSeverity::Critical] * 0.5f;
    QualityPenalty += Result.IssueCountBySeverity[EValidationSeverity::Error] * 0.3f;
    QualityPenalty += Result.IssueCountBySeverity[EValidationSeverity::Warning] * 0.1f;
    
    Result.QualityScore = FMath::Clamp(1.0f - (QualityPenalty / FMath::Max(1.0f, (float)Graph->Nodes.Num())), 0.0f, 1.0f);
    
    // Calculate performance score
    float PerformancePenalty = 0.0f;
    PerformancePenalty += FMath::Max(0.0f, (Graph->Nodes.Num() - 50) * 0.01f); // Penalty for large graphs
    
    Result.PerformanceScore = FMath::Clamp(1.0f - PerformancePenalty, 0.0f, 1.0f);
    
    // Calculate compliance score
    int32 ComplianceIssues = Result.IssueCountByCategory[EValidationCategory::Compliance];
    Result.ComplianceScore = FMath::Clamp(1.0f - (ComplianceIssues * 0.2f), 0.0f, 1.0f);
}

// Helper methods for graph traversal

void FGRAPHVALIDATOR::TraceReachableNodes(UEdGraphNode* StartNode, TSet<UEdGraphNode*>& ReachableNodes, TSet<UEdGraphNode*>& Visited)
{
    if (!StartNode || Visited.Contains(StartNode))
    {
        return;
    }

    Visited.Add(StartNode);
    ReachableNodes.Add(StartNode);

    // Follow execution pins
    for (UEdGraphPin* Pin : StartNode->Pins)
    {
        if (Pin && Pin->Direction == EGPD_Output && Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Exec)
        {
            for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
            {
                if (LinkedPin && LinkedPin->GetOwningNode())
                {
                    TraceReachableNodes(LinkedPin->GetOwningNode(), ReachableNodes, Visited);
                }
            }
        }
    }
}

bool FGRAPHVALIDATOR::HasCircularDependency(UEdGraphNode* Node, UEdGraph* Graph)
{
    TSet<UEdGraphNode*> Visited;
    TSet<UEdGraphNode*> RecursionStack;
    
    return CheckCircularDependencyRecursive(Node, Visited, RecursionStack);
}

bool FGRAPHVALIDATOR::CheckCircularDependencyRecursive(UEdGraphNode* Node, TSet<UEdGraphNode*>& Visited, TSet<UEdGraphNode*>& RecursionStack)
{
    if (!Node)
    {
        return false;
    }

    if (RecursionStack.Contains(Node))
    {
        return true; // Circular dependency found
    }

    if (Visited.Contains(Node))
    {
        return false; // Already processed
    }

    Visited.Add(Node);
    RecursionStack.Add(Node);

    // Check all connected nodes
    for (UEdGraphPin* Pin : Node->Pins)
    {
        if (Pin && Pin->Direction == EGPD_Output)
        {
            for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
            {
                if (LinkedPin && LinkedPin->GetOwningNode())
                {
                    if (CheckCircularDependencyRecursive(LinkedPin->GetOwningNode(), Visited, RecursionStack))
                    {
                        return true;
                    }
                }
            }
        }
    }

    RecursionStack.Remove(Node);
    return false;
} 