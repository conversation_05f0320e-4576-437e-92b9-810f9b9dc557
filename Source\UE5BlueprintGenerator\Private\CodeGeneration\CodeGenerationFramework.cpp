#include "CodeGeneration/CodeGenerationFramework.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "NLP/StructureExtractor.h"

DEFINE_LOG_CATEGORY(LogCodeGenerationFramework);

// Static instance for singleton
TSharedPtr<FCodeGenerationFramework> FCodeGenerationFramework::Instance = nullptr;

FCodeGenerationFramework::FCodeGenerationFramework()
    : bIsGenerationInProgress(false)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Initializing Code Generation Framework"));
    
    // Initialize default configuration
    Config.bEnableParallelGeneration = true;
    Config.bEnableCodeValidation = true;
    Config.bEnableAutoFormatting = true;
    Config.bEnableFileBackup = true;
    Config.MaxGenerationTime = 300.0f;
    Config.MaxFileSizeMB = 10.0f;
    Config.IndentationStyle = TEXT("Tabs");
    Config.LineEndingStyle = TEXT("CRLF");
    Config.MaxLineLength = 120;
    
    // Initialize statistics
    Statistics = FCodeGenerationStatistics();
    
    InitializeComponents();
}

FCodeGenerationFramework::~FCodeGenerationFramework()
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Shutting down Code Generation Framework"));
    ShutdownComponents();
}

FCodeGenerationFramework& FCodeGenerationFramework::Get()
{
    if (!Instance.IsValid())
    {
        Instance = MakeShared<FCodeGenerationFramework>();
    }
    return *Instance;
}

bool FCodeGenerationFramework::GenerateCode(const FCodeGenerationRequest& Request, FCodeGenerationResult& OutResult)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Starting code generation for blueprint: %s"), 
           Request.SourceBlueprint ? *Request.SourceBlueprint->GetName() : TEXT("Unknown"));
    
    // Reset result
    OutResult = FCodeGenerationResult();
    
    // Validate request
    TArray<FString> ValidationErrors;
    if (!ValidateGenerationRequest(Request, ValidationErrors))
    {
        OutResult.bSuccess = false;
        OutResult.Errors = ValidationErrors;
        BroadcastGenerationError(TEXT("Request validation failed"), Request);
        return false;
    }
    
    // Check if generation is already in progress
    if (bIsGenerationInProgress)
    {
        OutResult.bSuccess = false;
        OutResult.Errors.Add(TEXT("Code generation is already in progress"));
        BroadcastGenerationError(TEXT("Generation already in progress"), Request);
        return false;
    }
    
    // Initialize generation
    if (!InitializeGeneration(Request))
    {
        OutResult.bSuccess = false;
        OutResult.Errors.Add(TEXT("Failed to initialize code generation"));
        BroadcastGenerationError(TEXT("Initialization failed"), Request);
        return false;
    }
    
    // Execute generation
    bool bSuccess = ExecuteGeneration(Request, OutResult);
    
    // Finalize generation
    FinalizeGeneration(OutResult);
    
    // Update statistics
    UpdateStatistics(OutResult);
    
    // Broadcast completion
    if (bSuccess)
    {
        BroadcastGenerationCompleted(OutResult);
    }
    else
    {
        BroadcastGenerationError(TEXT("Code generation failed"), Request);
    }
    
    return bSuccess;
}

bool FCodeGenerationFramework::IsGenerationInProgress() const
{
    return bIsGenerationInProgress;
}

void FCodeGenerationFramework::CancelGeneration()
{
    if (bIsGenerationInProgress)
    {
        UE_LOG(LogCodeGenerationFramework, Warning, TEXT("Cancelling code generation"));
        bIsGenerationInProgress = false;
        BroadcastGenerationError(TEXT("Generation cancelled by user"), CurrentRequest);
    }
}

void FCodeGenerationFramework::SetGenerationConfig(const FCodeGenerationConfig& InConfig)
{
    Config = InConfig;
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Updated code generation configuration"));
}

FCodeGenerationConfig FCodeGenerationFramework::GetGenerationConfig() const
{
    return Config;
}

FCodeGenerationStatistics FCodeGenerationFramework::GetStatistics() const
{
    return Statistics;
}

void FCodeGenerationFramework::ResetStatistics()
{
    Statistics = FCodeGenerationStatistics();
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Reset code generation statistics"));
}

bool FCodeGenerationFramework::RegisterCodeGenerator(ECodeGenerationTarget Target, TSharedPtr<ICodeGenerator> Generator)
{
    if (!Generator.IsValid())
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Cannot register null code generator"));
        return false;
    }
    
    if (CodeGenerators.Contains(Target))
    {
        UE_LOG(LogCodeGenerationFramework, Warning, TEXT("Replacing existing code generator for target: %d"), (int32)Target);
    }
    
    CodeGenerators.Add(Target, Generator);
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Registered code generator: %s for target: %d"), 
           *Generator->GetGeneratorName(), (int32)Target);
    
    return true;
}

bool FCodeGenerationFramework::UnregisterCodeGenerator(ECodeGenerationTarget Target)
{
    if (CodeGenerators.Contains(Target))
    {
        CodeGenerators.Remove(Target);
        UE_LOG(LogCodeGenerationFramework, Log, TEXT("Unregistered code generator for target: %d"), (int32)Target);
        return true;
    }
    
    UE_LOG(LogCodeGenerationFramework, Warning, TEXT("No code generator found for target: %d"), (int32)Target);
    return false;
}

TArray<ECodeGenerationTarget> FCodeGenerationFramework::GetSupportedTargets() const
{
    TArray<ECodeGenerationTarget> SupportedTargets;
    CodeGenerators.GetKeys(SupportedTargets);
    return SupportedTargets;
}

bool FCodeGenerationFramework::ValidateGenerationRequest(const FCodeGenerationRequest& Request, TArray<FString>& OutValidationErrors)
{
    OutValidationErrors.Empty();
    
    // Validate source blueprint
    if (!Request.SourceBlueprint)
    {
        OutValidationErrors.Add(TEXT("Source blueprint is null"));
    }
    else if (!ValidateBlueprint(Request.SourceBlueprint, OutValidationErrors))
    {
        // Errors already added by ValidateBlueprint
    }
    
    // Validate target
    if (Request.Target == ECodeGenerationTarget::None)
    {
        OutValidationErrors.Add(TEXT("Invalid code generation target"));
    }
    else if (!CodeGenerators.Contains(Request.Target))
    {
        OutValidationErrors.Add(FString::Printf(TEXT("No generator registered for target: %d"), (int32)Request.Target));
    }
    
    // Validate output directory
    if (Request.OutputDirectory.IsEmpty())
    {
        OutValidationErrors.Add(TEXT("Output directory is empty"));
    }
    else if (!ValidateOutputDirectory(Request.OutputDirectory, OutValidationErrors))
    {
        // Errors already added by ValidateOutputDirectory
    }
    
    // Validate filename
    if (Request.BaseFilename.IsEmpty())
    {
        OutValidationErrors.Add(TEXT("Base filename is empty"));
    }
    else if (!ValidateFilename(Request.BaseFilename, OutValidationErrors))
    {
        // Errors already added by ValidateFilename
    }
    
    return OutValidationErrors.Num() == 0;
}

bool FCodeGenerationFramework::ValidateGeneratedCode(const FGeneratedCodeFile& CodeFile, TArray<FString>& OutValidationErrors)
{
    OutValidationErrors.Empty();
    
    // Validate file path
    if (CodeFile.FullFilePath.IsEmpty())
    {
        OutValidationErrors.Add(TEXT("Generated file path is empty"));
    }
    
    // Validate content
    if (CodeFile.Content.IsEmpty())
    {
        OutValidationErrors.Add(TEXT("Generated file content is empty"));
    }
    
    // Validate file size
    if (CodeFile.FileSizeBytes > Config.MaxFileSizeMB * 1024 * 1024)
    {
        OutValidationErrors.Add(FString::Printf(TEXT("Generated file size (%d bytes) exceeds maximum (%f MB)"), 
                                               CodeFile.FileSizeBytes, Config.MaxFileSizeMB));
    }
    
    return OutValidationErrors.Num() == 0;
}

bool FCodeGenerationFramework::SaveGeneratedFiles(const TArray<FGeneratedCodeFile>& Files, const FString& OutputDirectory)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Saving %d generated files to: %s"), Files.Num(), *OutputDirectory);
    
    // Create output directory if it doesn't exist
    if (!CreateOutputDirectory(OutputDirectory))
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Failed to create output directory: %s"), *OutputDirectory);
        return false;
    }
    
    // Backup existing files if enabled
    if (Config.bEnableFileBackup)
    {
        TArray<FString> ExistingFilePaths;
        for (const FGeneratedCodeFile& File : Files)
        {
            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*File.FullFilePath))
            {
                ExistingFilePaths.Add(File.FullFilePath);
            }
        }
        
        if (ExistingFilePaths.Num() > 0)
        {
            FString BackupDirectory = FPaths::Combine(OutputDirectory, TEXT("Backup"), FDateTime::Now().ToString(TEXT("%Y%m%d_%H%M%S")));
            if (!BackupExistingFiles(ExistingFilePaths, BackupDirectory))
            {
                UE_LOG(LogCodeGenerationFramework, Warning, TEXT("Failed to backup existing files"));
            }
        }
    }
    
    // Save each file
    int32 SavedFiles = 0;
    for (const FGeneratedCodeFile& File : Files)
    {
        if (WriteFileContent(File.FullFilePath, File.Content))
        {
            SavedFiles++;
            UE_LOG(LogCodeGenerationFramework, Verbose, TEXT("Saved file: %s"), *File.FullFilePath);
        }
        else
        {
            UE_LOG(LogCodeGenerationFramework, Error, TEXT("Failed to save file: %s"), *File.FullFilePath);
        }
    }
    
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Successfully saved %d of %d files"), SavedFiles, Files.Num());
    return SavedFiles == Files.Num();
}

bool FCodeGenerationFramework::BackupExistingFiles(const TArray<FString>& FilePaths, const FString& BackupDirectory)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Backing up %d files to: %s"), FilePaths.Num(), *BackupDirectory);
    
    // Create backup directory
    if (!CreateOutputDirectory(BackupDirectory))
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Failed to create backup directory: %s"), *BackupDirectory);
        return false;
    }
    
    // Copy each file
    int32 BackedUpFiles = 0;
    for (const FString& FilePath : FilePaths)
    {
        FString Filename = FPaths::GetCleanFilename(FilePath);
        FString BackupFilePath = FPaths::Combine(BackupDirectory, Filename);
        
        if (FPlatformFileManager::Get().GetPlatformFile().CopyFile(*BackupFilePath, *FilePath))
        {
            BackedUpFiles++;
            UE_LOG(LogCodeGenerationFramework, Verbose, TEXT("Backed up file: %s"), *FilePath);
        }
        else
        {
            UE_LOG(LogCodeGenerationFramework, Error, TEXT("Failed to backup file: %s"), *FilePath);
        }
    }
    
    return BackedUpFiles == FilePaths.Num();
}

bool FCodeGenerationFramework::FormatCodeFile(FGeneratedCodeFile& CodeFile)
{
    if (!Config.bEnableAutoFormatting)
    {
        return true;
    }
    
    UE_LOG(LogCodeGenerationFramework, Verbose, TEXT("Formatting code file: %s"), *CodeFile.RelativeFilePath);
    
    // Apply basic formatting
    FString FormattedContent = CodeFile.Content;
    
    // Normalize line endings
    if (Config.LineEndingStyle == TEXT("CRLF"))
    {
        FormattedContent = FormattedContent.Replace(TEXT("\n"), TEXT("\r\n"));
    }
    else if (Config.LineEndingStyle == TEXT("LF"))
    {
        FormattedContent = FormattedContent.Replace(TEXT("\r\n"), TEXT("\n"));
        FormattedContent = FormattedContent.Replace(TEXT("\r"), TEXT("\n"));
    }
    
    // Apply custom formatting rules
    for (const auto& Rule : Config.FormattingRules)
    {
        FormattedContent = FormattedContent.Replace(*Rule.Key, *Rule.Value);
    }
    
    // Update file content and metrics
    CodeFile.Content = FormattedContent;
    CodeFile.FileSizeBytes = FormattedContent.Len();
    CodeFile.LineCount = FormattedContent.Split(TEXT("\n")).Num();
    
    return true;
}

bool FCodeGenerationFramework::InitializeGeneration(const FCodeGenerationRequest& Request)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Initializing code generation"));
    
    bIsGenerationInProgress = true;
    GenerationStartTime = FDateTime::Now();
    CurrentRequest = Request;
    
    BroadcastGenerationStarted(Request);
    BroadcastGenerationProgress(0.0f, TEXT("Initializing generation"));
    
    return true;
}

bool FCodeGenerationFramework::ExecuteGeneration(const FCodeGenerationRequest& Request, FCodeGenerationResult& OutResult)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Executing code generation"));
    
    FDateTime StartTime = FDateTime::Now();
    
    // Preprocess blueprint
    BroadcastGenerationProgress(0.1f, TEXT("Preprocessing blueprint"));
    if (!PreprocessBlueprint(Request.SourceBlueprint))
    {
        OutResult.Errors.Add(TEXT("Failed to preprocess blueprint"));
        return false;
    }
    
    // Generate code for target
    BroadcastGenerationProgress(0.3f, TEXT("Generating code"));
    TArray<FGeneratedCodeFile> GeneratedFiles;
    if (!GenerateForTarget(Request, Request.Target, GeneratedFiles))
    {
        OutResult.Errors.Add(TEXT("Failed to generate code for target"));
        return false;
    }
    
    // Postprocess generated files
    BroadcastGenerationProgress(0.7f, TEXT("Postprocessing files"));
    if (!PostprocessGeneratedFiles(GeneratedFiles))
    {
        OutResult.Warnings.Add(TEXT("Some postprocessing operations failed"));
    }
    
    // Validate generated code
    if (Config.bEnableCodeValidation)
    {
        BroadcastGenerationProgress(0.8f, TEXT("Validating generated code"));
        for (const FGeneratedCodeFile& File : GeneratedFiles)
        {
            TArray<FString> ValidationErrors;
            if (!ValidateGeneratedCode(File, ValidationErrors))
            {
                OutResult.Warnings.Append(ValidationErrors);
            }
        }
    }
    
    // Save files
    BroadcastGenerationProgress(0.9f, TEXT("Saving files"));
    if (!SaveGeneratedFiles(GeneratedFiles, Request.OutputDirectory))
    {
        OutResult.Errors.Add(TEXT("Failed to save some generated files"));
    }
    
    // Calculate results
    OutResult.bSuccess = OutResult.Errors.Num() == 0;
    OutResult.GeneratedFiles = GeneratedFiles;
    OutResult.GenerationTime = (FDateTime::Now() - StartTime).GetTotalSeconds();
    OutResult.TotalFilesGenerated = GeneratedFiles.Num();
    
    // Calculate total lines generated
    for (const FGeneratedCodeFile& File : GeneratedFiles)
    {
        OutResult.TotalLinesGenerated += File.LineCount;
    }
    
    // Calculate quality score
    CalculateQualityScore(OutResult);
    
    BroadcastGenerationProgress(1.0f, TEXT("Generation complete"));
    
    return OutResult.bSuccess;
}

bool FCodeGenerationFramework::FinalizeGeneration(const FCodeGenerationResult& Result)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Finalizing code generation"));
    
    bIsGenerationInProgress = false;
    
    if (Result.bSuccess)
    {
        UE_LOG(LogCodeGenerationFramework, Log, TEXT("Code generation completed successfully"));
        UE_LOG(LogCodeGenerationFramework, Log, TEXT("Generated %d files with %d total lines in %.2f seconds"), 
               Result.TotalFilesGenerated, Result.TotalLinesGenerated, Result.GenerationTime);
    }
    else
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Code generation failed with %d errors"), Result.Errors.Num());
    }
    
    return true;
}

bool FCodeGenerationFramework::AnalyzeBlueprint(UBlueprint* Blueprint, FExtractedBlueprintStructure& OutStructure)
{
    if (!Blueprint)
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Cannot analyze null blueprint"));
        return false;
    }
    
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Analyzing blueprint: %s"), *Blueprint->GetName());
    
    // Use structure extractor to analyze the blueprint
    FStructureExtractor StructureExtractor;
    
    // For now, create a basic structure
    // In a real implementation, this would analyze the blueprint's graphs, nodes, variables, etc.
    OutStructure = FExtractedBlueprintStructure();
    
    return true;
}

bool FCodeGenerationFramework::ExtractBlueprintMetadata(UBlueprint* Blueprint, TMap<FString, FString>& OutMetadata)
{
    if (!Blueprint)
    {
        return false;
    }
    
    OutMetadata.Empty();
    
    // Extract basic metadata
    OutMetadata.Add(TEXT("Name"), Blueprint->GetName());
    OutMetadata.Add(TEXT("Class"), Blueprint->GetClass()->GetName());
    OutMetadata.Add(TEXT("Package"), Blueprint->GetPackage()->GetName());
    
    // Extract parent class information
    if (Blueprint->ParentClass)
    {
        OutMetadata.Add(TEXT("ParentClass"), Blueprint->ParentClass->GetName());
    }
    
    // Extract creation date if available
    OutMetadata.Add(TEXT("AnalysisDate"), FDateTime::Now().ToString());
    
    return true;
}

bool FCodeGenerationFramework::PreprocessBlueprint(UBlueprint* Blueprint)
{
    if (!Blueprint)
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Cannot preprocess null blueprint"));
        return false;
    }
    
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Preprocessing blueprint: %s"), *Blueprint->GetName());
    
    // Analyze blueprint structure
    FExtractedBlueprintStructure Structure;
    if (!AnalyzeBlueprint(Blueprint, Structure))
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Failed to analyze blueprint structure"));
        return false;
    }
    
    // Extract metadata
    TMap<FString, FString> Metadata;
    if (!ExtractBlueprintMetadata(Blueprint, Metadata))
    {
        UE_LOG(LogCodeGenerationFramework, Warning, TEXT("Failed to extract blueprint metadata"));
    }
    
    return true;
}

bool FCodeGenerationFramework::GenerateForTarget(const FCodeGenerationRequest& Request, ECodeGenerationTarget Target, TArray<FGeneratedCodeFile>& OutFiles)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Generating code for target: %d"), (int32)Target);
    
    // Find registered generator for target
    TSharedPtr<ICodeGenerator>* GeneratorPtr = CodeGenerators.Find(Target);
    if (!GeneratorPtr || !GeneratorPtr->IsValid())
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("No generator found for target: %d"), (int32)Target);
        return false;
    }
    
    TSharedPtr<ICodeGenerator> Generator = *GeneratorPtr;
    
    // Validate request with generator
    TArray<FString> GeneratorErrors;
    if (!Generator->ValidateRequest(Request, GeneratorErrors))
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Generator validation failed for target: %d"), (int32)Target);
        for (const FString& Error : GeneratorErrors)
        {
            UE_LOG(LogCodeGenerationFramework, Error, TEXT("Generator Error: %s"), *Error);
        }
        return false;
    }
    
    // Generate code
    if (!Generator->GenerateCode(Request, OutFiles))
    {
        UE_LOG(LogCodeGenerationFramework, Error, TEXT("Code generation failed for target: %d"), (int32)Target);
        return false;
    }
    
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Generated %d files for target: %d"), OutFiles.Num(), (int32)Target);
    return true;
}

bool FCodeGenerationFramework::PostprocessGeneratedFiles(TArray<FGeneratedCodeFile>& Files)
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Postprocessing %d generated files"), Files.Num());
    
    bool bAllSuccessful = true;
    
    for (FGeneratedCodeFile& File : Files)
    {
        // Format file if enabled
        if (!FormatCodeFile(File))
        {
            UE_LOG(LogCodeGenerationFramework, Warning, TEXT("Failed to format file: %s"), *File.RelativeFilePath);
            bAllSuccessful = false;
        }
        
        // Update file metrics
        File.FileSizeBytes = File.Content.Len();
        File.LineCount = File.Content.Split(TEXT("\n")).Num();
        File.CreationTime = FDateTime::Now();
    }
    
    return bAllSuccessful;
}

bool FCodeGenerationFramework::ValidateBlueprint(UBlueprint* Blueprint, TArray<FString>& OutErrors)
{
    if (!Blueprint)
    {
        OutErrors.Add(TEXT("Blueprint is null"));
        return false;
    }
    
    // Check if blueprint is valid
    if (!IsValid(Blueprint))
    {
        OutErrors.Add(TEXT("Blueprint is not valid"));
        return false;
    }
    
    // Check if blueprint has a valid class
    if (!Blueprint->GeneratedClass)
    {
        OutErrors.Add(TEXT("Blueprint has no generated class"));
        return false;
    }
    
    return OutErrors.Num() == 0;
}

bool FCodeGenerationFramework::ValidateOutputDirectory(const FString& Directory, TArray<FString>& OutErrors)
{
    if (Directory.IsEmpty())
    {
        OutErrors.Add(TEXT("Output directory is empty"));
        return false;
    }
    
    // Check for invalid characters
    if (Directory.Contains(TEXT("*")) || Directory.Contains(TEXT("?")) || Directory.Contains(TEXT("<")) || Directory.Contains(TEXT(">")))
    {
        OutErrors.Add(TEXT("Output directory contains invalid characters"));
        return false;
    }
    
    return OutErrors.Num() == 0;
}

bool FCodeGenerationFramework::ValidateFilename(const FString& Filename, TArray<FString>& OutErrors)
{
    if (Filename.IsEmpty())
    {
        OutErrors.Add(TEXT("Filename is empty"));
        return false;
    }
    
    // Check for invalid characters
    if (Filename.Contains(TEXT("/")) || Filename.Contains(TEXT("\\")) || Filename.Contains(TEXT(":")) || 
        Filename.Contains(TEXT("*")) || Filename.Contains(TEXT("?")) || Filename.Contains(TEXT("<")) || 
        Filename.Contains(TEXT(">")) || Filename.Contains(TEXT("|")))
    {
        OutErrors.Add(TEXT("Filename contains invalid characters"));
        return false;
    }
    
    return OutErrors.Num() == 0;
}

bool FCodeGenerationFramework::CreateOutputDirectory(const FString& Directory)
{
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (PlatformFile.DirectoryExists(*Directory))
    {
        return true;
    }
    
    return PlatformFile.CreateDirectoryTree(*Directory);
}

bool FCodeGenerationFramework::GenerateUniqueFilename(const FString& BaseFilename, const FString& Extension, const FString& Directory, FString& OutFilename)
{
    FString TestFilename = BaseFilename + Extension;
    FString TestPath = FPaths::Combine(Directory, TestFilename);
    
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*TestPath))
    {
        OutFilename = TestFilename;
        return true;
    }
    
    // Generate unique filename with counter
    for (int32 Counter = 1; Counter < 1000; Counter++)
    {
        TestFilename = FString::Printf(TEXT("%s_%d%s"), *BaseFilename, Counter, *Extension);
        TestPath = FPaths::Combine(Directory, TestFilename);
        
        if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*TestPath))
        {
            OutFilename = TestFilename;
            return true;
        }
    }
    
    UE_LOG(LogCodeGenerationFramework, Error, TEXT("Failed to generate unique filename for: %s"), *BaseFilename);
    return false;
}

bool FCodeGenerationFramework::WriteFileContent(const FString& FilePath, const FString& Content)
{
    return FFileHelper::SaveStringToFile(Content, *FilePath);
}

void FCodeGenerationFramework::UpdateStatistics(const FCodeGenerationResult& Result)
{
    Statistics.TotalGenerations++;
    
    if (Result.bSuccess)
    {
        Statistics.SuccessfulGenerations++;
    }
    else
    {
        Statistics.FailedGenerations++;
    }
    
    // Update timing statistics
    if (Statistics.TotalGenerations == 1)
    {
        Statistics.FastestGenerationTime = Result.GenerationTime;
        Statistics.SlowestGenerationTime = Result.GenerationTime;
        Statistics.AverageGenerationTime = Result.GenerationTime;
    }
    else
    {
        Statistics.FastestGenerationTime = FMath::Min(Statistics.FastestGenerationTime, Result.GenerationTime);
        Statistics.SlowestGenerationTime = FMath::Max(Statistics.SlowestGenerationTime, Result.GenerationTime);
        
        // Update average
        float TotalTime = Statistics.AverageGenerationTime * (Statistics.TotalGenerations - 1) + Result.GenerationTime;
        Statistics.AverageGenerationTime = TotalTime / Statistics.TotalGenerations;
    }
    
    // Update generation statistics
    Statistics.TotalLinesGenerated += Result.TotalLinesGenerated;
    Statistics.TotalFilesGenerated += Result.TotalFilesGenerated;
    
    // Update quality score
    if (Statistics.TotalGenerations == 1)
    {
        Statistics.AverageQualityScore = Result.QualityScore;
    }
    else
    {
        float TotalQuality = Statistics.AverageQualityScore * (Statistics.TotalGenerations - 1) + Result.QualityScore;
        Statistics.AverageQualityScore = TotalQuality / Statistics.TotalGenerations;
    }
    
    // Update most used target and mode
    Statistics.MostUsedTarget = CurrentRequest.Target;
    Statistics.MostUsedMode = CurrentRequest.Mode;
}

void FCodeGenerationFramework::CalculateQualityScore(FCodeGenerationResult& Result)
{
    float QualityScore = 1.0f;
    
    // Reduce score for errors
    if (Result.Errors.Num() > 0)
    {
        QualityScore -= 0.5f;
    }
    
    // Reduce score for warnings
    if (Result.Warnings.Num() > 0)
    {
        QualityScore -= 0.2f * FMath::Min(Result.Warnings.Num() / 10.0f, 1.0f);
    }
    
    // Bonus for having documentation
    if (CurrentRequest.bIncludeDocumentation)
    {
        QualityScore += 0.1f;
    }
    
    // Bonus for having comments
    if (CurrentRequest.bIncludeComments)
    {
        QualityScore += 0.1f;
    }
    
    // Ensure score is within valid range
    Result.QualityScore = FMath::Clamp(QualityScore, 0.0f, 1.0f);
}

void FCodeGenerationFramework::BroadcastGenerationStarted(const FCodeGenerationRequest& Request)
{
    OnCodeGenerationStarted.Broadcast(Request);
}

void FCodeGenerationFramework::BroadcastGenerationProgress(float Progress, const FString& CurrentTask)
{
    OnCodeGenerationProgress.Broadcast(Progress, CurrentTask);
}

void FCodeGenerationFramework::BroadcastGenerationCompleted(const FCodeGenerationResult& Result)
{
    OnCodeGenerationCompleted.Broadcast(Result);
}

void FCodeGenerationFramework::BroadcastGenerationError(const FString& ErrorMessage, const FCodeGenerationRequest& Request)
{
    OnCodeGenerationError.Broadcast(ErrorMessage, Request);
}

void FCodeGenerationFramework::InitializeComponents()
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Initializing code generation components"));
    
    // Register built-in generators
    RegisterBuiltInGenerators();
}

void FCodeGenerationFramework::ShutdownComponents()
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Shutting down code generation components"));
    
    // Clear registered generators
    CodeGenerators.Empty();
}

void FCodeGenerationFramework::RegisterBuiltInGenerators()
{
    UE_LOG(LogCodeGenerationFramework, Log, TEXT("Registering built-in code generators"));
    
    // Built-in generators will be registered here
    // For now, we'll implement them in separate tasks
} 