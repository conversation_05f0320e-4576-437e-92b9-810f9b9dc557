#include "BlueprintGeneration/ConnectionAssemblySystem.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node.h"

DEFINE_LOG_CATEGORY(LogConnectionAssemblySystem);

FConnectionAssemblySystem::FConnectionAssemblySystem()
{
    InitializeComponents();
    InitializeBuiltInOptimizationRules();
    
    // Initialize default configuration
    Config.bEnableValidation = true;
    Config.bEnableOptimization = true;
    Config.bEnableConversionNodes = true;
    Config.bEnablePinPromotion = true;
    Config.bEnableBatchProcessing = true;
    Config.bEnableErrorRecovery = true;
    Config.bEnablePerformanceMonitoring = true;
    Config.bEnableStatistics = true;
    Config.DefaultStrategy = EConnectionAssemblyStrategy::Optimized;
    Config.DefaultValidationLevel = EConnectionValidationLevel::Basic;
    Config.DefaultOptimizationMode = EConnectionOptimizationMode::Balanced;
    Config.AssemblyTimeout = 30.0f;
    Config.MaxRetryAttempts = 3;
    Config.RetryDelay = 1.0f;
    Config.MaxBatchSize = 100;
    Config.MinQualityThreshold = 0.5f;
}

FConnectionAssemblySystem::~FConnectionAssemblySystem()
{
    ShutdownComponents();
}

bool FConnectionAssemblySystem::AssembleConnection(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, FConnectionAssemblyResult& OutResult)
{
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Assembling connection: %s.%s -> %s.%s"), 
           *Instruction.SourceNodeId, *Instruction.SourcePinName, 
           *Instruction.TargetNodeId, *Instruction.TargetPinName);

    StartAssemblyTimer(Instruction.InstructionId);
    BroadcastAssemblyStarted(Instruction.InstructionId);

    OutResult.InstructionId = Instruction.InstructionId;
    OutResult.SourceNodeId = Instruction.SourceNodeId;
    OutResult.SourcePinName = Instruction.SourcePinName;
    OutResult.TargetNodeId = Instruction.TargetNodeId;
    OutResult.TargetPinName = Instruction.TargetPinName;
    OutResult.bSuccess = false;

    // Execute connection assembly
    bool bSuccess = ExecuteConnectionAssembly(Instruction, Context, OutResult);

    StopAssemblyTimer(Instruction.InstructionId, OutResult);
    UpdateStatistics(OutResult);

    if (bSuccess)
    {
        BroadcastAssemblyCompleted(OutResult);
        UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Successfully assembled connection"));
    }
    else
    {
        BroadcastAssemblyFailed(Instruction.InstructionId, OutResult.ErrorMessage);
        UE_LOG(LogConnectionAssemblySystem, Error, TEXT("Failed to assemble connection: %s"), *OutResult.ErrorMessage);
    }

    return bSuccess;
}

bool FConnectionAssemblySystem::AssembleConnections(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context, TArray<FConnectionAssemblyResult>& OutResults)
{
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Assembling %d connections"), Instructions.Num());

    OutResults.Empty();
    OutResults.SetNum(Instructions.Num());

    // Select optimal strategy
    EConnectionAssemblyStrategy Strategy = SelectOptimalStrategy(Instructions, Context);

    // Execute based on strategy
    switch (Strategy)
    {
        case EConnectionAssemblyStrategy::Sequential:
            return ExecuteSequentialStrategy(Instructions, Context, OutResults);
        case EConnectionAssemblyStrategy::Parallel:
            return ExecuteParallelStrategy(Instructions, Context, OutResults);
        case EConnectionAssemblyStrategy::Optimized:
            return ExecuteOptimizedStrategy(Instructions, Context, OutResults);
        case EConnectionAssemblyStrategy::TypeGrouped:
            return ExecuteTypeGroupedStrategy(Instructions, Context, OutResults);
        case EConnectionAssemblyStrategy::PriorityBased:
            return ExecutePriorityBasedStrategy(Instructions, Context, OutResults);
        case EConnectionAssemblyStrategy::DependencyOrder:
            return ExecuteDependencyOrderStrategy(Instructions, Context, OutResults);
        default:
            return ExecuteSequentialStrategy(Instructions, Context, OutResults);
    }
}

bool FConnectionAssemblySystem::ValidateConnection(const FString& SourceNodeId, const FString& SourcePinName, const FString& TargetNodeId, const FString& TargetPinName, FConnectionAssemblyContext& Context, TArray<FString>& OutValidationMessages)
{
    if (!Config.bEnableValidation)
    {
        return true;
    }

    OutValidationMessages.Empty();

    // Check if nodes exist
    if (!Context.AvailableNodes.Contains(SourceNodeId))
    {
        OutValidationMessages.Add(FString::Printf(TEXT("Source node not found: %s"), *SourceNodeId));
        return false;
    }

    if (!Context.AvailableNodes.Contains(TargetNodeId))
    {
        OutValidationMessages.Add(FString::Printf(TEXT("Target node not found: %s"), *TargetNodeId));
        return false;
    }

    // Use compatibility checker for detailed validation
    if (CompatibilityChecker.IsValid())
    {
        UK2Node* SourceNode = Context.AvailableNodes[SourceNodeId];
        UK2Node* TargetNode = Context.AvailableNodes[TargetNodeId];
        
        // In a full implementation, this would check pin compatibility
        // For now, we'll do basic validation
        if (!SourceNode || !TargetNode)
        {
            OutValidationMessages.Add(TEXT("Invalid node references"));
            return false;
        }
    }

    return true;
}

float FConnectionAssemblySystem::CalculateConnectionQuality(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context)
{
    float QualityScore = 0.0f;

    // Calculate compatibility score (40% weight)
    float CompatibilityScore = CalculateCompatibilityScore(Instruction, Context);
    QualityScore += CompatibilityScore * 0.4f;

    // Calculate performance score (30% weight)
    float PerformanceScore = CalculatePerformanceScore(Instruction, Context);
    QualityScore += PerformanceScore * 0.3f;

    // Calculate readability score (20% weight)
    float ReadabilityScore = CalculateReadabilityScore(Instruction, Context);
    QualityScore += ReadabilityScore * 0.2f;

    // Calculate maintenance score (10% weight)
    float MaintenanceScore = CalculateMaintenanceScore(Instruction, Context);
    QualityScore += MaintenanceScore * 0.1f;

    return FMath::Clamp(QualityScore, 0.0f, 1.0f);
}

void FConnectionAssemblySystem::SetAssemblyConfig(const FConnectionAssemblyConfig& NewConfig)
{
    Config = NewConfig;
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Connection assembly configuration updated"));
}

FConnectionAssemblyConfig FConnectionAssemblySystem::GetAssemblyConfig() const
{
    return Config;
}

FConnectionAssemblyContext FConnectionAssemblySystem::CreateAssemblyContext(UBlueprint* Blueprint, UEdGraph* Graph, const TMap<FString, TObjectPtr<UK2Node>>& AvailableNodes)
{
    FConnectionAssemblyContext Context;
    Context.Blueprint = Blueprint;
    Context.Graph = Graph;
    Context.AvailableNodes = AvailableNodes;
    Context.Config = Config;
    Context.AssemblyStartTime = FDateTime::Now();
    
    return Context;
}

FConnectionAssemblyStatistics FConnectionAssemblySystem::GetStatistics() const
{
    return Statistics;
}

void FConnectionAssemblySystem::ResetStatistics()
{
    Statistics = FConnectionAssemblyStatistics();
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Connection assembly statistics reset"));
}

EConnectionAssemblyStrategy FConnectionAssemblySystem::SelectOptimalStrategy(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context)
{
    // Simple strategy selection based on instruction count
    if (Instructions.Num() <= 5)
    {
        return EConnectionAssemblyStrategy::Sequential;
    }
    else if (Instructions.Num() <= 20)
    {
        return EConnectionAssemblyStrategy::PriorityBased;
    }
    else
    {
        return EConnectionAssemblyStrategy::Optimized;
    }
}

// Private implementation methods

bool FConnectionAssemblySystem::ExecuteConnectionAssembly(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, FConnectionAssemblyResult& OutResult)
{
    // Validate connection first
    if (Instruction.ValidationLevel != EConnectionValidationLevel::None)
    {
        TArray<FString> ValidationMessages;
        bool bValidationPassed = false;

        switch (Instruction.ValidationLevel)
        {
            case EConnectionValidationLevel::Basic:
                bValidationPassed = ValidateConnectionBasic(Instruction, Context, ValidationMessages);
                break;
            case EConnectionValidationLevel::Strict:
                bValidationPassed = ValidateConnectionStrict(Instruction, Context, ValidationMessages);
                break;
            case EConnectionValidationLevel::Advanced:
                bValidationPassed = ValidateConnectionAdvanced(Instruction, Context, ValidationMessages);
                break;
            case EConnectionValidationLevel::Complete:
                bValidationPassed = ValidateConnectionComplete(Instruction, Context, ValidationMessages);
                break;
        }

        OutResult.bValidationPerformed = true;
        OutResult.bValidationPassed = bValidationPassed;
        OutResult.ValidationMessages = ValidationMessages;

        if (!bValidationPassed && !Instruction.bForceConnection)
        {
            OutResult.ErrorMessage = TEXT("Connection validation failed");
            return false;
        }
    }

    // Try direct connection first
    if (EstablishDirectConnection(Instruction, Context, OutResult))
    {
        OutResult.bConnectionEstablished = true;
        OutResult.bSuccess = true;
        return true;
    }

    // Try connection with conversion if allowed
    if (Instruction.bAllowConversionNodes)
    {
        if (EstablishConnectionWithConversion(Instruction, Context, OutResult))
        {
            OutResult.bConnectionEstablished = true;
            OutResult.bConversionNodeCreated = true;
            OutResult.bSuccess = true;
            return true;
        }
    }

    // Force connection if requested
    if (Instruction.bForceConnection)
    {
        // In a full implementation, this would force the connection
        OutResult.bConnectionEstablished = true;
        OutResult.bSuccess = true;
        OutResult.Warnings.Add(TEXT("Connection was forced despite incompatibility"));
        return true;
    }

    OutResult.ErrorMessage = TEXT("Unable to establish connection");
    return false;
}

bool FConnectionAssemblySystem::EstablishDirectConnection(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, FConnectionAssemblyResult& OutResult)
{
    if (!ConnectionManager.IsValid())
    {
        return false;
    }

    // Use ConnectionManager to establish the connection
    FConnectionRequest ConnectionRequest;
    ConnectionRequest.SourceNodeId = Instruction.SourceNodeId;
    ConnectionRequest.SourcePinName = Instruction.SourcePinName;
    ConnectionRequest.TargetNodeId = Instruction.TargetNodeId;
    ConnectionRequest.TargetPinName = Instruction.TargetPinName;
    ConnectionRequest.Strategy = EConnectionStrategy::Direct;

    FConnectionResult ConnectionResult;
    bool bSuccess = ConnectionManager->EstablishConnection(ConnectionRequest, ConnectionResult);

    if (bSuccess)
    {
        OutResult.CompatibilityLevel = EPinCompatibilityLevel::PerfectMatch;
        OutResult.QualityScore = CalculateConnectionQuality(Instruction, Context);
    }

    return bSuccess;
}

bool FConnectionAssemblySystem::EstablishConnectionWithConversion(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, FConnectionAssemblyResult& OutResult)
{
    // In a full implementation, this would create conversion nodes
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Attempting connection with conversion"));
    
    // Placeholder implementation
    OutResult.ConversionNodeId = FString::Printf(TEXT("ConversionNode_%s"), *FDateTime::Now().ToString());
    BroadcastConversionNodeCreated(OutResult.ConversionNodeId);
    
    return true;
}

bool FConnectionAssemblySystem::ExecuteSequentialStrategy(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context, TArray<FConnectionAssemblyResult>& OutResults)
{
    bool bOverallSuccess = true;

    for (int32 i = 0; i < Instructions.Num(); ++i)
    {
        FConnectionAssemblyResult Result;
        bool bSuccess = AssembleConnection(Instructions[i], Context, Result);
        OutResults[i] = Result;

        if (!bSuccess)
        {
            bOverallSuccess = false;
        }

        // Report progress
        if (Config.bEnablePerformanceMonitoring)
        {
            float Progress = static_cast<float>(i + 1) / Instructions.Num();
            BroadcastAssemblyProgress(Instructions[i].InstructionId, Progress);
        }
    }

    return bOverallSuccess;
}

bool FConnectionAssemblySystem::ValidateConnectionBasic(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, TArray<FString>& OutMessages)
{
    return ValidateConnection(Instruction.SourceNodeId, Instruction.SourcePinName, 
                            Instruction.TargetNodeId, Instruction.TargetPinName, 
                            Context, OutMessages);
}

float FConnectionAssemblySystem::CalculateCompatibilityScore(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context)
{
    // Placeholder implementation
    return 0.8f;
}

float FConnectionAssemblySystem::CalculatePerformanceScore(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context)
{
    // Placeholder implementation
    return 0.7f;
}

float FConnectionAssemblySystem::CalculateReadabilityScore(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context)
{
    // Placeholder implementation
    return 0.9f;
}

float FConnectionAssemblySystem::CalculateMaintenanceScore(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context)
{
    // Placeholder implementation
    return 0.8f;
}

void FConnectionAssemblySystem::StartAssemblyTimer(const FString& InstructionId)
{
    if (Config.bEnablePerformanceMonitoring)
    {
        AssemblyTimers.Add(InstructionId, FDateTime::Now());
    }
}

void FConnectionAssemblySystem::StopAssemblyTimer(const FString& InstructionId, FConnectionAssemblyResult& Result)
{
    if (Config.bEnablePerformanceMonitoring && AssemblyTimers.Contains(InstructionId))
    {
        FDateTime StartTime = AssemblyTimers[InstructionId];
        FTimespan Duration = FDateTime::Now() - StartTime;
        Result.AssemblyTime = Duration.GetTotalSeconds();
        AssemblyTimers.Remove(InstructionId);
    }
}

void FConnectionAssemblySystem::UpdateStatistics(const FConnectionAssemblyResult& Result)
{
    if (!Config.bEnableStatistics)
    {
        return;
    }

    Statistics.TotalAssemblies++;
    
    if (Result.bSuccess)
    {
        Statistics.SuccessfulAssemblies++;
    }
    else
    {
        Statistics.FailedAssemblies++;
    }

    if (Result.bConnectionEstablished && !Result.bConversionNodeCreated)
    {
        Statistics.DirectConnections++;
    }

    if (Result.bConversionNodeCreated)
    {
        Statistics.ConversionNodesCreated++;
    }

    if (Result.bValidationPerformed)
    {
        Statistics.ValidationsPerformed++;
        if (!Result.bValidationPassed)
        {
            Statistics.ValidationFailures++;
        }
    }

    Statistics.TotalAssemblyTime += Result.AssemblyTime;
    Statistics.AverageAssemblyTime = Statistics.TotalAssemblyTime / Statistics.TotalAssemblies;
    
    if (Result.QualityScore > 0.0f)
    {
        Statistics.AverageQualityScore = (Statistics.AverageQualityScore + Result.QualityScore) / 2.0f;
    }
}

void FConnectionAssemblySystem::BroadcastAssemblyStarted(const FString& InstructionId)
{
    OnConnectionAssemblyStarted.Broadcast(InstructionId);
}

void FConnectionAssemblySystem::BroadcastAssemblyProgress(const FString& InstructionId, float Progress)
{
    OnConnectionAssemblyProgress.Broadcast(InstructionId, Progress);
}

void FConnectionAssemblySystem::BroadcastAssemblyCompleted(const FConnectionAssemblyResult& Result)
{
    OnConnectionAssemblyCompleted.Broadcast(Result);
}

void FConnectionAssemblySystem::BroadcastAssemblyFailed(const FString& InstructionId, const FString& ErrorMessage)
{
    OnConnectionAssemblyFailed.Broadcast(InstructionId, ErrorMessage);
}

void FConnectionAssemblySystem::BroadcastConversionNodeCreated(const FString& ConversionNodeId)
{
    OnConversionNodeCreated.Broadcast(ConversionNodeId);
}

FString FConnectionAssemblySystem::GenerateInstructionId()
{
    return FString::Printf(TEXT("ConnAssembly_%s"), *FDateTime::Now().ToString());
}

void FConnectionAssemblySystem::InitializeComponents()
{
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Initializing connection assembly system components"));

    CompatibilityChecker = MakeShared<FPinCompatibilityChecker>();
    ConnectionManager = MakeShared<FConnectionManager>();
}

void FConnectionAssemblySystem::ShutdownComponents()
{
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Shutting down connection assembly system components"));

    CompatibilityChecker.Reset();
    ConnectionManager.Reset();
}

void FConnectionAssemblySystem::InitializeBuiltInOptimizationRules()
{
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Initializing built-in optimization rules"));

    // Performance optimization rule
    FConnectionOptimizationRule PerformanceRule;
    PerformanceRule.RuleName = TEXT("MinimizeExecutionCost");
    PerformanceRule.OptimizationMode = EConnectionOptimizationMode::Performance;
    PerformanceRule.Priority = 10;
    PerformanceRule.QualityWeight = 1.0f;
    PerformanceRule.bIsEnabled = true;
    PerformanceRule.Description = TEXT("Minimize runtime execution cost");
    RegisterOptimizationRule(PerformanceRule);

    // Readability optimization rule
    FConnectionOptimizationRule ReadabilityRule;
    ReadabilityRule.RuleName = TEXT("MinimizeWireLength");
    ReadabilityRule.OptimizationMode = EConnectionOptimizationMode::Readability;
    ReadabilityRule.Priority = 8;
    ReadabilityRule.QualityWeight = 0.8f;
    ReadabilityRule.bIsEnabled = true;
    ReadabilityRule.Description = TEXT("Minimize visual wire length for better readability");
    RegisterOptimizationRule(ReadabilityRule);
}

void FConnectionAssemblySystem::RegisterOptimizationRule(const FConnectionOptimizationRule& Rule)
{
    if (!OptimizationRules.Contains(Rule.OptimizationMode))
    {
        OptimizationRules.Add(Rule.OptimizationMode, TArray<FConnectionOptimizationRule>());
    }
    
    OptimizationRules[Rule.OptimizationMode].Add(Rule);
    UE_LOG(LogConnectionAssemblySystem, Log, TEXT("Registered optimization rule: %s"), *Rule.RuleName);
}

// Placeholder implementations for remaining strategy methods
bool FConnectionAssemblySystem::ExecuteParallelStrategy(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context, TArray<FConnectionAssemblyResult>& OutResults)
{
    return ExecuteSequentialStrategy(Instructions, Context, OutResults);
}

bool FConnectionAssemblySystem::ExecuteOptimizedStrategy(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context, TArray<FConnectionAssemblyResult>& OutResults)
{
    return ExecuteSequentialStrategy(Instructions, Context, OutResults);
}

bool FConnectionAssemblySystem::ExecuteTypeGroupedStrategy(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context, TArray<FConnectionAssemblyResult>& OutResults)
{
    return ExecuteSequentialStrategy(Instructions, Context, OutResults);
}

bool FConnectionAssemblySystem::ExecutePriorityBasedStrategy(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context, TArray<FConnectionAssemblyResult>& OutResults)
{
    return ExecuteSequentialStrategy(Instructions, Context, OutResults);
}

bool FConnectionAssemblySystem::ExecuteDependencyOrderStrategy(const TArray<FConnectionAssemblyInstruction>& Instructions, FConnectionAssemblyContext& Context, TArray<FConnectionAssemblyResult>& OutResults)
{
    return ExecuteSequentialStrategy(Instructions, Context, OutResults);
}

bool FConnectionAssemblySystem::ValidateConnectionStrict(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, TArray<FString>& OutMessages)
{
    return ValidateConnectionBasic(Instruction, Context, OutMessages);
}

bool FConnectionAssemblySystem::ValidateConnectionAdvanced(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, TArray<FString>& OutMessages)
{
    return ValidateConnectionBasic(Instruction, Context, OutMessages);
}

bool FConnectionAssemblySystem::ValidateConnectionComplete(const FConnectionAssemblyInstruction& Instruction, FConnectionAssemblyContext& Context, TArray<FString>& OutMessages)
{
    return ValidateConnectionBasic(Instruction, Context, OutMessages);
} 