#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "NLP/StructureExtractor.h"

DECLARE_LOG_CATEGORY_EXTERN(LogCodeGenerationFramework, Log, All);

/**
 * Code generation target enumeration
 */
UENUM(BlueprintType)
enum class ECodeGenerationTarget : uint8
{
    None,
    CPlusPlus,          // Generate C++ header and source files
    Python,             // Generate Python scripts
    JSON,               // Generate JSON representation
    XML,                // Generate XML representation
    Documentation,      // Generate documentation
    Custom              // Custom format defined by user
};

/**
 * Code generation mode enumeration
 */
UENUM(BlueprintType)
enum class ECodeGenerationMode : uint8
{
    HeaderOnly,         // Generate only header files
    SourceOnly,         // Generate only source files
    Complete,           // Generate both header and source files
    Documentation,      // Generate documentation only
    Minimal,            // Generate minimal code structure
    Verbose,            // Generate verbose code with comments
    Custom              // Custom mode defined by user
};

/**
 * Code generation quality level enumeration
 */
UENUM(BlueprintType)
enum class ECodeGenerationQuality : uint8
{
    Draft,              // Quick generation with basic structure
    Standard,           // Standard quality with proper formatting
    Production,         // Production-ready code with optimizations
    Enterprise,         // Enterprise-level code with full documentation
    Custom              // Custom quality level
};

/**
 * Code generation request structure
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FCodeGenerationRequest
{
    GENERATED_BODY()

    // Source blueprint to generate code from
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    TObjectPtr<UBlueprint> SourceBlueprint = nullptr;

    // Target code generation format
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    ECodeGenerationTarget Target = ECodeGenerationTarget::CPlusPlus;

    // Code generation mode
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    ECodeGenerationMode Mode = ECodeGenerationMode::Complete;

    // Quality level for generated code
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    ECodeGenerationQuality Quality = ECodeGenerationQuality::Standard;

    // Output directory path
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    FString OutputDirectory;

    // Base filename for generated files
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    FString BaseFilename;

    // Include comments in generated code
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bIncludeComments = true;

    // Include documentation in generated code
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bIncludeDocumentation = true;

    // Include error handling in generated code
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bIncludeErrorHandling = true;

    // Include performance optimizations
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bIncludeOptimizations = false;

    // Include debug information
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bIncludeDebugInfo = false;

    // Custom generation parameters
    UPROPERTY(BlueprintReadWrite, Category = "Request")
    TMap<FString, FString> CustomParameters;

    FCodeGenerationRequest()
    {
        SourceBlueprint = nullptr;
        Target = ECodeGenerationTarget::CPlusPlus;
        Mode = ECodeGenerationMode::Complete;
        Quality = ECodeGenerationQuality::Standard;
        bIncludeComments = true;
        bIncludeDocumentation = true;
        bIncludeErrorHandling = true;
        bIncludeOptimizations = false;
        bIncludeDebugInfo = false;
    }
};

/**
 * Generated code file structure
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FGeneratedCodeFile
{
    GENERATED_BODY()

    // File path relative to output directory
    UPROPERTY(BlueprintReadOnly, Category = "File")
    FString RelativeFilePath;

    // Full file path
    UPROPERTY(BlueprintReadOnly, Category = "File")
    FString FullFilePath;

    // Generated code content
    UPROPERTY(BlueprintReadOnly, Category = "File")
    FString Content;

    // File type (header, source, documentation, etc.)
    UPROPERTY(BlueprintReadOnly, Category = "File")
    FString FileType;

    // File size in bytes
    UPROPERTY(BlueprintReadOnly, Category = "File")
    int32 FileSizeBytes = 0;

    // Number of lines in the file
    UPROPERTY(BlueprintReadOnly, Category = "File")
    int32 LineCount = 0;

    // File creation timestamp
    UPROPERTY(BlueprintReadOnly, Category = "File")
    FDateTime CreationTime;

    FGeneratedCodeFile()
    {
        FileSizeBytes = 0;
        LineCount = 0;
        CreationTime = FDateTime::Now();
    }
};

/**
 * Code generation result structure
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FCodeGenerationResult
{
    GENERATED_BODY()

    // Whether code generation was successful
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // Generated files
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FGeneratedCodeFile> GeneratedFiles;

    // Generation errors
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Errors;

    // Generation warnings
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Warnings;

    // Generation notes
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Notes;

    // Total generation time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float GenerationTime = 0.0f;

    // Total lines of code generated
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 TotalLinesGenerated = 0;

    // Total files generated
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 TotalFilesGenerated = 0;

    // Quality score of generated code (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float QualityScore = 0.0f;

    FCodeGenerationResult()
    {
        bSuccess = false;
        GenerationTime = 0.0f;
        TotalLinesGenerated = 0;
        TotalFilesGenerated = 0;
        QualityScore = 0.0f;
    }
};

/**
 * Code generation configuration structure
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FCodeGenerationConfig
{
    GENERATED_BODY()

    // Enable parallel code generation
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    bool bEnableParallelGeneration = true;

    // Enable code validation after generation
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    bool bEnableCodeValidation = true;

    // Enable automatic file formatting
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    bool bEnableAutoFormatting = true;

    // Enable backup of existing files
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    bool bEnableFileBackup = true;

    // Maximum generation time in seconds
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    float MaxGenerationTime = 300.0f;

    // Maximum file size in MB
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    float MaxFileSizeMB = 10.0f;

    // Code style settings
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    FString IndentationStyle = TEXT("Tabs");

    // Line ending style
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    FString LineEndingStyle = TEXT("CRLF");

    // Maximum line length
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    int32 MaxLineLength = 120;

    // Custom formatting rules
    UPROPERTY(BlueprintReadWrite, Category = "Configuration")
    TMap<FString, FString> FormattingRules;

    FCodeGenerationConfig()
    {
        bEnableParallelGeneration = true;
        bEnableCodeValidation = true;
        bEnableAutoFormatting = true;
        bEnableFileBackup = true;
        MaxGenerationTime = 300.0f;
        MaxFileSizeMB = 10.0f;
        IndentationStyle = TEXT("Tabs");
        LineEndingStyle = TEXT("CRLF");
        MaxLineLength = 120;
    }
};

/**
 * Code generation statistics structure
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FCodeGenerationStatistics
{
    GENERATED_BODY()

    // Total number of code generations performed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalGenerations = 0;

    // Number of successful generations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulGenerations = 0;

    // Number of failed generations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedGenerations = 0;

    // Average generation time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageGenerationTime = 0.0f;

    // Fastest generation time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float FastestGenerationTime = 0.0f;

    // Slowest generation time in seconds
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float SlowestGenerationTime = 0.0f;

    // Total lines of code generated
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalLinesGenerated = 0;

    // Total files generated
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalFilesGenerated = 0;

    // Average quality score
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageQualityScore = 0.0f;

    // Most used generation target
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    ECodeGenerationTarget MostUsedTarget = ECodeGenerationTarget::CPlusPlus;

    // Most used generation mode
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    ECodeGenerationMode MostUsedMode = ECodeGenerationMode::Complete;

    FCodeGenerationStatistics()
    {
        TotalGenerations = 0;
        SuccessfulGenerations = 0;
        FailedGenerations = 0;
        AverageGenerationTime = 0.0f;
        FastestGenerationTime = 0.0f;
        SlowestGenerationTime = 0.0f;
        TotalLinesGenerated = 0;
        TotalFilesGenerated = 0;
        AverageQualityScore = 0.0f;
        MostUsedTarget = ECodeGenerationTarget::CPlusPlus;
        MostUsedMode = ECodeGenerationMode::Complete;
    }
};

/**
 * Delegate declarations for code generation events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCodeGenerationStarted, const FCodeGenerationRequest&, Request);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCodeGenerationProgress, float, Progress, const FString&, CurrentTask);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCodeGenerationCompleted, const FCodeGenerationResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCodeGenerationError, const FString&, ErrorMessage, const FCodeGenerationRequest&, Request);

/**
 * Code Generation Framework - Core system for converting blueprints to various code formats
 * 
 * This class provides the foundation for converting UE5 blueprints into different code formats
 * including C++, Python, JSON, XML, and documentation. It manages the generation pipeline,
 * handles different output formats, and ensures code quality through validation and formatting.
 */
class UE5BLUEPRINTGENERATOR_API FCodeGenerationFramework
{
public:
    FCodeGenerationFramework();
    virtual ~FCodeGenerationFramework();

    // Singleton access
    static FCodeGenerationFramework& Get();

    // Core generation operations
    bool GenerateCode(const FCodeGenerationRequest& Request, FCodeGenerationResult& OutResult);
    bool IsGenerationInProgress() const;
    void CancelGeneration();

    // Configuration management
    void SetGenerationConfig(const FCodeGenerationConfig& Config);
    FCodeGenerationConfig GetGenerationConfig() const;

    // Statistics
    FCodeGenerationStatistics GetStatistics() const;
    void ResetStatistics();

    // Target registration
    bool RegisterCodeGenerator(ECodeGenerationTarget Target, TSharedPtr<class ICodeGenerator> Generator);
    bool UnregisterCodeGenerator(ECodeGenerationTarget Target);
    TArray<ECodeGenerationTarget> GetSupportedTargets() const;

    // Validation
    bool ValidateGenerationRequest(const FCodeGenerationRequest& Request, TArray<FString>& OutValidationErrors);
    bool ValidateGeneratedCode(const FGeneratedCodeFile& CodeFile, TArray<FString>& OutValidationErrors);

    // File operations
    bool SaveGeneratedFiles(const TArray<FGeneratedCodeFile>& Files, const FString& OutputDirectory);
    bool BackupExistingFiles(const TArray<FString>& FilePaths, const FString& BackupDirectory);
    bool FormatCodeFile(FGeneratedCodeFile& CodeFile);

    // Event delegates
    FOnCodeGenerationStarted OnCodeGenerationStarted;
    FOnCodeGenerationProgress OnCodeGenerationProgress;
    FOnCodeGenerationCompleted OnCodeGenerationCompleted;
    FOnCodeGenerationError OnCodeGenerationError;

private:
    // Singleton instance
    static TSharedPtr<FCodeGenerationFramework> Instance;

    // Configuration
    FCodeGenerationConfig Config;

    // Statistics
    FCodeGenerationStatistics Statistics;

    // Generation state
    bool bIsGenerationInProgress;
    FDateTime GenerationStartTime;
    FCodeGenerationRequest CurrentRequest;

    // Registered code generators
    TMap<ECodeGenerationTarget, TSharedPtr<class ICodeGenerator>> CodeGenerators;

    // Core generation methods
    bool InitializeGeneration(const FCodeGenerationRequest& Request);
    bool ExecuteGeneration(const FCodeGenerationRequest& Request, FCodeGenerationResult& OutResult);
    bool FinalizeGeneration(const FCodeGenerationResult& Result);

    // Blueprint analysis
    bool AnalyzeBlueprint(UBlueprint* Blueprint, FExtractedBlueprintStructure& OutStructure);
    bool ExtractBlueprintMetadata(UBlueprint* Blueprint, TMap<FString, FString>& OutMetadata);

    // Generation pipeline
    bool PreprocessBlueprint(UBlueprint* Blueprint);
    bool GenerateForTarget(const FCodeGenerationRequest& Request, ECodeGenerationTarget Target, TArray<FGeneratedCodeFile>& OutFiles);
    bool PostprocessGeneratedFiles(TArray<FGeneratedCodeFile>& Files);

    // Validation helpers
    bool ValidateBlueprint(UBlueprint* Blueprint, TArray<FString>& OutErrors);
    bool ValidateOutputDirectory(const FString& Directory, TArray<FString>& OutErrors);
    bool ValidateFilename(const FString& Filename, TArray<FString>& OutErrors);

    // File management
    bool CreateOutputDirectory(const FString& Directory);
    bool GenerateUniqueFilename(const FString& BaseFilename, const FString& Extension, const FString& Directory, FString& OutFilename);
    bool WriteFileContent(const FString& FilePath, const FString& Content);

    // Statistics helpers
    void UpdateStatistics(const FCodeGenerationResult& Result);
    void CalculateQualityScore(FCodeGenerationResult& Result);

    // Event broadcasting
    void BroadcastGenerationStarted(const FCodeGenerationRequest& Request);
    void BroadcastGenerationProgress(float Progress, const FString& CurrentTask);
    void BroadcastGenerationCompleted(const FCodeGenerationResult& Result);
    void BroadcastGenerationError(const FString& ErrorMessage, const FCodeGenerationRequest& Request);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();
    void RegisterBuiltInGenerators();
};

/**
 * Interface for code generators that can be registered with the framework
 */
class UE5BLUEPRINTGENERATOR_API ICodeGenerator
{
public:
    virtual ~ICodeGenerator() = default;

    // Core generation interface
    virtual bool GenerateCode(const FCodeGenerationRequest& Request, TArray<FGeneratedCodeFile>& OutFiles) = 0;
    virtual bool CanGenerateForTarget(ECodeGenerationTarget Target) const = 0;
    virtual ECodeGenerationTarget GetSupportedTarget() const = 0;

    // Generator information
    virtual FString GetGeneratorName() const = 0;
    virtual FString GetGeneratorVersion() const = 0;
    virtual FString GetGeneratorDescription() const = 0;

    // Validation
    virtual bool ValidateRequest(const FCodeGenerationRequest& Request, TArray<FString>& OutErrors) = 0;
    virtual bool ValidateGeneratedCode(const FGeneratedCodeFile& CodeFile, TArray<FString>& OutErrors) = 0;

    // Configuration
    virtual void SetGeneratorConfig(const TMap<FString, FString>& Config) = 0;
    virtual TMap<FString, FString> GetGeneratorConfig() const = 0;
}; 