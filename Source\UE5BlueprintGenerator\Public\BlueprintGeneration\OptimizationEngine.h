#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGeneration/CompilationManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogOptimizationEngine, Log, All);

/**
 * Optimization type enumeration
 */
UENUM(BlueprintType)
enum class EBPGenOptimizationType : uint8
{
    None,                   // No optimization
    Performance,            // Performance optimization
    Memory,                 // Memory usage optimization
    Readability,            // Code readability optimization
    Maintenance,            // Maintainability optimization
    Size,                   // Blueprint size optimization
    Compilation,            // Compilation time optimization
    Runtime,                // Runtime performance optimization
    Network,                // Network performance optimization
    All                     // All optimization types
};

/**
 * Optimization level enumeration
 */
UENUM(BlueprintType)
enum class EOptimizationLevel : uint8
{
    None,                   // No optimization
    Basic,                  // Basic optimizations
    Standard,               // Standard optimizations
    Aggressive,             // Aggressive optimizations
    Maximum,                // Maximum optimizations
    Custom                  // Custom optimization level
};

/**
 * Optimization strategy enumeration
 */
UENUM(BlueprintType)
enum class EOptimizationStrategy : uint8
{
    Conservative,           // Conservative optimization (safe)
    Balanced,               // Balanced optimization
    Aggressive,             // Aggressive optimization
    Experimental,           // Experimental optimizations
    Custom                  // Custom optimization strategy
};

/**
 * Optimization rule structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONRULE
{
    GENERATED_BODY()

    // Rule name
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    FString RuleName;

    // Rule description
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    FString Description;

    // Optimization type
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    EBPGenOptimizationType OptimizationType = EBPGenOptimizationType::Performance;

    // Rule priority (1-10)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule", meta = (ClampMin = "1", ClampMax = "10"))
    int32 Priority = 5;

    // Rule enabled
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    bool bEnabled = true;

    // Expected improvement (0.0-1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ExpectedImprovement = 0.1f;

    // Risk level (0.0-1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float RiskLevel = 0.1f;

    // Rule conditions
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    TMap<FString, FString> Conditions;

    // Rule parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    TMap<FString, FString> Parameters;

    FOPTIMIZATIONRULE()
    {
        RuleName = TEXT("Default Rule");
        Description = TEXT("Default optimization rule");
        OptimizationType = EBPGenOptimizationType::Performance;
        Priority = 5;
        bEnabled = true;
        ExpectedImprovement = 0.1f;
        RiskLevel = 0.1f;
    }
};

/**
 * Optimization analysis result structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONANALYSISRESULT
{
    GENERATED_BODY()

    // Analysis successful
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    bool bAnalysisSuccessful = false;

    // Current performance score (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float CurrentPerformanceScore = 0.0f;

    // Potential improvement (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float PotentialImprovement = 0.0f;

    // Optimization opportunities
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> OptimizationOpportunities;

    // Performance bottlenecks
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> PerformanceBottlenecks;

    // Memory usage issues
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> MemoryUsageIssues;

    // Readability issues
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> ReadabilityIssues;

    // Maintenance issues
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> MaintenanceIssues;

    // Applicable rules
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> ApplicableRules;

    // Analysis time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float AnalysisTime = 0.0f;

    FOPTIMIZATIONANALYSISRESULT()
    {
        bAnalysisSuccessful = false;
        CurrentPerformanceScore = 0.0f;
        PotentialImprovement = 0.0f;
        AnalysisTime = 0.0f;
    }
};

/**
 * Optimization result structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONRESULT
{
    GENERATED_BODY()

    // Optimization successful
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bOptimizationSuccessful = false;

    // Rules applied
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> RulesApplied;

    // Performance improvement (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float PerformanceImprovement = 0.0f;

    // Memory improvement (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float MemoryImprovement = 0.0f;

    // Readability improvement (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ReadabilityImprovement = 0.0f;

    // Maintenance improvement (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float MaintenanceImprovement = 0.0f;

    // Changes made
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> ChangesMade;

    // Side effects
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> SideEffects;

    // Optimization time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float OptimizationTime = 0.0f;

    // Before/after metrics
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, float> BeforeMetrics;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, float> AfterMetrics;

    FOPTIMIZATIONRESULT()
    {
        bOptimizationSuccessful = false;
        PerformanceImprovement = 0.0f;
        MemoryImprovement = 0.0f;
        ReadabilityImprovement = 0.0f;
        MaintenanceImprovement = 0.0f;
        OptimizationTime = 0.0f;
    }
};

/**
 * Optimization configuration structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONCONFIG
{
    GENERATED_BODY()

    // Optimization level
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    EOptimizationLevel OptimizationLevel = EOptimizationLevel::Standard;

    // Optimization strategy
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    EOptimizationStrategy OptimizationStrategy = EOptimizationStrategy::Balanced;

    // Enabled optimization types
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    TArray<EBPGenOptimizationType> EnabledOptimizationTypes;

    // Enable performance optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceOptimization = true;

    // Enable memory optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableMemoryOptimization = true;

    // Enable readability optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Readability")
    bool bEnableReadabilityOptimization = true;

    // Enable maintenance optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Maintenance")
    bool bEnableMaintenanceOptimization = true;

    // Enable size optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size")
    bool bEnableSizeOptimization = false;

    // Enable compilation optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnableCompilationOptimization = true;

    // Enable runtime optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Runtime")
    bool bEnableRuntimeOptimization = true;

    // Enable network optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    bool bEnableNetworkOptimization = false;

    // Maximum optimization time (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Limits", meta = (ClampMin = "1", ClampMax = "300"))
    float MaxOptimizationTime = 60.0f;

    // Maximum risk level (0.0-1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Limits", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MaxRiskLevel = 0.3f;

    // Minimum improvement threshold (0.0-1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Limits", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MinImprovementThreshold = 0.05f;

    // Enable backup creation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    bool bCreateBackup = true;

    // Enable rollback on failure
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    bool bRollbackOnFailure = true;

    // Enable validation after optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    bool bValidateAfterOptimization = true;

    // Custom optimization rules
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
    TArray<FOptimizationRule> CustomRules;

    FOPTIMIZATIONCONFIG()
    {
        OptimizationLevel = EOptimizationLevel::Standard;
        OptimizationStrategy = EOptimizationStrategy::Balanced;
        EnabledOptimizationTypes = { EBPGenOptimizationType::Performance, EBPGenOptimizationType::Memory, EBPGenOptimizationType::Readability };
        bEnablePerformanceOptimization = true;
        bEnableMemoryOptimization = true;
        bEnableReadabilityOptimization = true;
        bEnableMaintenanceOptimization = true;
        bEnableSizeOptimization = false;
        bEnableCompilationOptimization = true;
        bEnableRuntimeOptimization = true;
        bEnableNetworkOptimization = false;
        MaxOptimizationTime = 60.0f;
        MaxRiskLevel = 0.3f;
        MinImprovementThreshold = 0.05f;
        bCreateBackup = true;
        bRollbackOnFailure = true;
        bValidateAfterOptimization = true;
    }
};

/**
 * Optimization statistics structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONSTATISTICS
{
    GENERATED_BODY()

    // Total optimizations performed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalOptimizations = 0;

    // Successful optimizations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulOptimizations = 0;

    // Failed optimizations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedOptimizations = 0;

    // Average optimization time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageOptimizationTime = 0.0f;

    // Average performance improvement
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AveragePerformanceImprovement = 0.0f;

    // Average memory improvement
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageMemoryImprovement = 0.0f;

    // Rules usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<FString, int32> RulesUsage;

    // Optimization types usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EBPGenOptimizationType, int32> OptimizationTypesUsage;

    // Most effective rules
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<FString, float> MostEffectiveRules;

    FOPTIMIZATIONSTATISTICS()
    {
        TotalOptimizations = 0;
        SuccessfulOptimizations = 0;
        FailedOptimizations = 0;
        AverageOptimizationTime = 0.0f;
        AveragePerformanceImprovement = 0.0f;
        AverageMemoryImprovement = 0.0f;
    }
};

/**
 * Delegate declarations for optimization events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnOptimizationStarted, UBlueprint*, Blueprint);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnOptimizationAnalysisCompleted, UBlueprint*, Blueprint, const FOptimizationAnalysisResult&, Analysis);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnOptimizationRuleApplied, UBlueprint*, Blueprint, const FString&, RuleName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnOptimizationCompleted, UBlueprint*, Blueprint, const FOptimizationResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnOptimizationFailed, UBlueprint*, Blueprint, const FString&, ErrorMessage);

/**
 * Optimization Engine - Advanced optimization system for blueprint performance and quality improvement
 * 
 * This class provides comprehensive optimization capabilities for blueprints,
 * including performance optimization, memory optimization, readability improvement,
 * and maintenance enhancement. It uses a rule-based system with configurable
 * optimization strategies and safety measures.
 */
class UE5BLUEPRINTGENERATOR_API FOptimizationEngine
{
public:
    FOptimizationEngine();
    virtual ~FOptimizationEngine();

    // Core optimization operations
    bool OptimizeBlueprint(UBlueprint* Blueprint, const FOptimizationConfig& Config, FOptimizationResult& OutResult);
    bool OptimizeBlueprints(const TArray<UBlueprint*>& Blueprints, const FOptimizationConfig& Config, TArray<FOptimizationResult>& OutResults);

    // Analysis operations
    FOptimizationAnalysisResult AnalyzeBlueprint(UBlueprint* Blueprint, const FOptimizationConfig& Config);
    bool CanOptimizeBlueprint(UBlueprint* Blueprint, const FOptimizationConfig& Config);
    TArray<FString> GetOptimizationOpportunities(UBlueprint* Blueprint, const FOptimizationConfig& Config);

    // Rule management
    void AddOptimizationRule(const FOptimizationRule& Rule);
    void RemoveOptimizationRule(const FString& RuleName);
    bool HasOptimizationRule(const FString& RuleName) const;
    FOptimizationRule GetOptimizationRule(const FString& RuleName) const;
    TArray<FOptimizationRule> GetAllOptimizationRules() const;
    TArray<FOptimizationRule> GetApplicableRules(UBlueprint* Blueprint, const FOptimizationConfig& Config);

    // Performance analysis
    float CalculatePerformanceScore(UBlueprint* Blueprint);
    float CalculateMemoryScore(UBlueprint* Blueprint);
    float CalculateReadabilityScore(UBlueprint* Blueprint);
    float CalculateMaintenanceScore(UBlueprint* Blueprint);
    TMap<FString, float> GetDetailedMetrics(UBlueprint* Blueprint);

    // Optimization strategies
    bool ApplyPerformanceOptimizations(UBlueprint* Blueprint, const FOptimizationConfig& Config, FOptimizationResult& OutResult);
    bool ApplyMemoryOptimizations(UBlueprint* Blueprint, const FOptimizationConfig& Config, FOptimizationResult& OutResult);
    bool ApplyReadabilityOptimizations(UBlueprint* Blueprint, const FOptimizationConfig& Config, FOptimizationResult& OutResult);
    bool ApplyMaintenanceOptimizations(UBlueprint* Blueprint, const FOptimizationConfig& Config, FOptimizationResult& OutResult);

    // Configuration management
    void SetOptimizationConfig(const FOptimizationConfig& Config);
    FOptimizationConfig GetOptimizationConfig() const;

    // Statistics and monitoring
    FOptimizationStatistics GetStatistics() const;
    void ResetStatistics();

    // Backup and rollback
    bool CreateBackup(UBlueprint* Blueprint, FString& OutBackupId);
    bool RestoreFromBackup(UBlueprint* Blueprint, const FString& BackupId);
    bool DeleteBackup(const FString& BackupId);
    TArray<FString> GetAvailableBackups(UBlueprint* Blueprint);

    // Validation
    bool ValidateOptimization(UBlueprint* Blueprint, const FOptimizationResult& Result);
    TArray<FString> GetValidationErrors(UBlueprint* Blueprint);

    // Event delegates
    FOnOptimizationStarted OnOptimizationStarted;
    FOnOptimizationAnalysisCompleted OnOptimizationAnalysisCompleted;
    FOnOptimizationRuleApplied OnOptimizationRuleApplied;
    FOnOptimizationCompleted OnOptimizationCompleted;
    FOnOptimizationFailed OnOptimizationFailed;

private:
    // Configuration
    FOptimizationConfig Config;

    // Statistics
    FOptimizationStatistics Statistics;

    // Optimization rules
    TArray<FOptimizationRule> OptimizationRules;

    // Rule handlers
    TMap<FString, TFunction<bool(UBlueprint*, const FOptimizationConfig&, FOptimizationResult&)>> RuleHandlers;

    // Backup storage
    TMap<FString, TArray<uint8>> BackupStorage;

    // Analysis methods
    TArray<FString> AnalyzePerformanceBottlenecks(UBlueprint* Blueprint);
    TArray<FString> AnalyzeMemoryUsage(UBlueprint* Blueprint);
    TArray<FString> AnalyzeReadabilityIssues(UBlueprint* Blueprint);
    TArray<FString> AnalyzeMaintenanceIssues(UBlueprint* Blueprint);
    float CalculatePotentialImprovement(UBlueprint* Blueprint, const FOptimizationConfig& Config);

    // Optimization implementation methods
    bool OptimizeNodeLayout(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeConnections(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeVariables(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeFunctions(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeEvents(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeComponents(UBlueprint* Blueprint, FOptimizationResult& OutResult);

    // Performance optimization methods
    bool RemoveUnusedNodes(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeLoops(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeBranching(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeFunctionCalls(UBlueprint* Blueprint, FOptimizationResult& OutResult);

    // Memory optimization methods
    bool OptimizeVariableTypes(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeArrayUsage(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OptimizeObjectReferences(UBlueprint* Blueprint, FOptimizationResult& OutResult);

    // Readability optimization methods
    bool ImproveNodeNaming(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool AddComments(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool OrganizeNodeLayout(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool GroupRelatedNodes(UBlueprint* Blueprint, FOptimizationResult& OutResult);

    // Maintenance optimization methods
    bool ExtractReusableFunctions(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool SimplifyComplexLogic(UBlueprint* Blueprint, FOptimizationResult& OutResult);
    bool StandardizeNamingConventions(UBlueprint* Blueprint, FOptimizationResult& OutResult);

    // Utility methods
    bool IsRuleApplicable(const FOptimizationRule& Rule, UBlueprint* Blueprint, const FOptimizationConfig& Config);
    float CalculateRuleEffectiveness(const FOptimizationRule& Rule, UBlueprint* Blueprint);
    bool ApplyOptimizationRule(const FOptimizationRule& Rule, UBlueprint* Blueprint, FOptimizationResult& OutResult);
    void UpdateStatistics(const FOptimizationResult& Result);

    // Event broadcasting
    void BroadcastOptimizationStarted(UBlueprint* Blueprint);
    void BroadcastOptimizationAnalysisCompleted(UBlueprint* Blueprint, const FOptimizationAnalysisResult& Analysis);
    void BroadcastOptimizationRuleApplied(UBlueprint* Blueprint, const FString& RuleName);
    void BroadcastOptimizationCompleted(UBlueprint* Blueprint, const FOptimizationResult& Result);
    void BroadcastOptimizationFailed(UBlueprint* Blueprint, const FString& ErrorMessage);

    // Component initialization
    void InitializeComponents();
    void InitializeOptimizationRules();
    void InitializeRuleHandlers();
    void ShutdownComponents();
}; 