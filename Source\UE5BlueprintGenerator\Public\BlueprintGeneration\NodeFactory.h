#pragma once

#include "CoreMinimal.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_CustomEvent.h"
#include "BlueprintGraph/Classes/K2Node_IfThenElse.h"
#include "BlueprintGraph/Classes/K2Node_ExecutionSequence.h"
#include "BlueprintGraph/Classes/K2Node_ForLoop.h"
#include "BlueprintGraph/Classes/K2Node_WhileLoop.h"
#include "BlueprintGraph/Classes/K2Node_Switch.h"
#include "BlueprintGraph/Classes/K2Node_MultiGate.h"
#include "BlueprintGraph/Classes/K2Node_Gate.h"
#include "BlueprintGraph/Classes/K2Node_Delay.h"
#include "BlueprintGraph/Classes/K2Node_Timeline.h"
#include "BlueprintGraph/Classes/K2Node_DynamicCast.h"
#include "BlueprintGraph/Classes/K2Node_ForEachElementInArray.h"
#include "BlueprintGraph/Classes/K2Node_Self.h"
#include "BlueprintGraph/Classes/K2Node_InputAction.h"
#include "BlueprintGraph/Classes/K2Node_InputAxisKeyEvent.h"
#include "BlueprintGraph/Classes/K2Node_MacroInstance.h"
#include "BlueprintGraph/Classes/K2Node_CommutativeAssociativeBinaryOperator.h"
#include "Engine/Blueprint.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNodeFactory, Log, All);

/**
 * Enumeration for different categories of blueprint nodes
 */
UENUM(BlueprintType)
enum class ENodeCategory : uint8
{
    FlowControl     UMETA(DisplayName = "Flow Control"),
    Functions       UMETA(DisplayName = "Functions"),
    Variables       UMETA(DisplayName = "Variables"),
    Events          UMETA(DisplayName = "Events"),
    Timers          UMETA(DisplayName = "Timers"),
    Math            UMETA(DisplayName = "Math"),
    Casting         UMETA(DisplayName = "Casting"),
    Arrays          UMETA(DisplayName = "Arrays"),
    Strings         UMETA(DisplayName = "Strings"),
    Debug           UMETA(DisplayName = "Debug"),
    Input           UMETA(DisplayName = "Input"),
    Custom          UMETA(DisplayName = "Custom")
};

/**
 * Structure containing information about a node type
 */
USTRUCT(BlueprintType)
struct FNODECREATIONINFO
{
    GENERATED_BODY()

    /** Name of the node type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    FString NodeTypeName;

    /** Category of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    ENodeCategory Category;

    /** Description of what the node does */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    FString Description;

    /** Position where the node should be created */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    FVector2D Position;

    /** Properties to set on the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    TMap<FString, FString> Properties;

    /** Comment to add to the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    FString Comment;

    /** Whether the node should be marked as advanced */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    bool bIsAdvanced;

    /** Pins that should be marked as advanced display */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    TArray<FString> AdvancedPins;

    FNODECREATIONINFO()
        : NodeTypeName(TEXT(""))
        , Category(ENodeCategory::Custom)
        , Description(TEXT(""))
        , Position(FVector2D::ZeroVector)
        , Comment(TEXT(""))
        , bIsAdvanced(false)
    {
    }
};

/**
 * Structure containing the result of a node creation operation
 */
USTRUCT(BlueprintType)
struct FNODECREATIONRESULT
{
    GENERATED_BODY()

    /** Whether the creation was successful */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    bool bSuccess;

    /** The created node (if successful) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    UK2Node* CreatedNode;

    /** Error message (if creation failed) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    FString ErrorMessage;

    /** Warning messages */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    TArray<FString> WarningMessages;

    /** Node GUID for reference */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Creation")
    FString NodeGUID;

    FNODECREATIONRESULT()
        : bSuccess(false)
        , CreatedNode(nullptr)
        , ErrorMessage(TEXT(""))
        , NodeGUID(TEXT(""))
    {
    }
};

/**
 * Structure containing information about a registered node type
 */
USTRUCT(BlueprintType)
struct FNODETYPEINFO
{
    GENERATED_BODY()

    /** Name of the node type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString NodeTypeName;

    /** Class of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    UClass* NodeClass;

    /** Category of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    ENodeCategory Category;

    /** Description of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString Description;

    /** Graph types where this node is supported */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    TArray<FString> SupportedGraphTypes;

    /** Whether this node requires special configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    bool bRequiresSpecialConfiguration;

    /** Function name for function call nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString FunctionName;

    /** Class name for function call nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Node Type")
    FString ClassName;

    FNODETYPEINFO()
        : NodeTypeName(TEXT(""))
        , NodeClass(nullptr)
        , Category(ENodeCategory::Custom)
        , Description(TEXT(""))
        , bRequiresSpecialConfiguration(false)
        , FunctionName(TEXT(""))
        , ClassName(TEXT(""))
    {
    }
};

/**
 * Class responsible for creating and managing blueprint nodes
 */
class UE5BLUEPRINTGENERATOR_API FNodeFactory
{
public:
    FNodeFactory();
    ~FNodeFactory();

    // Node creation methods

    /**
     * Creates a node based on the provided creation info
     * @param Graph - The graph to create the node in
     * @param CreationInfo - Information about the node to create
     * @return Result of the creation operation
     */
    FNODECREATIONRESULT CreateNode(UEdGraph* Graph, const FNODECREATIONINFO& CreationInfo);

    /**
     * Creates a node of a specific type
     * @param Graph - The graph to create the node in
     * @param NodeType - Type of node to create
     * @param Position - Position where the node should be created
     * @return The created node or nullptr if creation failed
     */
    UK2Node* CreateNodeOfType(UEdGraph* Graph, const FString& NodeType, const FVector2D& Position);

    /**
     * Creates a function call node
     * @param Graph - The graph to create the node in
     * @param Function - The function to call
     * @param Position - Position where the node should be created
     * @return The created function call node or nullptr if creation failed
     */
    UK2Node_CallFunction* CreateFunctionCallNode(UEdGraph* Graph, UFunction* Function, const FVector2D& Position);

    /**
     * Creates a function call node by name
     * @param Graph - The graph to create the node in
     * @param FunctionName - Name of the function to call
     * @param ClassName - Name of the class containing the function
     * @param Position - Position where the node should be created
     * @return The created function call node or nullptr if creation failed
     */
    UK2Node_CallFunction* CreateFunctionCallNodeByName(UEdGraph* Graph, const FString& FunctionName, const FString& ClassName, const FVector2D& Position);

    /**
     * Creates a variable get node
     * @param Graph - The graph to create the node in
     * @param Property - The property to get
     * @param Position - Position where the node should be created
     * @return The created variable get node or nullptr if creation failed
     */
    UK2Node_VariableGet* CreateVariableGetNode(UEdGraph* Graph, FProperty* Property, const FVector2D& Position);

    /**
     * Creates a variable set node
     * @param Graph - The graph to create the node in
     * @param Property - The property to set
     * @param Position - Position where the node should be created
     * @return The created variable set node or nullptr if creation failed
     */
    UK2Node_VariableSet* CreateVariableSetNode(UEdGraph* Graph, FProperty* Property, const FVector2D& Position);

    /**
     * Creates an event node
     * @param Graph - The graph to create the node in
     * @param EventName - Name of the event
     * @param Position - Position where the node should be created
     * @return The created event node or nullptr if creation failed
     */
    UK2Node_Event* CreateEventNode(UEdGraph* Graph, const FString& EventName, const FVector2D& Position);

    /**
     * Creates a custom event node
     * @param Graph - The graph to create the node in
     * @param EventName - Name of the custom event
     * @param Position - Position where the node should be created
     * @return The created custom event node or nullptr if creation failed
     */
    UK2Node_CustomEvent* CreateCustomEventNode(UEdGraph* Graph, const FString& EventName, const FVector2D& Position);

    // Flow control nodes

    /**
     * Creates a branch (if-then-else) node
     * @param Graph - The graph to create the node in
     * @param Position - Position where the node should be created
     * @return The created branch node or nullptr if creation failed
     */
    UK2Node_IfThenElse* CreateBranchNode(UEdGraph* Graph, const FVector2D& Position);

    /**
     * Creates a sequence node
     * @param Graph - The graph to create the node in
     * @param Position - Position where the node should be created
     * @param OutputCount - Number of output execution pins
     * @return The created sequence node or nullptr if creation failed
     */
    UK2Node_ExecutionSequence* CreateSequenceNode(UEdGraph* Graph, const FVector2D& Position, int32 OutputCount = 2);

    /**
     * Creates a for loop node
     * @param Graph - The graph to create the node in
     * @param Position - Position where the node should be created
     * @return The created for loop node or nullptr if creation failed
     */
    UK2Node_ForLoop* CreateForLoopNode(UEdGraph* Graph, const FVector2D& Position);

    /**
     * Creates a while loop node
     * @param Graph - The graph to create the node in
     * @param Position - Position where the node should be created
     * @return The created while loop node or nullptr if creation failed
     */
    UK2Node_WhileLoop* CreateWhileLoopNode(UEdGraph* Graph, const FVector2D& Position);

    /**
     * Creates a switch node
     * @param Graph - The graph to create the node in
     * @param Position - Position where the node should be created
     * @param SwitchType - Type of switch (int, string, enum, etc.)
     * @return The created switch node or nullptr if creation failed
     */
    UK2Node_Switch* CreateSwitchNode(UEdGraph* Graph, const FVector2D& Position, const FString& SwitchType = TEXT("int"));

    // Node configuration methods

    /**
     * Configures a node with the provided properties
     * @param Node - The node to configure
     * @param Properties - Properties to set on the node
     * @return True if configuration was successful
     */
    bool ConfigureNode(UK2Node* Node, const TMap<FString, FString>& Properties);

    /**
     * Sets a comment on a node
     * @param Node - The node to set the comment on
     * @param Comment - The comment text
     * @return True if the comment was set successfully
     */
    bool SetNodeComment(UK2Node* Node, const FString& Comment);

    /**
     * Marks specific pins as advanced display
     * @param Node - The node to configure
     * @param PinNames - Names of pins to mark as advanced
     * @return True if the pins were marked successfully
     */
    bool SetNodeAdvancedDisplay(UK2Node* Node, const TArray<FString>& PinNames);

    /**
     * Sets a property value on a node
     * @param Node - The node to configure
     * @param PropertyName - Name of the property to set
     * @param PropertyValue - Value to set
     * @return True if the property was set successfully
     */
    bool SetNodeProperty(UK2Node* Node, const FString& PropertyName, const FString& PropertyValue);

    // Node validation methods

    /**
     * Checks if a node type is supported
     * @param NodeType - The node type to check
     * @return True if the node type is supported
     */
    bool IsNodeTypeSupported(const FString& NodeType);

    /**
     * Checks if a node can be created in the specified graph
     * @param NodeType - The node type to check
     * @param Graph - The graph to check
     * @return True if the node can be created in the graph
     */
    bool CanCreateNodeInGraph(const FString& NodeType, UEdGraph* Graph);

    /**
     * Validates a node creation request
     * @param Graph - The graph where the node will be created
     * @param CreationInfo - The node creation information
     * @param OutErrorMessage - Error message if validation fails
     * @return True if the request is valid
     */
    bool ValidateNodeCreation(UEdGraph* Graph, const FNODECREATIONINFO& CreationInfo, FString& OutErrorMessage);

    // Node type registry methods

    /**
     * Registers a new node type
     * @param NodeTypeInfo - Information about the node type to register
     */
    void RegisterNodeType(const FNODETYPEINFO& NodeTypeInfo);

    /**
     * Unregisters a node type
     * @param NodeTypeName - Name of the node type to unregister
     */
    void UnregisterNodeType(const FString& NodeTypeName);

    /**
     * Gets information about a registered node type
     * @param NodeTypeName - Name of the node type
     * @return Information about the node type, or empty struct if not found
     */
    FNODETYPEINFO GetNodeTypeInfo(const FString& NodeTypeName);

    /**
     * Gets all registered node type names
     * @return Array of all registered node type names
     */
    TArray<FString> GetRegisteredNodeTypes();

    /**
     * Gets node type names by category
     * @param Category - The category to filter by
     * @return Array of node type names in the specified category
     */
    TArray<FString> GetNodeTypesByCategory(ENodeCategory Category);

    // Utility methods

    /**
     * Gets the class for a node type
     * @param NodeTypeName - Name of the node type
     * @return The class for the node type, or nullptr if not found
     */
    UClass* GetNodeClass(const FString& NodeTypeName);

    /**
     * Converts a node category enum to string
     * @param Category - The category to convert
     * @return String representation of the category
     */
    FString NodeCategoryToString(ENodeCategory Category);

    /**
     * Converts a string to a node category enum
     * @param CategoryString - The string to convert
     * @return The corresponding node category
     */
    ENodeCategory StringToNodeCategory(const FString& CategoryString);

    /**
     * Gets the default position for a new node
     * @param Graph - The graph where the node will be created
     * @return A suitable position for the new node
     */
    FVector2D GetDefaultNodePosition(UEdGraph* Graph);

    /**
     * Refreshes the node after creation or modification
     * @param Node - The node to refresh
     */
    void RefreshNode(UK2Node* Node);

private:
    // Helper methods

    /**
     * Creates a node of a specific class
     * @param Graph - The graph to create the node in
     * @param NodeClass - The class of node to create
     * @param Position - Position where the node should be created
     * @return The created node or nullptr if creation failed
     */
    UK2Node* CreateNodeOfClass(UEdGraph* Graph, UClass* NodeClass, const FVector2D& Position);

    /**
     * Initializes default values for a node
     * @param Node - The node to initialize
     * @param Properties - Properties to set on the node
     * @return True if initialization was successful
     */
    bool InitializeNodeDefaultValues(UK2Node* Node, const TMap<FString, FString>& Properties);

    /**
     * Finds a function by name and class
     * @param FunctionName - Name of the function to find
     * @param ClassName - Name of the class containing the function
     * @return The found function or nullptr if not found
     */
    UFunction* FindFunction(const FString& FunctionName, const FString& ClassName);

    /**
     * Finds a property by name in a blueprint
     * @param PropertyName - Name of the property to find
     * @param Blueprint - The blueprint to search in
     * @return The found property or nullptr if not found
     */
    FProperty* FindProperty(const FString& PropertyName, UBlueprint* Blueprint);

    /**
     * Gets the blueprint from a graph
     * @param Graph - The graph to get the blueprint from
     * @return The blueprint containing the graph
     */
    UBlueprint* GetBlueprintFromGraph(UEdGraph* Graph);

    /**
     * Initializes the built-in node type registry
     */
    void InitializeBuiltInNodeTypes();

    /**
     * Logs node creation activity
     * @param Message - The message to log
     * @param bIsError - Whether this is an error message
     */
    void LogNodeCreation(const FString& Message, bool bIsError = false);

    // Member variables

    /** Registry of all registered node types */
    TMap<FString, FNODETYPEINFO> RegisteredNodeTypes;

    /** Counter for generating unique node positions */
    int32 NodePositionCounter;

    /** Default spacing between nodes */
    float DefaultNodeSpacing;

    /** Whether to automatically refresh nodes after creation */
    bool bAutoRefreshNodes;

    /** Whether to show detailed logging */
    bool bVerboseLogging;
}; 