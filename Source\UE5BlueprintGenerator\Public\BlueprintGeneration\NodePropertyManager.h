#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "BlueprintGeneration/NodeFactory.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNodePropertyManager, Log, All);

/**
 * Enumeration for property data types
 */
UENUM(BlueprintType)
enum class EPropertyDataType : uint8
{
    Unknown         UMETA(DisplayName = "Unknown"),
    Boolean         UMETA(DisplayName = "Boolean"),
    Integer         UMETA(DisplayName = "Integer"),
    Float           UMETA(DisplayName = "Float"),
    String          UMETA(DisplayName = "String"),
    Name            UMETA(DisplayName = "Name"),
    Text            UMETA(DisplayName = "Text"),
    Vector          UMETA(DisplayName = "Vector"),
    Vector2D        UMETA(DisplayName = "Vector2D"),
    Rotator         UMETA(DisplayName = "Rotator"),
    Transform       UMETA(DisplayName = "Transform"),
    Color           UMETA(DisplayName = "Color"),
    LinearColor     UMETA(DisplayName = "Linear Color"),
    Object          UMETA(DisplayName = "Object Reference"),
    Class           UMETA(DisplayName = "Class Reference"),
    Enum            UMETA(DisplayName = "Enumeration"),
    Struct          UMETA(DisplayName = "Structure"),
    Array           UMETA(DisplayName = "Array"),
    Set             UMETA(DisplayName = "Set"),
    Map             UMETA(DisplayName = "Map"),
    Delegate        UMETA(DisplayName = "Delegate"),
    Interface       UMETA(DisplayName = "Interface")
};

/**
 * Enumeration for property validation levels
 */
UENUM(BlueprintType)
enum class EPropertyValidationLevel : uint8
{
    None            UMETA(DisplayName = "No Validation"),
    Basic           UMETA(DisplayName = "Basic Validation"),
    Strict          UMETA(DisplayName = "Strict Validation"),
    Custom          UMETA(DisplayName = "Custom Validation")
};

/**
 * Structure containing property metadata and configuration
 */
USTRUCT(BlueprintType)
struct FNODEPROPERTYINFO
{
    GENERATED_BODY()

    /** Property name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString PropertyName;

    /** Display name for UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString DisplayName;

    /** Property data type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    EPropertyDataType DataType;

    /** Default value as string */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString DefaultValue;

    /** Current value as string */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString CurrentValue;

    /** Property description */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString Description;

    /** Validation level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    EPropertyValidationLevel ValidationLevel;

    /** Validation rules (regex patterns, ranges, etc.) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    TArray<FString> ValidationRules;

    /** Whether this property is required */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    bool bIsRequired;

    /** Whether this property is read-only */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    bool bIsReadOnly;

    /** Whether this property is advanced (hidden by default) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    bool bIsAdvanced;

    /** Whether this property affects node reconstruction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    bool bRequiresReconstruction;

    /** Property category for organization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString Category;

    /** Tooltip text */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString ToolTip;

    /** Minimum value (for numeric types) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString MinValue;

    /** Maximum value (for numeric types) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FString MaxValue;

    /** Allowed values (for enum/choice types) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    TArray<FString> AllowedValues;

    /** Dependencies on other properties */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    TArray<FString> Dependencies;

    FNODEPROPERTYINFO()
        : DataType(EPropertyDataType::Unknown)
        , ValidationLevel(EPropertyValidationLevel::Basic)
        , bIsRequired(false)
        , bIsReadOnly(false)
        , bIsAdvanced(false)
        , bRequiresReconstruction(false)
    {
    }
};

/**
 * Structure for property validation results
 */
USTRUCT(BlueprintType)
struct FPROPERTYVALIDATIONRESULT
{
    GENERATED_BODY()

    /** Whether validation passed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bIsValid;

    /** Validation error messages */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    TArray<FString> ErrorMessages;

    /** Validation warning messages */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    TArray<FString> WarningMessages;

    /** Suggested corrections */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    TArray<FString> Suggestions;

    /** Validation execution time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    float ValidationTimeMs;

    FPROPERTYVALIDATIONRESULT()
        : bIsValid(true)
        , ValidationTimeMs(0.0f)
    {
    }
};

/**
 * Structure for property configuration request
 */
USTRUCT(BlueprintType)
struct FPROPERTYCONFIGUATIONREQUEST
{
    GENERATED_BODY()

    /** Target node to configure */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    UK2Node* TargetNode;

    /** Properties to configure */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<FString, FString> PropertyValues;

    /** Whether to validate before applying */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bValidateBeforeApply;

    /** Whether to reconstruct node after configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bReconstructAfterApply;

    /** Whether to apply default values for unspecified properties */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bApplyDefaults;

    FPROPERTYCONFIGUATIONREQUEST()
        : TargetNode(nullptr)
        , bValidateBeforeApply(true)
        , bReconstructAfterApply(true)
        , bApplyDefaults(false)
    {
    }
};

/**
 * Structure for property configuration result
 */
USTRUCT(BlueprintType)
struct FPROPERTYCONFIGURATIONRESULT
{
    GENERATED_BODY()

    /** Whether configuration was successful */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bSuccess;

    /** Properties that were successfully configured */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FString> ConfiguredProperties;

    /** Properties that failed to configure */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FString> FailedProperties;

    /** Error messages */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FString> ErrorMessages;

    /** Warning messages */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FString> WarningMessages;

    /** Configuration execution time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float ConfigurationTimeMs;

    FPROPERTYCONFIGURATIONRESULT()
        : bSuccess(false)
        , ConfigurationTimeMs(0.0f)
    {
    }
};

/**
 * Class responsible for managing node properties and their configuration
 */
class UE5BLUEPRINTGENERATOR_API FNodePropertyManager
{
public:
    FNodePropertyManager();
    ~FNodePropertyManager();

    // Initialization and management

    /**
     * Initializes the property manager
     * @return True if initialization was successful
     */
    bool Initialize();

    /**
     * Shuts down the property manager
     */
    void Shutdown();

    // Property discovery and introspection

    /**
     * Gets all properties for a node type
     * @param NodeClass - The node class to get properties for
     * @return Array of property information
     */
    TArray<FNODEPROPERTYINFO> GetNodeProperties(UClass* NodeClass);

    /**
     * Gets all properties for a specific node instance
     * @param Node - The node instance to get properties for
     * @return Array of property information with current values
     */
    TArray<FNODEPROPERTYINFO> GetNodeInstanceProperties(UK2Node* Node);

    /**
     * Gets a specific property for a node
     * @param Node - The node to get the property from
     * @param PropertyName - The name of the property
     * @return Property information, or empty if not found
     */
    FNODEPROPERTYINFO GetNodeProperty(UK2Node* Node, const FString& PropertyName);

    /**
     * Checks if a node has a specific property
     * @param Node - The node to check
     * @param PropertyName - The name of the property
     * @return True if the property exists
     */
    bool HasNodeProperty(UK2Node* Node, const FString& PropertyName);

    // Property configuration

    /**
     * Configures multiple properties on a node
     * @param ConfigRequest - The configuration request
     * @return Configuration result with success/failure details
     */
    FPROPERTYCONFIGURATIONRESULT ConfigureNodeProperties(const FPROPERTYCONFIGUATIONREQUEST& ConfigRequest);

    /**
     * Sets a single property on a node
     * @param Node - The node to configure
     * @param PropertyName - The name of the property
     * @param PropertyValue - The value to set (as string)
     * @param bValidate - Whether to validate the value before setting
     * @return True if the property was set successfully
     */
    bool SetNodeProperty(UK2Node* Node, const FString& PropertyName, const FString& PropertyValue, bool bValidate = true);

    /**
     * Gets a property value from a node
     * @param Node - The node to get the property from
     * @param PropertyName - The name of the property
     * @return The property value as string, or empty if not found
     */
    FString GetNodePropertyValue(UK2Node* Node, const FString& PropertyName);

    /**
     * Resets a property to its default value
     * @param Node - The node to reset the property on
     * @param PropertyName - The name of the property
     * @return True if the property was reset successfully
     */
    bool ResetNodeProperty(UK2Node* Node, const FString& PropertyName);

    /**
     * Applies default values to all properties of a node
     * @param Node - The node to apply defaults to
     * @return Number of properties that had defaults applied
     */
    int32 ApplyDefaultValues(UK2Node* Node);

    // Property validation

    /**
     * Validates a property value
     * @param PropertyInfo - The property information
     * @param Value - The value to validate
     * @return Validation result
     */
    FPROPERTYVALIDATIONRESULT ValidatePropertyValue(const FNODEPROPERTYINFO& PropertyInfo, const FString& Value);

    /**
     * Validates all properties of a node
     * @param Node - The node to validate
     * @return Validation result for all properties
     */
    FPROPERTYVALIDATIONRESULT ValidateNodeProperties(UK2Node* Node);

    /**
     * Validates property dependencies
     * @param Node - The node to validate dependencies for
     * @param PropertyName - The property to check dependencies for
     * @return True if all dependencies are satisfied
     */
    bool ValidatePropertyDependencies(UK2Node* Node, const FString& PropertyName);

    // Property type conversion

    /**
     * Converts a string value to the appropriate property type
     * @param Value - The string value to convert
     * @param DataType - The target data type
     * @param OutConvertedValue - The converted value (as void pointer)
     * @return True if conversion was successful
     */
    bool ConvertStringToPropertyType(const FString& Value, EPropertyDataType DataType, void* OutConvertedValue);

    /**
     * Converts a property value to string representation
     * @param PropertyValue - The property value (as void pointer)
     * @param DataType - The data type of the property
     * @return String representation of the value
     */
    FString ConvertPropertyTypeToString(const void* PropertyValue, EPropertyDataType DataType);

    /**
     * Gets the data type of a property
     * @param Property - The UE5 property to analyze
     * @return The corresponding data type enum
     */
    EPropertyDataType GetPropertyDataType(FProperty* Property);

    // Property templates and presets

    /**
     * Saves a property configuration as a template
     * @param TemplateName - The name of the template
     * @param Node - The node to save configuration from
     * @return True if template was saved successfully
     */
    bool SavePropertyTemplate(const FString& TemplateName, UK2Node* Node);

    /**
     * Loads a property configuration from a template
     * @param TemplateName - The name of the template
     * @param Node - The node to apply configuration to
     * @return True if template was loaded and applied successfully
     */
    bool LoadPropertyTemplate(const FString& TemplateName, UK2Node* Node);

    /**
     * Gets all available property templates
     * @return Array of template names
     */
    TArray<FString> GetAvailableTemplates();

    /**
     * Deletes a property template
     * @param TemplateName - The name of the template to delete
     * @return True if template was deleted successfully
     */
    bool DeletePropertyTemplate(const FString& TemplateName);

    // Property constraints and rules

    /**
     * Adds a validation rule for a property type
     * @param DataType - The data type to add the rule for
     * @param RuleName - The name of the rule
     * @param RulePattern - The validation pattern (regex or custom)
     * @param ErrorMessage - The error message to show if validation fails
     */
    void AddValidationRule(EPropertyDataType DataType, const FString& RuleName, const FString& RulePattern, const FString& ErrorMessage);

    /**
     * Removes a validation rule
     * @param DataType - The data type to remove the rule from
     * @param RuleName - The name of the rule to remove
     */
    void RemoveValidationRule(EPropertyDataType DataType, const FString& RuleName);

    /**
     * Gets all validation rules for a data type
     * @param DataType - The data type to get rules for
     * @return Map of rule names to patterns
     */
    TMap<FString, FString> GetValidationRules(EPropertyDataType DataType);

    // Utility methods

    /**
     * Gets the string representation of a property data type
     * @param DataType - The data type to convert
     * @return String representation
     */
    FString PropertyDataTypeToString(EPropertyDataType DataType);

    /**
     * Converts string to property data type
     * @param DataTypeString - The string to convert
     * @return Corresponding data type enum
     */
    EPropertyDataType StringToPropertyDataType(const FString& DataTypeString);

    /**
     * Gets the string representation of a validation level
     * @param ValidationLevel - The validation level to convert
     * @return String representation
     */
    FString ValidationLevelToString(EPropertyValidationLevel ValidationLevel);

    /**
     * Converts string to validation level
     * @param ValidationLevelString - The string to convert
     * @return Corresponding validation level enum
     */
    EPropertyValidationLevel StringToValidationLevel(const FString& ValidationLevelString);

    /**
     * Gets property manager statistics
     * @return Map of statistics
     */
    TMap<FString, FString> GetPropertyManagerStatistics();

private:
    // Property discovery helpers

    /**
     * Analyzes a UE5 property and creates property info
     * @param Property - The UE5 property to analyze
     * @param Node - The node instance (optional)
     * @return Property information structure
     */
    FNODEPROPERTYINFO AnalyzeProperty(FProperty* Property, UK2Node* Node = nullptr);

    /**
     * Gets the default value for a property
     * @param Property - The UE5 property
     * @param Node - The node instance
     * @return Default value as string
     */
    FString GetPropertyDefaultValue(FProperty* Property, UK2Node* Node);

    /**
     * Gets the current value of a property
     * @param Property - The UE5 property
     * @param Node - The node instance
     * @return Current value as string
     */
    FString GetPropertyCurrentValue(FProperty* Property, UK2Node* Node);

    // Property setting helpers

    /**
     * Sets a boolean property
     * @param Property - The property to set
     * @param Node - The node instance
     * @param Value - The value to set
     * @return True if successful
     */
    bool SetBooleanProperty(FBoolProperty* Property, UK2Node* Node, const FString& Value);

    /**
     * Sets an integer property
     * @param Property - The property to set
     * @param Node - The node instance
     * @param Value - The value to set
     * @return True if successful
     */
    bool SetIntegerProperty(FIntProperty* Property, UK2Node* Node, const FString& Value);

    /**
     * Sets a float property
     * @param Property - The property to set
     * @param Node - The node instance
     * @param Value - The value to set
     * @return True if successful
     */
    bool SetFloatProperty(FFloatProperty* Property, UK2Node* Node, const FString& Value);

    /**
     * Sets a string property
     * @param Property - The property to set
     * @param Node - The node instance
     * @param Value - The value to set
     * @return True if successful
     */
    bool SetStringProperty(FStrProperty* Property, UK2Node* Node, const FString& Value);

    /**
     * Sets an object property
     * @param Property - The property to set
     * @param Node - The node instance
     * @param Value - The value to set
     * @return True if successful
     */
    bool SetObjectProperty(FObjectProperty* Property, UK2Node* Node, const FString& Value);

    // Validation helpers

    /**
     * Validates a boolean value
     * @param Value - The value to validate
     * @param PropertyInfo - The property information
     * @return Validation result
     */
    FPROPERTYVALIDATIONRESULT ValidateBooleanValue(const FString& Value, const FNODEPROPERTYINFO& PropertyInfo);

    /**
     * Validates a numeric value
     * @param Value - The value to validate
     * @param PropertyInfo - The property information
     * @return Validation result
     */
    FPROPERTYVALIDATIONRESULT ValidateNumericValue(const FString& Value, const FNODEPROPERTYINFO& PropertyInfo);

    /**
     * Validates a string value
     * @param Value - The value to validate
     * @param PropertyInfo - The property information
     * @return Validation result
     */
    FPROPERTYVALIDATIONRESULT ValidateStringValue(const FString& Value, const FNODEPROPERTYINFO& PropertyInfo);

    /**
     * Validates using custom rules
     * @param Value - The value to validate
     * @param Rules - The validation rules to apply
     * @return Validation result
     */
    FPROPERTYVALIDATIONRESULT ValidateWithCustomRules(const FString& Value, const TArray<FString>& Rules);

    // Template management

    /**
     * Gets the template file path
     * @param TemplateName - The name of the template
     * @return Full file path to the template
     */
    FString GetTemplateFilePath(const FString& TemplateName);

    /**
     * Loads template data from file
     * @param FilePath - The file path to load from
     * @return Map of property names to values
     */
    TMap<FString, FString> LoadTemplateFromFile(const FString& FilePath);

    /**
     * Saves template data to file
     * @param FilePath - The file path to save to
     * @param PropertyValues - The property values to save
     * @return True if successful
     */
    bool SaveTemplateToFile(const FString& FilePath, const TMap<FString, FString>& PropertyValues);

    // Logging and statistics

    /**
     * Logs property manager activity
     * @param Message - The message to log
     * @param bIsError - Whether this is an error message
     */
    void LogPropertyActivity(const FString& Message, bool bIsError = false);

    /**
     * Updates property manager statistics
     * @param Operation - The operation that was performed
     * @param PropertyName - The property involved (optional)
     */
    void UpdatePropertyStatistics(const FString& Operation, const FString& PropertyName = TEXT(""));

    // Member variables

    /** Whether the property manager has been initialized */
    bool bIsInitialized;

    /** Whether to enable verbose logging */
    bool bVerboseLogging;

    /** Whether to track property statistics */
    bool bTrackPropertyStatistics;

    /** Validation rules for different data types */
    TMap<EPropertyDataType, TMap<FString, TPair<FString, FString>>> ValidationRules;

    /** Property manager statistics */
    TMap<FString, int32> PropertyStatistics;

    /** Template directory path */
    FString TemplateDirectory;

    /** Total number of property operations */
    int32 TotalPropertyOperations;

    /** Total number of validation operations */
    int32 TotalValidationOperations;
}; 