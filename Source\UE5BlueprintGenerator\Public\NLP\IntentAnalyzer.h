#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

/**
 * Enumeration of different user intent types
 */
UENUM(BlueprintType)
enum class EUserIntentType : uint8
{
	Unknown			UMETA(DisplayName = "Unknown"),
	Create			UMETA(DisplayName = "Create"),
	Modify			UMETA(DisplayName = "Modify"),
	Debug			UMETA(DisplayName = "Debug"),
	Optimize		UMETA(DisplayName = "Optimize"),
	Learn			UMETA(DisplayName = "Learn"),
	Convert			UMETA(DisplayName = "Convert"),
	Fix				UMETA(DisplayName = "Fix"),
	Enhance			UMETA(DisplayName = "Enhance")
};

/**
 * Enumeration of different action types
 */
UENUM(BlueprintType)
enum class EActionType : uint8
{
	Unknown				UMETA(DisplayName = "Unknown"),
	AddComponent		UMETA(DisplayName = "Add Component"),
	CreateFunction		UMETA(DisplayName = "Create Function"),
	HandleEvent			UMETA(DisplayName = "Handle Event"),
	ManageState			UMETA(DisplayName = "Manage State"),
	ProcessData			UMETA(DisplayName = "Process Data"),
	SetupTimer			UMETA(DisplayName = "Setup Timer"),
	ConfigureInput		UMETA(DisplayName = "Configure Input"),
	ControlAnimation	UMETA(DisplayName = "Control Animation"),
	ImplementAI			UMETA(DisplayName = "Implement AI"),
	SetupNetworking		UMETA(DisplayName = "Setup Networking"),
	CreateUI			UMETA(DisplayName = "Create UI"),
	AddGameplay			UMETA(DisplayName = "Add Gameplay"),
	CreateUtility		UMETA(DisplayName = "Create Utility")
};

/**
 * Enumeration of different target objects
 */
UENUM(BlueprintType)
enum class ETargetObjectType : uint8
{
	Unknown			UMETA(DisplayName = "Unknown"),
	Actor			UMETA(DisplayName = "Actor"),
	Component		UMETA(DisplayName = "Component"),
	Function		UMETA(DisplayName = "Function"),
	Variable		UMETA(DisplayName = "Variable"),
	Event			UMETA(DisplayName = "Event"),
	Interface		UMETA(DisplayName = "Interface"),
	Widget			UMETA(DisplayName = "Widget"),
	GameMode		UMETA(DisplayName = "Game Mode"),
	PlayerController UMETA(DisplayName = "Player Controller"),
	AIController	UMETA(DisplayName = "AI Controller"),
	Animation		UMETA(DisplayName = "Animation"),
	Material		UMETA(DisplayName = "Material"),
	Sound			UMETA(DisplayName = "Sound")
};

/**
 * Structure representing extracted parameters from user input
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FExtractedParameter
{
	GENERATED_BODY()

	/** Parameter name */
	UPROPERTY(BlueprintReadOnly)
	FString Name;

	/** Parameter type */
	UPROPERTY(BlueprintReadOnly)
	FString Type;

	/** Parameter value (if specified) */
	UPROPERTY(BlueprintReadOnly)
	FString Value;

	/** Whether this parameter is required */
	UPROPERTY(BlueprintReadOnly)
	bool bRequired;

	/** Parameter description */
	UPROPERTY(BlueprintReadOnly)
	FString Description;

	FExtractedParameter()
		: bRequired(false)
	{
	}

	FExtractedParameter(const FString& InName, const FString& InType, const FString& InValue = TEXT(""), bool bInRequired = false)
		: Name(InName)
		, Type(InType)
		, Value(InValue)
		, bRequired(bInRequired)
	{
	}
};

/**
 * Structure representing the result of intent analysis
 */
USTRUCT(BlueprintType)
struct UE5BLUEPRINTGENERATOR_API FIntentAnalysisResult
{
	GENERATED_BODY()

	/** Primary user intent */
	UPROPERTY(BlueprintReadOnly)
	EUserIntentType PrimaryIntent;

	/** Confidence in the primary intent (0.0 to 1.0) */
	UPROPERTY(BlueprintReadOnly)
	float IntentConfidence;

	/** Primary action to be performed */
	UPROPERTY(BlueprintReadOnly)
	EActionType PrimaryAction;

	/** Confidence in the primary action (0.0 to 1.0) */
	UPROPERTY(BlueprintReadOnly)
	float ActionConfidence;

	/** Target object type */
	UPROPERTY(BlueprintReadOnly)
	ETargetObjectType TargetObject;

	/** Specific target name (if mentioned) */
	UPROPERTY(BlueprintReadOnly)
	FString TargetName;

	/** Extracted parameters from the input */
	UPROPERTY(BlueprintReadOnly)
	TArray<FExtractedParameter> Parameters;

	/** Additional context information */
	UPROPERTY(BlueprintReadOnly)
	TMap<FString, FString> ContextData;

	/** Secondary intents detected */
	UPROPERTY(BlueprintReadOnly)
	TArray<EUserIntentType> SecondaryIntents;

	/** Whether the intent requires user interaction */
	UPROPERTY(BlueprintReadOnly)
	bool bRequiresUserInput;

	/** Estimated complexity of the requested action */
	UPROPERTY(BlueprintReadOnly)
	float EstimatedComplexity;

	FIntentAnalysisResult()
		: PrimaryIntent(EUserIntentType::Unknown)
		, IntentConfidence(0.0f)
		, PrimaryAction(EActionType::Unknown)
		, ActionConfidence(0.0f)
		, TargetObject(ETargetObjectType::Unknown)
		, bRequiresUserInput(false)
		, EstimatedComplexity(1.0f)
	{
	}
};

/**
 * Analyzes user intent from natural language input
 * Determines what the user wants to accomplish and how to achieve it
 */
class UE5BLUEPRINTGENERATOR_API FIntentAnalyzer
{
public:
	FIntentAnalyzer();
	~FIntentAnalyzer();

	// Intent analysis
	FIntentAnalysisResult AnalyzeIntent(const FString& InputText);
	EUserIntentType GetPrimaryIntent(const FString& InputText);
	EActionType GetPrimaryAction(const FString& InputText);
	ETargetObjectType GetTargetObject(const FString& InputText);

	// Parameter extraction
	TArray<FExtractedParameter> ExtractParameters(const FString& InputText);
	TMap<FString, FString> ExtractContextData(const FString& InputText);
	FString ExtractTargetName(const FString& InputText);

	// Intent classification
	float CalculateIntentConfidence(const FString& InputText, EUserIntentType IntentType);
	float CalculateActionConfidence(const FString& InputText, EActionType ActionType);
	bool RequiresUserInput(const FString& InputText);
	float EstimateComplexity(const FString& InputText);

	// Intent information
	TArray<FString> GetIntentKeywords(EUserIntentType IntentType) const;
	TArray<FString> GetActionKeywords(EActionType ActionType) const;
	TArray<FString> GetTargetKeywords(ETargetObjectType TargetType) const;

	// Configuration
	void SetIntentThreshold(float Threshold);
	void SetActionThreshold(float Threshold);
	void AddCustomIntentKeywords(EUserIntentType IntentType, const TArray<FString>& Keywords);
	void AddCustomActionKeywords(EActionType ActionType, const TArray<FString>& Keywords);

private:
	// Intent databases
	TMap<EUserIntentType, TArray<FString>> IntentKeywords;
	TMap<EActionType, TArray<FString>> ActionKeywords;
	TMap<ETargetObjectType, TArray<FString>> TargetKeywords;

	// Parameter patterns
	TMap<FString, FString> ParameterPatterns;
	TArray<FString> RequiredParameterIndicators;
	TArray<FString> OptionalParameterIndicators;

	// Configuration
	float IntentThreshold;
	float ActionThreshold;

	// Analysis methods
	TArray<TPair<EUserIntentType, float>> RankIntents(const FString& InputText);
	TArray<TPair<EActionType, float>> RankActions(const FString& InputText);
	TArray<TPair<ETargetObjectType, float>> RankTargets(const FString& InputText);

	// Keyword matching
	int32 CountKeywordMatches(const FString& InputText, const TArray<FString>& Keywords) const;
	float CalculateKeywordDensity(const FString& InputText, const TArray<FString>& Keywords) const;
	bool ContainsKeyword(const FString& InputText, const FString& Keyword) const;

	// Parameter extraction methods
	FExtractedParameter ExtractNumericParameter(const FString& InputText, const FString& ParameterName) const;
	FExtractedParameter ExtractStringParameter(const FString& InputText, const FString& ParameterName) const;
	FExtractedParameter ExtractBooleanParameter(const FString& InputText, const FString& ParameterName) const;
	TArray<FString> ExtractNamedEntities(const FString& InputText) const;

	// Context analysis
	bool IsCreationIntent(const FString& InputText) const;
	bool IsModificationIntent(const FString& InputText) const;
	bool IsDebuggingIntent(const FString& InputText) const;
	bool IsOptimizationIntent(const FString& InputText) const;
	bool IsLearningIntent(const FString& InputText) const;

	// Initialization methods
	void InitializeIntentKeywords();
	void InitializeActionKeywords();
	void InitializeTargetKeywords();
	void InitializeParameterPatterns();

	// Helper methods
	TArray<FString> TokenizeInput(const FString& InputText) const;
	FString NormalizeText(const FString& Text) const;
	bool IsQuestionWord(const FString& Word) const;
	bool IsActionVerb(const FString& Word) const;
	bool IsTargetNoun(const FString& Word) const;
}; 