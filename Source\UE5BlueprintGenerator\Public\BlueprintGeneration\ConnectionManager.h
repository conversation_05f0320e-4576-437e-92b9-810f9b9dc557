#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "EdGraph/EdGraph.h"
#include "BlueprintGeneration/PinCompatibilityChecker.h"

DECLARE_LOG_CATEGORY_EXTERN(LogConnectionManager, Log, All);

// Forward declarations
class UBlueprint;
class UEdGraphNode;
class UEdGraphPin;
class UEdGraph;

/**
 * Enumeration for connection operation types
 */
UENUM(BlueprintType)
enum class EConnectionOperationType : uint8
{
    Connect,
    Disconnect,
    Reconnect,
    ValidateConnection,
    BatchConnect,
    AutoConnect
};

/**
 * Enumeration for connection strategies
 */
UENUM(BlueprintType)
enum class EConnectionStrategy : uint8
{
    Direct,           // Direct pin-to-pin connection
    WithConversion,   // Connection with automatic conversion node insertion
    BestMatch,        // Find best matching pins automatically
    ForceConnect,     // Force connection even if not ideal
    SmartConnect      // Intelligent connection with multiple strategies
};

/**
 * Enumeration for auto-connection modes
 */
UENUM(BlueprintType)
enum class EAutoConnectionMode : uint8
{
    None,             // No auto-connection
    ByName,           // Connect pins with matching names
    ByType,           // Connect pins with compatible types
    ByPosition,       // Connect pins based on position/order
    Intelligent       // Use AI-like logic for best connections
};

/**
 * Structure representing a connection request
 */
USTRUCT(BlueprintType)
struct FCONNECTIONREQUEST
{
    GENERATED_BODY()

    /** Source pin for the connection */
    UPROPERTY()
    UEdGraphPin* SourcePin = nullptr;

    /** Target pin for the connection */
    UPROPERTY()
    UEdGraphPin* TargetPin = nullptr;

    /** Connection strategy to use */
    UPROPERTY()
    EConnectionStrategy Strategy = EConnectionStrategy::Direct;

    /** Whether to allow conversion nodes */
    UPROPERTY()
    bool bAllowConversionNodes = true;

    /** Whether to validate before connecting */
    UPROPERTY()
    bool bValidateBeforeConnect = true;

    /** Whether to force connection even if validation fails */
    UPROPERTY()
    bool bForceConnection = false;

    /** Custom conversion node to use (if specified) */
    UPROPERTY()
    FString CustomConversionNode;

    /** Additional metadata for the connection */
    UPROPERTY()
    TMap<FString, FString> Metadata;

    FCONNECTIONREQUEST()
    {
        SourcePin = nullptr;
        TargetPin = nullptr;
        Strategy = EConnectionStrategy::Direct;
        bAllowConversionNodes = true;
        bValidateBeforeConnect = true;
        bForceConnection = false;
    }
};

/**
 * Structure representing a connection result
 */
USTRUCT(BlueprintType)
struct FCONNECTIONRESULT
{
    GENERATED_BODY()

    /** Whether the connection was successful */
    UPROPERTY()
    bool bSuccess = false;

    /** Source pin that was connected */
    UPROPERTY()
    UEdGraphPin* SourcePin = nullptr;

    /** Target pin that was connected */
    UPROPERTY()
    UEdGraphPin* TargetPin = nullptr;

    /** Conversion node that was created (if any) */
    UPROPERTY()
    UEdGraphNode* ConversionNode = nullptr;

    /** Strategy that was used for the connection */
    UPROPERTY()
    EConnectionStrategy UsedStrategy = EConnectionStrategy::Direct;

    /** Time taken for the connection operation (in milliseconds) */
    UPROPERTY()
    float OperationTimeMs = 0.0f;

    /** Error messages if connection failed */
    UPROPERTY()
    TArray<FString> ErrorMessages;

    /** Warning messages for the connection */
    UPROPERTY()
    TArray<FString> WarningMessages;

    /** Additional information about the connection */
    UPROPERTY()
    TArray<FString> InfoMessages;

    /** Metadata about the connection operation */
    UPROPERTY()
    TMap<FString, FString> Metadata;

    FCONNECTIONRESULT()
    {
        bSuccess = false;
        SourcePin = nullptr;
        TargetPin = nullptr;
        ConversionNode = nullptr;
        UsedStrategy = EConnectionStrategy::Direct;
        OperationTimeMs = 0.0f;
    }
};

/**
 * Structure for batch connection requests
 */
USTRUCT(BlueprintType)
struct FBATCHCONNECTIONREQUEST
{
    GENERATED_BODY()

    /** Array of connection requests */
    UPROPERTY()
    TArray<FCONNECTIONREQUEST> ConnectionRequests;

    /** Whether to stop on first failure */
    UPROPERTY()
    bool bStopOnFirstFailure = false;

    /** Maximum time to spend on batch operation (in milliseconds) */
    UPROPERTY()
    float MaxOperationTimeMs = 5000.0f;

    /** Whether to validate all connections before executing any */
    UPROPERTY()
    bool bValidateAllFirst = true;

    /** Whether to rollback on failure */
    UPROPERTY()
    bool bRollbackOnFailure = true;

    FBATCHCONNECTIONREQUEST()
    {
        bStopOnFirstFailure = false;
        MaxOperationTimeMs = 5000.0f;
        bValidateAllFirst = true;
        bRollbackOnFailure = true;
    }
};

/**
 * Structure for batch connection results
 */
USTRUCT(BlueprintType)
struct FBATCHCONNECTIONRESULT
{
    GENERATED_BODY()

    /** Whether the entire batch operation was successful */
    UPROPERTY()
    bool bSuccess = false;

    /** Individual connection results */
    UPROPERTY()
    TArray<FCONNECTIONRESULT> Results;

    /** Number of successful connections */
    UPROPERTY()
    int32 SuccessfulConnections = 0;

    /** Number of failed connections */
    UPROPERTY()
    int32 FailedConnections = 0;

    /** Number of connections that required conversion nodes */
    UPROPERTY()
    int32 ConversionNodesCreated = 0;

    /** Total time for the batch operation (in milliseconds) */
    UPROPERTY()
    float TotalOperationTimeMs = 0.0f;

    /** Overall error messages */
    UPROPERTY()
    TArray<FString> ErrorMessages;

    /** Overall warning messages */
    UPROPERTY()
    TArray<FString> WarningMessages;

    /** Nodes that were created during the batch operation */
    UPROPERTY()
    TArray<UEdGraphNode*> CreatedNodes;

    FBATCHCONNECTIONRESULT()
    {
        bSuccess = false;
        SuccessfulConnections = 0;
        FailedConnections = 0;
        ConversionNodesCreated = 0;
        TotalOperationTimeMs = 0.0f;
    }
};

/**
 * Structure for auto-connection configuration
 */
USTRUCT(BlueprintType)
struct FAUTOCONNECTIONCONFIG
{
    GENERATED_BODY()

    /** Auto-connection mode to use */
    UPROPERTY()
    EAutoConnectionMode Mode = EAutoConnectionMode::Intelligent;

    /** Whether to connect execution pins automatically */
    UPROPERTY()
    bool bConnectExecutionPins = true;

    /** Whether to connect data pins automatically */
    UPROPERTY()
    bool bConnectDataPins = true;

    /** Whether to allow conversion nodes in auto-connection */
    UPROPERTY()
    bool bAllowConversionNodes = true;

    /** Minimum compatibility score for auto-connection (0.0-1.0) */
    UPROPERTY()
    float MinCompatibilityScore = 0.6f;

    /** Maximum distance for position-based auto-connection */
    UPROPERTY()
    float MaxConnectionDistance = 500.0f;

    /** Whether to prefer exact type matches */
    UPROPERTY()
    bool bPreferExactMatches = true;

    /** Whether to connect optional pins */
    UPROPERTY()
    bool bConnectOptionalPins = false;

    FAUTOCONNECTIONCONFIG()
    {
        Mode = EAutoConnectionMode::Intelligent;
        bConnectExecutionPins = true;
        bConnectDataPins = true;
        bAllowConversionNodes = true;
        MinCompatibilityScore = 0.6f;
        MaxConnectionDistance = 500.0f;
        bPreferExactMatches = true;
        bConnectOptionalPins = false;
    }
};

/**
 * Connection Manager class for handling pin connections in blueprint graphs
 */
class FCONNECTIONMANAGER
{
public:
    /** Constructor */
    FCONNECTIONMANAGER();

    /** Destructor */
    ~FCONNECTIONMANAGER();

    // Initialization and Configuration
    
    /** Initialize the connection manager */
    bool Initialize();

    /** Shutdown the connection manager */
    void Shutdown();

    /** Check if the connection manager is initialized */
    bool IsInitialized() const { return bIsInitialized; }

    /** Set the pin compatibility checker to use */
    void SetPinCompatibilityChecker(TSharedPtr<FPinCompatibilityChecker> InCompatibilityChecker);

    // Single Connection Operations

    /** Connect two pins directly */
    FCONNECTIONRESULT ConnectPins(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Connect two pins with specified strategy */
    FCONNECTIONRESULT ConnectPins(const FCONNECTIONREQUEST& Request);

    /** Disconnect two pins */
    FCONNECTIONRESULT DisconnectPins(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Disconnect all connections from a pin */
    FCONNECTIONRESULT DisconnectPin(UEdGraphPin* Pin);

    /** Reconnect a pin to a new target */
    FCONNECTIONRESULT ReconnectPin(UEdGraphPin* SourcePin, UEdGraphPin* OldTarget, UEdGraphPin* NewTarget);

    // Batch Connection Operations

    /** Execute multiple connection requests */
    FBATCHCONNECTIONRESULT ExecuteBatchConnections(const FBATCHCONNECTIONREQUEST& BatchRequest);

    /** Connect multiple pin pairs */
    FBATCHCONNECTIONRESULT ConnectPinPairs(const TArray<TPair<UEdGraphPin*, UEdGraphPin*>>& PinPairs);

    // Auto-Connection Features

    /** Auto-connect nodes in a graph */
    FBATCHCONNECTIONRESULT AutoConnectNodes(UEdGraph* Graph, const FAUTOCONNECTIONCONFIG& Config);

    /** Auto-connect two specific nodes */
    FBATCHCONNECTIONRESULT AutoConnectNodes(UEdGraphNode* SourceNode, UEdGraphNode* TargetNode, const FAUTOCONNECTIONCONFIG& Config);

    /** Find and suggest connections for a node */
    TArray<FCONNECTIONREQUEST> SuggestConnections(UEdGraphNode* Node, UEdGraph* Graph, const FAUTOCONNECTIONCONFIG& Config);

    // Connection Validation

    /** Validate a connection request */
    bool ValidateConnectionRequest(const FCONNECTIONREQUEST& Request, TArray<FString>& OutErrors);

    /** Validate all connections in a graph */
    TArray<FString> ValidateGraphConnections(UEdGraph* Graph);

    /** Check if two pins can be connected */
    bool CanConnect(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    // Conversion Node Management

    /** Create a conversion node between two pins */
    UEdGraphNode* CreateConversionNode(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, const FString& ConversionNodeType = FString());

    /** Get suggested conversion node type for two pins */
    FString GetSuggestedConversionNodeType(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);

    /** Remove conversion nodes that are no longer needed */
    int32 CleanupUnusedConversionNodes(UEdGraph* Graph);

    // Connection Analysis

    /** Get all connections in a graph */
    TArray<TPair<UEdGraphPin*, UEdGraphPin*>> GetAllConnections(UEdGraph* Graph);

    /** Get connections for a specific node */
    TArray<TPair<UEdGraphPin*, UEdGraphPin*>> GetNodeConnections(UEdGraphNode* Node);

    /** Find disconnected pins in a graph */
    TArray<UEdGraphPin*> FindDisconnectedPins(UEdGraph* Graph);

    /** Analyze connection quality in a graph */
    float AnalyzeConnectionQuality(UEdGraph* Graph);

    // Utility Functions

    /** Get connection statistics */
    TMap<FString, FString> GetConnectionStatistics();

    /** Set verbose logging */
    void SetVerboseLogging(bool bEnabled);

    /** Set statistics tracking */
    void SetTrackStatistics(bool bEnabled);

    /** Reset statistics */
    void ResetStatistics();

private:
    // Internal state
    bool bIsInitialized;
    bool bVerboseLogging;
    bool bTrackStatistics;

    // Dependencies
    TSharedPtr<FPinCompatibilityChecker> CompatibilityChecker;

    // Statistics
    int32 TotalConnections;
    int32 TotalDisconnections;
    int32 TotalBatchOperations;
    int32 TotalAutoConnections;
    int32 TotalConversionNodesCreated;
    float LastOperationTimeMs;
    TMap<FString, int32> ConnectionStatistics;

    // Internal connection methods
    FCONNECTIONRESULT ConnectPinsInternal(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, EConnectionStrategy Strategy, bool bAllowConversion);
    FCONNECTIONRESULT DisconnectPinsInternal(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);
    UEdGraphNode* CreateConversionNodeInternal(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, const FString& ConversionNodeType);

    // Auto-connection helpers
    TArray<FCONNECTIONREQUEST> FindConnectionsByName(UEdGraphNode* SourceNode, UEdGraphNode* TargetNode, const FAUTOCONNECTIONCONFIG& Config);
    TArray<FCONNECTIONREQUEST> FindConnectionsByType(UEdGraphNode* SourceNode, UEdGraphNode* TargetNode, const FAUTOCONNECTIONCONFIG& Config);
    TArray<FCONNECTIONREQUEST> FindConnectionsByPosition(UEdGraphNode* SourceNode, UEdGraphNode* TargetNode, const FAUTOCONNECTIONCONFIG& Config);
    TArray<FCONNECTIONREQUEST> FindIntelligentConnections(UEdGraphNode* SourceNode, UEdGraphNode* TargetNode, const FAUTOCONNECTIONCONFIG& Config);

    // Validation helpers
    bool ValidatePinDirection(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);
    bool ValidatePinCompatibility(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);
    bool ValidateConnectionStrategy(const FCONNECTIONREQUEST& Request);

    // Utility helpers
    float CalculateConnectionDistance(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin);
    float ScoreConnection(UEdGraphPin* SourcePin, UEdGraphPin* TargetPin, const FAUTOCONNECTIONCONFIG& Config);
    void LogConnectionActivity(const FString& Message, bool bIsError = false);
    void UpdateConnectionStatistics(const FString& Operation, const FString& ConnectionType = FString());
}; 