#include "BlueprintGeneration/AssemblyValidator.h"
#include "Engine/Engine.h"

DEFINE_LOG_CATEGORY(LogAssemblyValidator);

FAssemblyValidator::FAssemblyValidator()
{
    InitializeComponents();
}

FAssemblyValidator::~FAssemblyValidator()
{
    ShutdownComponents();
}

bool FAssemblyValidator::ValidateAssemblyRequest(const FAssemblyRequest& Request, FAssemblyValidationResult& OutResult)
{
    UE_LOG(LogAssemblyValidator, Log, TEXT("Validating assembly request"));

    CurrentValidationId = GenerateValidationId();
    StartValidationTimer();
    BroadcastValidationStarted(CurrentValidationId);

    TArray<FAssemblyValidationIssue> Issues;

    // Validate request structure
    if (IsValidationRuleEnabled(EAssemblyValidationRule::ValidRequest))
    {
        ValidateRequestStructure(Request, Issues);
    }

    // Validate dependencies
    if (IsValidationRuleEnabled(EAssemblyValidationRule::ValidDependencies))
    {
        ValidateRequestDependencies(Request, Issues);
    }

    // Validate complexity
    if (IsValidationRuleEnabled(EAssemblyValidationRule::ComplexityLimit))
    {
        ValidateRequestComplexity(Request, Issues);
    }

    // Validate naming conventions
    if (IsValidationRuleEnabled(EAssemblyValidationRule::NamingConventions))
    {
        ValidateNamingConventions(Request, Issues);
    }

    // Validate UE5 standards
    if (IsValidationRuleEnabled(EAssemblyValidationRule::UE5Standards))
    {
        ValidateUE5Standards(Request, Issues);
    }

    // Validate blueprint best practices
    if (IsValidationRuleEnabled(EAssemblyValidationRule::BlueprintBestPractices))
    {
        ValidateBlueprintBestPractices(Request, Issues);
    }

    // Prepare result
    OutResult.Issues = Issues;
    OutResult.TotalIssues = Issues.Num();
    OutResult.ValidationScore = CalculateValidationScore(Issues);
    OutResult.bValidationPassed = (OutResult.ValidationScore >= 0.7f); // 70% threshold

    // Calculate issue statistics
    for (const FAssemblyValidationIssue& Issue : Issues)
    {
        OutResult.IssuesBySeverity.FindOrAdd(Issue.Severity)++;
        OutResult.IssuesByCategory.FindOrAdd(Issue.Category)++;
    }

    // Auto-fix issues if enabled
    if (Config.bEnableAutoFix)
    {
        AutoFixIssues(OutResult);
    }

    StopValidationTimer(OutResult);
    UpdateStatistics(OutResult);
    BroadcastValidationCompleted(OutResult);

    UE_LOG(LogAssemblyValidator, Log, TEXT("Assembly request validation completed. Score: %f, Issues: %d"), 
           OutResult.ValidationScore, OutResult.TotalIssues);

    return OutResult.bValidationPassed;
}

bool FAssemblyValidator::ValidateAssemblyPlan(const FAssemblyExecutionPlan& Plan, FAssemblyValidationResult& OutResult)
{
    UE_LOG(LogAssemblyValidator, Log, TEXT("Validating assembly plan: %s"), *Plan.PlanId);

    CurrentValidationId = GenerateValidationId();
    StartValidationTimer();
    BroadcastValidationStarted(CurrentValidationId);

    TArray<FAssemblyValidationIssue> Issues;

    // Validate plan instructions
    if (IsValidationRuleEnabled(EAssemblyValidationRule::ValidInstructions))
    {
        ValidatePlanInstructions(Plan, Issues);
    }

    // Validate execution order
    if (IsValidationRuleEnabled(EAssemblyValidationRule::ValidExecutionOrder))
    {
        ValidatePlanExecutionOrder(Plan, Issues);
    }

    // Validate connections
    if (IsValidationRuleEnabled(EAssemblyValidationRule::ValidConnections))
    {
        ValidatePlanConnections(Plan, Issues);
    }

    // Validate performance
    if (IsValidationRuleEnabled(EAssemblyValidationRule::InstructionLimit) ||
        IsValidationRuleEnabled(EAssemblyValidationRule::ConnectionLimit))
    {
        ValidatePlanPerformance(Plan, Issues);
    }

    // Validate performance guidelines
    if (IsValidationRuleEnabled(EAssemblyValidationRule::PerformanceGuidelines))
    {
        ValidatePerformanceGuidelines(Plan, Issues);
    }

    // Prepare result
    OutResult.Issues = Issues;
    OutResult.TotalIssues = Issues.Num();
    OutResult.ValidationScore = CalculateValidationScore(Issues);
    OutResult.bValidationPassed = (OutResult.ValidationScore >= 0.7f);

    // Calculate issue statistics
    for (const FAssemblyValidationIssue& Issue : Issues)
    {
        OutResult.IssuesBySeverity.FindOrAdd(Issue.Severity)++;
        OutResult.IssuesByCategory.FindOrAdd(Issue.Category)++;
    }

    StopValidationTimer(OutResult);
    UpdateStatistics(OutResult);
    BroadcastValidationCompleted(OutResult);

    return OutResult.bValidationPassed;
}

bool FAssemblyValidator::ValidateAssemblyContext(const FAssemblyExecutionContext& Context, FAssemblyValidationResult& OutResult)
{
    UE_LOG(LogAssemblyValidator, Log, TEXT("Validating assembly context"));

    CurrentValidationId = GenerateValidationId();
    StartValidationTimer();
    BroadcastValidationStarted(CurrentValidationId);

    TArray<FAssemblyValidationIssue> Issues;

    // Validate context blueprint
    ValidateContextBlueprint(Context, Issues);

    // Validate context nodes
    ValidateContextNodes(Context, Issues);

    // Validate context configuration
    ValidateContextConfiguration(Context, Issues);

    // Prepare result
    OutResult.Issues = Issues;
    OutResult.TotalIssues = Issues.Num();
    OutResult.ValidationScore = CalculateValidationScore(Issues);
    OutResult.bValidationPassed = (OutResult.ValidationScore >= 0.7f);

    StopValidationTimer(OutResult);
    UpdateStatistics(OutResult);
    BroadcastValidationCompleted(OutResult);

    return OutResult.bValidationPassed;
}

bool FAssemblyValidator::ValidateAssemblyResult(const FAssemblyResult& Result, FAssemblyValidationResult& OutResult)
{
    UE_LOG(LogAssemblyValidator, Log, TEXT("Validating assembly result"));

    CurrentValidationId = GenerateValidationId();
    StartValidationTimer();
    BroadcastValidationStarted(CurrentValidationId);

    TArray<FAssemblyValidationIssue> Issues;

    // Validate result integrity
    ValidateResultIntegrity(Result, Issues);

    // Validate result quality
    ValidateResultQuality(Result, Issues);

    // Validate result compliance
    ValidateResultCompliance(Result, Issues);

    // Prepare result
    OutResult.Issues = Issues;
    OutResult.TotalIssues = Issues.Num();
    OutResult.ValidationScore = CalculateValidationScore(Issues);
    OutResult.bValidationPassed = (OutResult.ValidationScore >= 0.7f);

    StopValidationTimer(OutResult);
    UpdateStatistics(OutResult);
    BroadcastValidationCompleted(OutResult);

    return OutResult.bValidationPassed;
}

bool FAssemblyValidator::ValidateCompleteAssembly(const FAssemblyRequest& Request, const FAssemblyExecutionPlan& Plan, 
                                                  const FAssemblyExecutionContext& Context, const FAssemblyResult& Result, 
                                                  FAssemblyValidationResult& OutResult)
{
    UE_LOG(LogAssemblyValidator, Log, TEXT("Performing complete assembly validation"));

    CurrentValidationId = GenerateValidationId();
    StartValidationTimer();
    BroadcastValidationStarted(CurrentValidationId);

    TArray<FAssemblyValidationIssue> AllIssues;

    // Validate each component
    FAssemblyValidationResult RequestResult, PlanResult, ContextResult, ResultResult;
    
    ValidateAssemblyRequest(Request, RequestResult);
    AllIssues.Append(RequestResult.Issues);

    ValidateAssemblyPlan(Plan, PlanResult);
    AllIssues.Append(PlanResult.Issues);

    ValidateAssemblyContext(Context, ContextResult);
    AllIssues.Append(ContextResult.Issues);

    ValidateAssemblyResult(Result, ResultResult);
    AllIssues.Append(ResultResult.Issues);

    // Prepare comprehensive result
    OutResult.Issues = AllIssues;
    OutResult.TotalIssues = AllIssues.Num();
    OutResult.ValidationScore = CalculateValidationScore(AllIssues);
    OutResult.bValidationPassed = (OutResult.ValidationScore >= 0.7f);

    // Calculate comprehensive statistics
    for (const FAssemblyValidationIssue& Issue : AllIssues)
    {
        OutResult.IssuesBySeverity.FindOrAdd(Issue.Severity)++;
        OutResult.IssuesByCategory.FindOrAdd(Issue.Category)++;
    }

    // Auto-fix issues if enabled
    if (Config.bEnableAutoFix)
    {
        AutoFixIssues(OutResult);
    }

    StopValidationTimer(OutResult);
    UpdateStatistics(OutResult);
    BroadcastValidationCompleted(OutResult);

    UE_LOG(LogAssemblyValidator, Log, TEXT("Complete assembly validation finished. Score: %f, Issues: %d"), 
           OutResult.ValidationScore, OutResult.TotalIssues);

    return OutResult.bValidationPassed;
}

bool FAssemblyValidator::ValidateRequestStructure(const FAssemblyRequest& Request, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Validate blueprint request
    if (Request.BlueprintRequest.BlueprintName.IsEmpty())
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidRequest,
            EAssemblyValidationSeverity::Error,
            EAssemblyValidationCategory::Structure,
            TEXT("Blueprint name is empty"),
            TEXT("BlueprintRequest.BlueprintName"),
            TEXT("Provide a valid blueprint name")
        );
        AddIssue(Issue, OutIssues);
    }

    // Validate extracted structure
    const FExtractedBlueprintStructure& Structure = Request.ExtractedStructure;
    if (Structure.Variables.Num() == 0 && Structure.Functions.Num() == 0 && 
        Structure.Events.Num() == 0 && Structure.Components.Num() == 0)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidStructure,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::Structure,
            TEXT("Extracted structure is empty"),
            TEXT("ExtractedStructure"),
            TEXT("Ensure the natural language input contains blueprint elements")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidateRequestDependencies(const FAssemblyRequest& Request, TArray<FAssemblyValidationIssue>& OutIssues)
{
    const FDependencyAnalysisResult& Dependencies = Request.DependencyAnalysis;
    
    if (Dependencies.bHasCircularDependencies)
    {
        for (const FCircularDependency& CircularDep : Dependencies.CircularDependencies)
        {
            FAssemblyValidationIssue Issue = CreateIssue(
                EAssemblyValidationRule::ValidDependencies,
                EAssemblyValidationSeverity::Warning,
                EAssemblyValidationCategory::Dependencies,
                FString::Printf(TEXT("Circular dependency detected: %s"), *CircularDep.Description),
                TEXT("DependencyAnalysis"),
                TEXT("Review and restructure dependencies to eliminate cycles")
            );
            AddIssue(Issue, OutIssues);
        }
    }

    return true;
}

bool FAssemblyValidator::ValidateRequestComplexity(const FAssemblyRequest& Request, TArray<FAssemblyValidationIssue>& OutIssues)
{
    const FComplexityEstimationResult& Complexity = Request.ComplexityEstimation;
    
    if (Complexity.ComplexityScore > Config.MaxComplexityScore)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ComplexityLimit,
            EAssemblyValidationSeverity::Error,
            EAssemblyValidationCategory::Performance,
            FString::Printf(TEXT("Complexity score %f exceeds limit %f"), 
                           Complexity.ComplexityScore, Config.MaxComplexityScore),
            TEXT("ComplexityEstimation"),
            TEXT("Simplify the blueprint structure or increase complexity limit")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidatePlanInstructions(const FAssemblyExecutionPlan& Plan, TArray<FAssemblyValidationIssue>& OutIssues)
{
    if (Plan.Instructions.Num() == 0)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidInstructions,
            EAssemblyValidationSeverity::Error,
            EAssemblyValidationCategory::Instructions,
            TEXT("Assembly plan has no instructions"),
            TEXT("Instructions"),
            TEXT("Ensure the assembly plan contains valid instructions")
        );
        AddIssue(Issue, OutIssues);
        return false;
    }

    // Validate each instruction
    for (const FNodeAssemblyInstruction& Instruction : Plan.Instructions)
    {
        if (Instruction.InstructionId.IsEmpty())
        {
            FAssemblyValidationIssue Issue = CreateIssue(
                EAssemblyValidationRule::ValidInstructions,
                EAssemblyValidationSeverity::Error,
                EAssemblyValidationCategory::Instructions,
                TEXT("Instruction has empty ID"),
                TEXT("Instruction"),
                TEXT("Ensure all instructions have valid IDs")
            );
            AddIssue(Issue, OutIssues);
        }

        if (Instruction.NodeType.IsEmpty())
        {
            FAssemblyValidationIssue Issue = CreateIssue(
                EAssemblyValidationRule::ValidInstructions,
                EAssemblyValidationSeverity::Error,
                EAssemblyValidationCategory::Instructions,
                FString::Printf(TEXT("Instruction %s has empty node type"), *Instruction.InstructionId),
                TEXT("Instruction.NodeType"),
                TEXT("Specify a valid node type for the instruction")
            );
            Issue.RelatedInstructionId = Instruction.InstructionId;
            AddIssue(Issue, OutIssues);
        }
    }

    return true;
}

bool FAssemblyValidator::ValidatePlanExecutionOrder(const FAssemblyExecutionPlan& Plan, TArray<FAssemblyValidationIssue>& OutIssues)
{
    if (Plan.ExecutionOrder.Num() != Plan.Instructions.Num())
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidExecutionOrder,
            EAssemblyValidationSeverity::Error,
            EAssemblyValidationCategory::Instructions,
            FString::Printf(TEXT("Execution order count (%d) doesn't match instruction count (%d)"), 
                           Plan.ExecutionOrder.Num(), Plan.Instructions.Num()),
            TEXT("ExecutionOrder"),
            TEXT("Ensure execution order includes all instructions")
        );
        AddIssue(Issue, OutIssues);
        return false;
    }

    // Validate that all instructions are in execution order
    for (const FNodeAssemblyInstruction& Instruction : Plan.Instructions)
    {
        if (!Plan.ExecutionOrder.Contains(Instruction.InstructionId))
        {
            FAssemblyValidationIssue Issue = CreateIssue(
                EAssemblyValidationRule::ValidExecutionOrder,
                EAssemblyValidationSeverity::Error,
                EAssemblyValidationCategory::Instructions,
                FString::Printf(TEXT("Instruction %s not found in execution order"), *Instruction.InstructionId),
                TEXT("ExecutionOrder"),
                TEXT("Include all instructions in the execution order")
            );
            Issue.RelatedInstructionId = Instruction.InstructionId;
            AddIssue(Issue, OutIssues);
        }
    }

    return true;
}

bool FAssemblyValidator::ValidatePlanConnections(const FAssemblyExecutionPlan& Plan, TArray<FAssemblyValidationIssue>& OutIssues)
{
    int32 TotalConnections = 0;
    
    for (const FNodeAssemblyInstruction& Instruction : Plan.Instructions)
    {
        TotalConnections += Instruction.Connections.Num();
        
        // Validate connection format
        for (const FString& Connection : Instruction.Connections)
        {
            if (!Connection.Contains(TEXT("->")))
            {
                FAssemblyValidationIssue Issue = CreateIssue(
                    EAssemblyValidationRule::ValidConnections,
                    EAssemblyValidationSeverity::Warning,
                    EAssemblyValidationCategory::Instructions,
                    FString::Printf(TEXT("Invalid connection format: %s"), *Connection),
                    TEXT("Connection"),
                    TEXT("Use format: SourceNode.SourcePin->TargetNode.TargetPin")
                );
                Issue.RelatedInstructionId = Instruction.InstructionId;
                AddIssue(Issue, OutIssues);
            }
        }
    }

    if (TotalConnections > Config.MaxConnections)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ConnectionLimit,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::Performance,
            FString::Printf(TEXT("Total connections (%d) exceeds limit (%d)"), 
                           TotalConnections, Config.MaxConnections),
            TEXT("Connections"),
            TEXT("Reduce the number of connections or increase the limit")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidatePlanPerformance(const FAssemblyExecutionPlan& Plan, TArray<FAssemblyValidationIssue>& OutIssues)
{
    if (Plan.Instructions.Num() > Config.MaxInstructions)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::InstructionLimit,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::Performance,
            FString::Printf(TEXT("Instruction count (%d) exceeds limit (%d)"), 
                           Plan.Instructions.Num(), Config.MaxInstructions),
            TEXT("Instructions"),
            TEXT("Reduce the number of instructions or increase the limit")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidateContextBlueprint(const FAssemblyExecutionContext& Context, TArray<FAssemblyValidationIssue>& OutIssues)
{
    if (!Context.Blueprint)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidRequest,
            EAssemblyValidationSeverity::Critical,
            EAssemblyValidationCategory::General,
            TEXT("Execution context has no blueprint"),
            TEXT("Context.Blueprint"),
            TEXT("Ensure a valid blueprint is provided in the context")
        );
        AddIssue(Issue, OutIssues);
        return false;
    }

    return true;
}

bool FAssemblyValidator::ValidateContextNodes(const FAssemblyExecutionContext& Context, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Validate created nodes
    for (const auto& NodePair : Context.CreatedNodes)
    {
        if (!NodePair.Value)
        {
            FAssemblyValidationIssue Issue = CreateIssue(
                EAssemblyValidationRule::ValidRequest,
                EAssemblyValidationSeverity::Error,
                EAssemblyValidationCategory::General,
                FString::Printf(TEXT("Created node %s is null"), *NodePair.Key),
                TEXT("Context.CreatedNodes"),
                TEXT("Ensure all created nodes are valid")
            );
            AddIssue(Issue, OutIssues);
        }
    }

    return true;
}

bool FAssemblyValidator::ValidateContextConfiguration(const FAssemblyExecutionContext& Context, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Validate assembly configuration
    if (Context.Config.AssemblyTimeout <= 0.0f)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidRequest,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::General,
            TEXT("Assembly timeout is invalid"),
            TEXT("Context.Config.AssemblyTimeout"),
            TEXT("Set a positive timeout value")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidateResultIntegrity(const FAssemblyResult& Result, TArray<FAssemblyValidationIssue>& OutIssues)
{
    if (!Result.bSuccess && Result.CreatedBlueprint)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidRequest,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::General,
            TEXT("Assembly marked as failed but blueprint was created"),
            TEXT("Result"),
            TEXT("Review assembly result consistency")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidateResultQuality(const FAssemblyResult& Result, TArray<FAssemblyValidationIssue>& OutIssues)
{
    if (Result.ValidationResult.OverallQualityScore < 0.5f)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::ValidRequest,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::Quality,
            FString::Printf(TEXT("Low quality score: %f"), Result.ValidationResult.OverallQualityScore),
            TEXT("Result.ValidationResult"),
            TEXT("Review and improve blueprint quality")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidateResultCompliance(const FAssemblyResult& Result, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Check for compliance issues in the validation result
    for (const FValidationIssue& ValidationIssue : Result.ValidationResult.Issues)
    {
        if (ValidationIssue.Severity == EValidationSeverity::Critical)
        {
            FAssemblyValidationIssue Issue = CreateIssue(
                EAssemblyValidationRule::UE5Standards,
                EAssemblyValidationSeverity::Error,
                EAssemblyValidationCategory::Compliance,
                FString::Printf(TEXT("Critical validation issue: %s"), *ValidationIssue.Message),
                TEXT("Result.ValidationResult"),
                TEXT("Address critical validation issues")
            );
            AddIssue(Issue, OutIssues);
        }
    }

    return true;
}

bool FAssemblyValidator::ValidateNamingConventions(const FAssemblyRequest& Request, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Check blueprint name convention
    const FString& BlueprintName = Request.BlueprintRequest.BlueprintName;
    if (!BlueprintName.StartsWith(TEXT("BP_")))
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::NamingConventions,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::Quality,
            TEXT("Blueprint name should start with 'BP_'"),
            TEXT("BlueprintRequest.BlueprintName"),
            TEXT("Rename blueprint to follow UE5 naming conventions")
        );
        Issue.bCanAutoFix = true;
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::ValidateUE5Standards(const FAssemblyRequest& Request, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Validate against UE5 standards
    // This is a placeholder for more comprehensive UE5 standard validation
    
    return true;
}

bool FAssemblyValidator::ValidateBlueprintBestPractices(const FAssemblyRequest& Request, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Validate against blueprint best practices
    // This is a placeholder for more comprehensive best practice validation
    
    return true;
}

bool FAssemblyValidator::ValidatePerformanceGuidelines(const FAssemblyExecutionPlan& Plan, TArray<FAssemblyValidationIssue>& OutIssues)
{
    // Check for performance anti-patterns
    if (Plan.ComplexityScore > 8.0f)
    {
        FAssemblyValidationIssue Issue = CreateIssue(
            EAssemblyValidationRule::PerformanceGuidelines,
            EAssemblyValidationSeverity::Warning,
            EAssemblyValidationCategory::Performance,
            FString::Printf(TEXT("High complexity score: %f"), Plan.ComplexityScore),
            TEXT("Plan.ComplexityScore"),
            TEXT("Consider simplifying the blueprint structure")
        );
        AddIssue(Issue, OutIssues);
    }

    return true;
}

bool FAssemblyValidator::AutoFixIssues(FAssemblyValidationResult& ValidationResult)
{
    int32 FixedCount = 0;
    
    for (FAssemblyValidationIssue& Issue : ValidationResult.Issues)
    {
        if (Issue.bCanAutoFix && CanAutoFixIssue(Issue))
        {
            // Auto-fix logic would go here
            // For now, we just mark it as fixed
            ValidationResult.AutoFixedIssues.Add(Issue);
            FixedCount++;
            
            BroadcastIssueFixed(Issue, CurrentValidationId);
        }
    }

    UE_LOG(LogAssemblyValidator, Log, TEXT("Auto-fixed %d issues"), FixedCount);
    
    return FixedCount > 0;
}

bool FAssemblyValidator::CanAutoFixIssue(const FAssemblyValidationIssue& Issue)
{
    return Issue.bCanAutoFix;
}

bool FAssemblyValidator::ApplyAutoFix(const FAssemblyValidationIssue& Issue, FAssemblyRequest& Request)
{
    // Auto-fix implementation would go here
    return false;
}

FAssemblyValidationIssue FAssemblyValidator::CreateIssue(EAssemblyValidationRule Rule, EAssemblyValidationSeverity Severity, 
                                                        EAssemblyValidationCategory Category, const FString& Message, 
                                                        const FString& Context, const FString& SuggestedFix)
{
    FAssemblyValidationIssue Issue;
    Issue.IssueId = FGuid::NewGuid().ToString();
    Issue.Rule = Rule;
    Issue.Severity = Severity;
    Issue.Category = Category;
    Issue.Message = Message;
    Issue.Context = Context;
    Issue.SuggestedFix = SuggestedFix;
    Issue.DetectionTime = FDateTime::Now();
    
    return Issue;
}

void FAssemblyValidator::AddIssue(const FAssemblyValidationIssue& Issue, TArray<FAssemblyValidationIssue>& Issues)
{
    if (ShouldReportIssue(Issue))
    {
        Issues.Add(Issue);
        BroadcastIssueFound(Issue, CurrentValidationId);
        
        if (Config.bStopOnCriticalError && Issue.Severity == EAssemblyValidationSeverity::Critical)
        {
            UE_LOG(LogAssemblyValidator, Error, TEXT("Critical error encountered, stopping validation"));
        }
    }
}

bool FAssemblyValidator::ShouldReportIssue(const FAssemblyValidationIssue& Issue) const
{
    // Check severity threshold
    if (Issue.Severity < Config.MinSeverityLevel)
    {
        return false;
    }

    // Check category filter
    if (!Config.EnabledCategories.Contains(Issue.Category))
    {
        return false;
    }

    // Check rule filter
    if (!Config.EnabledRules.Contains(Issue.Rule))
    {
        return false;
    }

    return true;
}

float FAssemblyValidator::CalculateValidationScore(const TArray<FAssemblyValidationIssue>& Issues)
{
    if (Issues.Num() == 0)
    {
        return 1.0f; // Perfect score
    }

    float Score = 1.0f;
    
    for (const FAssemblyValidationIssue& Issue : Issues)
    {
        float Penalty = 0.0f;
        
        switch (Issue.Severity)
        {
        case EAssemblyValidationSeverity::Info:
            Penalty = 0.01f;
            break;
        case EAssemblyValidationSeverity::Warning:
            Penalty = 0.05f;
            break;
        case EAssemblyValidationSeverity::Error:
            Penalty = 0.15f;
            break;
        case EAssemblyValidationSeverity::Critical:
            Penalty = 0.3f;
            break;
        case EAssemblyValidationSeverity::Fatal:
            Penalty = 0.5f;
            break;
        }
        
        Score -= Penalty;
    }

    return FMath::Clamp(Score, 0.0f, 1.0f);
}

void FAssemblyValidator::UpdateStatistics(const FAssemblyValidationResult& Result)
{
    Statistics.TotalValidations++;
    
    if (Result.bValidationPassed)
    {
        Statistics.SuccessfulValidations++;
    }
    else
    {
        Statistics.FailedValidations++;
    }

    // Update average validation time
    float TotalTime = Statistics.AverageValidationTime * (Statistics.TotalValidations - 1) + Result.ValidationTime;
    Statistics.AverageValidationTime = TotalTime / Statistics.TotalValidations;

    // Update average validation score
    float TotalScore = Statistics.AverageValidationScore * (Statistics.TotalValidations - 1) + Result.ValidationScore;
    Statistics.AverageValidationScore = TotalScore / Statistics.TotalValidations;

    // Update common issues
    for (const FAssemblyValidationIssue& Issue : Result.Issues)
    {
        Statistics.CommonIssues.FindOrAdd(Issue.Rule)++;
    }

    // Update auto-fix success rate
    if (Result.Issues.Num() > 0)
    {
        float FixRate = static_cast<float>(Result.AutoFixedIssues.Num()) / Result.Issues.Num();
        float TotalFixRate = Statistics.AutoFixSuccessRate * (Statistics.TotalValidations - 1) + FixRate;
        Statistics.AutoFixSuccessRate = TotalFixRate / Statistics.TotalValidations;
    }
}

FString FAssemblyValidator::GenerateValidationId()
{
    return FString::Printf(TEXT("Validation_%s"), *FDateTime::Now().ToString());
}

void FAssemblyValidator::StartValidationTimer()
{
    ValidationStartTime = FDateTime::Now();
}

void FAssemblyValidator::StopValidationTimer(FAssemblyValidationResult& Result)
{
    FTimespan ValidationTime = FDateTime::Now() - ValidationStartTime;
    Result.ValidationTime = ValidationTime.GetTotalSeconds();
}

void FAssemblyValidator::InitializeComponents()
{
    UE_LOG(LogAssemblyValidator, Log, TEXT("Initializing assembly validator components"));
    
    GraphValidator = MakeShared<FGraphValidator>();
}

void FAssemblyValidator::ShutdownComponents()
{
    UE_LOG(LogAssemblyValidator, Log, TEXT("Shutting down assembly validator components"));
    
    GraphValidator.Reset();
}

// Delegate broadcast methods
void FAssemblyValidator::BroadcastValidationStarted(const FString& ValidationId)
{
    OnAssemblyValidationStarted.Broadcast(ValidationId);
}

void FAssemblyValidator::BroadcastIssueFound(const FAssemblyValidationIssue& Issue, const FString& ValidationId)
{
    OnAssemblyValidationIssueFound.Broadcast(Issue, ValidationId);
}

void FAssemblyValidator::BroadcastIssueFixed(const FAssemblyValidationIssue& Issue, const FString& ValidationId)
{
    OnAssemblyValidationIssueFixed.Broadcast(Issue, ValidationId);
}

void FAssemblyValidator::BroadcastValidationCompleted(const FAssemblyValidationResult& Result)
{
    OnAssemblyValidationCompleted.Broadcast(Result);
}

// Public interface methods
void FAssemblyValidator::SetValidationConfig(const FAssemblyValidationConfig& NewConfig)
{
    Config = NewConfig;
}

FAssemblyValidationConfig FAssemblyValidator::GetValidationConfig() const
{
    return Config;
}

FAssemblyValidationStatistics FAssemblyValidator::GetStatistics() const
{
    return Statistics;
}

void FAssemblyValidator::ResetStatistics()
{
    Statistics = FAssemblyValidationStatistics();
}

void FAssemblyValidator::EnableValidationRule(EAssemblyValidationRule Rule)
{
    Config.EnabledRules.AddUnique(Rule);
}

void FAssemblyValidator::DisableValidationRule(EAssemblyValidationRule Rule)
{
    Config.EnabledRules.Remove(Rule);
}

bool FAssemblyValidator::IsValidationRuleEnabled(EAssemblyValidationRule Rule) const
{
    return Config.EnabledRules.Contains(Rule);
}

void FAssemblyValidator::EnableValidationCategory(EAssemblyValidationCategory Category)
{
    Config.EnabledCategories.AddUnique(Category);
}

void FAssemblyValidator::DisableValidationCategory(EAssemblyValidationCategory Category)
{
    Config.EnabledCategories.Remove(Category);
}

bool FAssemblyValidator::IsValidationCategoryEnabled(EAssemblyValidationCategory Category) const
{
    return Config.EnabledCategories.Contains(Category);
} 