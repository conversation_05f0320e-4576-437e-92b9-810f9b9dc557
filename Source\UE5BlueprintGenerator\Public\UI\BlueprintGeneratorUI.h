#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Widgets/Input/SComboBox.h"
#include "Widgets/Input/SCheckBox.h"
#include "Widgets/Input/SButton.h"
#include "Framework/Text/TextLayout.h"
#include "AIModelConnector.h"

class SButton;
class SComboBox;
class SEditableTextBox;
class SMultiLineEditableTextBox;
class UBlueprintGeneratorSettings;

/**
 * Main UI widget for the Blueprint Generator plugin
 */
class UE5BLUEPRINTGENERATOR_API SBlueprintGeneratorUI : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SBlueprintGeneratorUI)
	{}
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);

private:
	// UI Components
	TSharedPtr<SMultiLineEditableTextBox> InputTextBox;
	TSharedPtr<SComboBox<TSharedPtr<FString>>> ElementTypeComboBox;
	TSharedPtr<SComboBox<TSharedPtr<FString>>> ProviderComboBox;
	TSharedPtr<SComboBox<TSharedPtr<FString>>> ModelComboBox;
	TSharedPtr<SCheckBox> GenerateCommentsCheckBox;
	TSharedPtr<SCheckBox> IncludeErrorHandlingCheckBox;
	TSharedPtr<SCheckBox> UseAdvancedLogicCheckBox;
	TSharedPtr<SCheckBox> OptimizePerformanceCheckBox;
	TSharedPtr<SButton> GenerateButton;
	TSharedPtr<SButton> ClearButton;
	TSharedPtr<SButton> SettingsButton;

	// Data
	TArray<TSharedPtr<FString>> ElementTypeOptions;
	TArray<TSharedPtr<FString>> ProviderOptions;
	TArray<TSharedPtr<FString>> ModelOptions;
	FString CurrentInputText;
	EAIModelProvider CurrentProvider;
	FString CurrentModelName;
	bool bGenerateComments;
	bool bIncludeErrorHandling;
	bool bUseAdvancedLogic;
	bool bOptimizePerformance;
	bool bIsRefreshingModels;

	// UI Construction Methods
	void ConstructInputSection();
	void ConstructControlsSection();
	void ConstructOptionsSection();
	void ConstructButtonsSection();

	// Event Handlers
	void OnInputTextChanged(const FText& NewText);
	void OnInputTextCommitted(const FText& NewText, ETextCommit::Type CommitType);
	void OnElementTypeChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo);
	void OnProviderChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo);
	void OnModelChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo);
	void OnGenerateCommentsChanged(ECheckBoxState NewState);
	void OnIncludeErrorHandlingChanged(ECheckBoxState NewState);
	void OnUseAdvancedLogicChanged(ECheckBoxState NewState);
	void OnOptimizePerformanceChanged(ECheckBoxState NewState);
	FReply OnGenerateButtonClicked();
	FReply OnClearButtonClicked();
	FReply OnSettingsButtonClicked();

	// Widget Generation Methods
	TSharedRef<SWidget> MakeElementTypeWidget(TSharedPtr<FString> InOption);
	TSharedRef<SWidget> MakeProviderWidget(TSharedPtr<FString> InOption);
	TSharedRef<SWidget> MakeModelWidget(TSharedPtr<FString> InOption);

	// Text Getters
	FText GetElementTypeText() const;
	FText GetProviderText() const;
	FText GetModelText() const;

	// State Getters
	bool IsGenerateButtonEnabled() const;
	ECheckBoxState GetGenerateCommentsState() const;
	ECheckBoxState GetIncludeErrorHandlingState() const;
	ECheckBoxState GetUseAdvancedLogicState() const;
	ECheckBoxState GetOptimizePerformanceState() const;

	// Initialization
	void InitializeOptions();
	void InitializeDefaultValues();

	// Validation
	bool ValidateInput() const;
	FText GetInputValidationMessage() const;

	// Real-time feedback
	void UpdateComplexityIndicator();
	void UpdateSuggestedElementType();

	// Settings integration
	void LoadSettingsFromConfig();
	void SaveSettingsToConfig();
	void RefreshModelsList();
}; 