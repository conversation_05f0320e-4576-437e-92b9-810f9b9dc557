#include "AI/LMStudioConnector.h"
#include "UE5BlueprintGeneratorModule.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Dom/JsonObject.h"
#include "Misc/DateTime.h"

DEFINE_LOG_CATEGORY_EXTERN(LogLMStudioConnector, Log, All);
DEFINE_LOG_CATEGORY(LogLMStudioConnector);

FLMStudioConnector::FLMStudioConnector()
{
	UE_LOG(LogLMStudioConnector, Log, TEXT("LMStudio connector initialized"));
}

FLMStudioConnector::~FLMStudioConnector()
{
	CancelPendingRequests();
	UE_LOG(LogLMStudioConnector, Log, TEXT("LMStudio connector destroyed"));
}

bool FLMStudioConnector::SendRequest(const FAIModelRequest& Request, TFunction<void(const FAIModelResponse&)> OnResponse)
{
	if (Request.Prompt.IsEmpty())
	{
		UE_LOG(LogLMStudioConnector, Warning, TEXT("Cannot send request with empty prompt"));
		FAIModelResponse ErrorResponse = CreateErrorResponse(TEXT("Empty prompt provided"));
		OnResponse(ErrorResponse);
		return false;
	}

	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = CreateChatCompletionRequest(Request);
	if (!HttpRequest.IsValid())
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("Failed to create HTTP request"));
		FAIModelResponse ErrorResponse = CreateErrorResponse(TEXT("Failed to create HTTP request"));
		OnResponse(ErrorResponse);
		return false;
	}

	// Bind response handler
	HttpRequest->OnProcessRequestComplete().BindLambda([this, OnResponse](FHttpRequestPtr RequestPtr, FHttpResponsePtr Response, bool bWasSuccessful)
	{
		HandleChatCompletionResponse(RequestPtr, Response, bWasSuccessful, OnResponse);
	});

	// Send the request
	bool bRequestSent = HttpRequest->ProcessRequest();
	if (bRequestSent)
	{
		PendingRequests.Add(HttpRequest);
		UE_LOG(LogLMStudioConnector, Log, TEXT("LMStudio generation request sent"));
	}
	else
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("Failed to send LMStudio request"));
		FAIModelResponse ErrorResponse = CreateErrorResponse(TEXT("Failed to send HTTP request"));
		OnResponse(ErrorResponse);
	}

	return bRequestSent;
}

bool FLMStudioConnector::TestConnection(const FAIModelSettings& Settings, TFunction<void(bool, const FString&)> OnResponse)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = CreateTestRequest(Settings);
	if (!HttpRequest.IsValid())
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("Failed to create test HTTP request"));
		OnResponse(false, TEXT("Failed to create HTTP request"));
		return false;
	}

	// Bind response handler
	HttpRequest->OnProcessRequestComplete().BindLambda([this, OnResponse](FHttpRequestPtr RequestPtr, FHttpResponsePtr Response, bool bWasSuccessful)
	{
		HandleTestResponse(RequestPtr, Response, bWasSuccessful, OnResponse);
	});

	// Send the request
	bool bRequestSent = HttpRequest->ProcessRequest();
	if (bRequestSent)
	{
		PendingRequests.Add(HttpRequest);
		UE_LOG(LogLMStudioConnector, Log, TEXT("LMStudio connection test sent"));
	}
	else
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("Failed to send LMStudio test request"));
		OnResponse(false, TEXT("Failed to send HTTP request"));
	}

	return bRequestSent;
}

bool FLMStudioConnector::GetAvailableModels(const FAIModelSettings& Settings, TFunction<void(const TArray<FString>&)> OnResponse)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = CreateModelsRequest(Settings);
	if (!HttpRequest.IsValid())
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("Failed to create models HTTP request"));
		TArray<FString> EmptyArray;
		OnResponse(EmptyArray);
		return false;
	}

	// Bind response handler
	HttpRequest->OnProcessRequestComplete().BindLambda([this, OnResponse](FHttpRequestPtr RequestPtr, FHttpResponsePtr Response, bool bWasSuccessful)
	{
		HandleModelsResponse(RequestPtr, Response, bWasSuccessful, OnResponse);
	});

	// Send the request
	bool bRequestSent = HttpRequest->ProcessRequest();
	if (bRequestSent)
	{
		PendingRequests.Add(HttpRequest);
		UE_LOG(LogLMStudioConnector, Log, TEXT("LMStudio models request sent"));
	}
	else
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("Failed to send LMStudio models request"));
		TArray<FString> EmptyArray;
		OnResponse(EmptyArray);
	}

	return bRequestSent;
}

void FLMStudioConnector::CancelPendingRequests()
{
	for (auto& Request : PendingRequests)
	{
		if (Request.IsValid())
		{
			Request->CancelRequest();
		}
	}
	PendingRequests.Empty();
	UE_LOG(LogLMStudioConnector, Log, TEXT("Cancelled %d pending LMStudio requests"), PendingRequests.Num());
}

TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> FLMStudioConnector::CreateChatCompletionRequest(const FAIModelRequest& Request)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	HttpRequest->SetURL(GetChatCompletionEndpoint(Request.Settings));
	HttpRequest->SetVerb(TEXT("POST"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequest->SetTimeout(Request.Settings.TimeoutSeconds);

	// Add authorization header if API key is provided
	if (!Request.Settings.APIKey.IsEmpty())
	{
		FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *Request.Settings.APIKey);
		HttpRequest->SetHeader(TEXT("Authorization"), AuthHeader);
	}

	// Create JSON payload
	TSharedPtr<FJsonObject> JsonPayload = CreateChatCompletionRequestJson(Request);
	FString JsonString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
	FJsonSerializer::Serialize(JsonPayload.ToSharedRef(), Writer);

	HttpRequest->SetContentAsString(JsonString);

	UE_LOG(LogLMStudioConnector, VeryVerbose, TEXT("LMStudio request JSON: %s"), *JsonString);

	return HttpRequest;
}

TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> FLMStudioConnector::CreateTestRequest(const FAIModelSettings& Settings)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	HttpRequest->SetURL(GetModelsEndpoint(Settings));
	HttpRequest->SetVerb(TEXT("GET"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequest->SetTimeout(10); // Short timeout for connection test

	// Add authorization header if API key is provided
	if (!Settings.APIKey.IsEmpty())
	{
		FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *Settings.APIKey);
		HttpRequest->SetHeader(TEXT("Authorization"), AuthHeader);
	}

	return HttpRequest;
}

TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> FLMStudioConnector::CreateModelsRequest(const FAIModelSettings& Settings)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	HttpRequest->SetURL(GetModelsEndpoint(Settings));
	HttpRequest->SetVerb(TEXT("GET"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequest->SetTimeout(Settings.TimeoutSeconds);

	// Add authorization header if API key is provided
	if (!Settings.APIKey.IsEmpty())
	{
		FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *Settings.APIKey);
		HttpRequest->SetHeader(TEXT("Authorization"), AuthHeader);
	}

	return HttpRequest;
}

void FLMStudioConnector::HandleChatCompletionResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const FAIModelResponse&)> OnResponse)
{
	RemoveCompletedRequest(Request);

	FAIModelResponse ModelResponse;
	
	if (!bWasSuccessful || !Response.IsValid())
	{
		ModelResponse = CreateErrorResponse(TEXT("HTTP request failed"));
		UE_LOG(LogLMStudioConnector, Error, TEXT("LMStudio generation request failed"));
		OnResponse(ModelResponse);
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	if (ResponseCode != 200)
	{
		FString ErrorMsg = FString::Printf(TEXT("HTTP %d: %s"), ResponseCode, *GetHttpErrorMessage(ResponseCode));
		ModelResponse = CreateErrorResponse(ErrorMsg);
		UE_LOG(LogLMStudioConnector, Error, TEXT("LMStudio returned error: %s"), *ErrorMsg);
		OnResponse(ModelResponse);
		return;
	}

	FString ResponseContent = Response->GetContentAsString();
	UE_LOG(LogLMStudioConnector, VeryVerbose, TEXT("LMStudio response: %s"), *ResponseContent);

	ModelResponse = ParseChatCompletionResponse(ResponseContent);
	OnResponse(ModelResponse);
}

void FLMStudioConnector::HandleTestResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(bool, const FString&)> OnResponse)
{
	RemoveCompletedRequest(Request);

	if (!bWasSuccessful || !Response.IsValid())
	{
		UE_LOG(LogLMStudioConnector, Warning, TEXT("LMStudio connection test failed"));
		OnResponse(false, TEXT("Connection failed"));
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	if (ResponseCode == 200)
	{
		UE_LOG(LogLMStudioConnector, Log, TEXT("LMStudio connection test successful"));
		OnResponse(true, TEXT("Connection successful"));
	}
	else
	{
		FString ErrorMsg = FString::Printf(TEXT("HTTP %d: %s"), ResponseCode, *GetHttpErrorMessage(ResponseCode));
		UE_LOG(LogLMStudioConnector, Warning, TEXT("LMStudio connection test failed: %s"), *ErrorMsg);
		OnResponse(false, ErrorMsg);
	}
}

void FLMStudioConnector::HandleModelsResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const TArray<FString>&)> OnResponse)
{
	RemoveCompletedRequest(Request);

	TArray<FString> Models;

	if (!bWasSuccessful || !Response.IsValid())
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("LMStudio models request failed"));
		OnResponse(Models);
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	if (ResponseCode != 200)
	{
		FString ErrorMsg = FString::Printf(TEXT("HTTP %d: %s"), ResponseCode, *GetHttpErrorMessage(ResponseCode));
		UE_LOG(LogLMStudioConnector, Error, TEXT("LMStudio models request failed: %s"), *ErrorMsg);
		OnResponse(Models);
		return;
	}

	FString ResponseContent = Response->GetContentAsString();
	Models = ParseModelsResponse(ResponseContent);
	
	UE_LOG(LogLMStudioConnector, Log, TEXT("Retrieved %d models from LMStudio"), Models.Num());
	OnResponse(Models);
}

TSharedPtr<FJsonObject> FLMStudioConnector::CreateChatCompletionRequestJson(const FAIModelRequest& Request)
{
	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

	JsonObject->SetStringField(TEXT("model"), Request.Settings.ModelName);
	JsonObject->SetNumberField(TEXT("temperature"), Request.Settings.Temperature);
	JsonObject->SetNumberField(TEXT("top_p"), Request.Settings.TopP);
	JsonObject->SetNumberField(TEXT("max_tokens"), Request.Settings.MaxTokens);
	JsonObject->SetBoolField(TEXT("stream"), Request.Settings.bUseStreaming);

	// Create messages array
	TArray<TSharedPtr<FJsonValue>> MessagesArray;

	// Add system message if provided
	if (!Request.Settings.SystemPrompt.IsEmpty())
	{
		TSharedPtr<FJsonObject> SystemMessage = MakeShareable(new FJsonObject);
		SystemMessage->SetStringField(TEXT("role"), TEXT("system"));
		SystemMessage->SetStringField(TEXT("content"), Request.Settings.SystemPrompt);
		MessagesArray.Add(MakeShareable(new FJsonValueObject(SystemMessage)));
	}

	// Add user message
	TSharedPtr<FJsonObject> UserMessage = MakeShareable(new FJsonObject);
	UserMessage->SetStringField(TEXT("role"), TEXT("user"));
	UserMessage->SetStringField(TEXT("content"), Request.Prompt);
	MessagesArray.Add(MakeShareable(new FJsonValueObject(UserMessage)));

	JsonObject->SetArrayField(TEXT("messages"), MessagesArray);

	return JsonObject;
}

FAIModelResponse FLMStudioConnector::ParseChatCompletionResponse(const FString& ResponseContent)
{
	FAIModelResponse ModelResponse;
	
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ResponseContent);
	
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		ModelResponse = CreateErrorResponse(TEXT("Failed to parse JSON response"));
		return ModelResponse;
	}

	// Check for error in response
	if (JsonObject->HasField(TEXT("error")))
	{
		TSharedPtr<FJsonObject> ErrorObject = JsonObject->GetObjectField(TEXT("error"));
		FString ErrorMessage = ErrorObject->GetStringField(TEXT("message"));
		ModelResponse = CreateErrorResponse(ErrorMessage);
		return ModelResponse;
	}

	// Extract choices array
	const TArray<TSharedPtr<FJsonValue>>* ChoicesArray;
	if (!JsonObject->TryGetArrayField(TEXT("choices"), ChoicesArray) || ChoicesArray->Num() == 0)
	{
		ModelResponse = CreateErrorResponse(TEXT("No choices in response"));
		return ModelResponse;
	}

	// Get first choice
	TSharedPtr<FJsonObject> FirstChoice = (*ChoicesArray)[0]->AsObject();
	if (!FirstChoice.IsValid())
	{
		ModelResponse = CreateErrorResponse(TEXT("Invalid choice format"));
		return ModelResponse;
	}

	// Extract message content
	TSharedPtr<FJsonObject> MessageObject = FirstChoice->GetObjectField(TEXT("message"));
	if (!MessageObject.IsValid())
	{
		ModelResponse = CreateErrorResponse(TEXT("No message in choice"));
		return ModelResponse;
	}

	ModelResponse.Response = MessageObject->GetStringField(TEXT("content"));
	ModelResponse.bSuccess = true;

	// Extract usage information if available
	if (JsonObject->HasField(TEXT("usage")))
	{
		TSharedPtr<FJsonObject> UsageObject = JsonObject->GetObjectField(TEXT("usage"));
		if (UsageObject.IsValid())
		{
			ModelResponse.TokensUsed = UsageObject->GetIntegerField(TEXT("total_tokens"));
		}
	}

	return ModelResponse;
}

TArray<FString> FLMStudioConnector::ParseModelsResponse(const FString& ResponseContent)
{
	TArray<FString> Models;
	
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ResponseContent);
	
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogLMStudioConnector, Error, TEXT("Failed to parse models JSON response"));
		return Models;
	}

	const TArray<TSharedPtr<FJsonValue>>* DataArray;
	if (JsonObject->TryGetArrayField(TEXT("data"), DataArray))
	{
		for (const auto& ModelValue : *DataArray)
		{
			TSharedPtr<FJsonObject> ModelObject = ModelValue->AsObject();
			if (ModelObject.IsValid())
			{
				FString ModelId = ModelObject->GetStringField(TEXT("id"));
				if (!ModelId.IsEmpty())
				{
					Models.Add(ModelId);
				}
			}
		}
	}

	return Models;
}

FString FLMStudioConnector::GetChatCompletionEndpoint(const FAIModelSettings& Settings)
{
	FString BaseURL = Settings.Endpoint;
	
	// Handle different endpoint formats
	if (BaseURL.EndsWith(TEXT("/v1/chat/completions")))
	{
		return BaseURL;
	}
	
	if (BaseURL.EndsWith(TEXT("/v1")))
	{
		return BaseURL + TEXT("/chat/completions");
	}
	
	// Remove trailing slash if present
	if (BaseURL.EndsWith(TEXT("/")))
	{
		BaseURL = BaseURL.LeftChop(1);
	}
	
	return BaseURL + TEXT("/v1/chat/completions");
}

FString FLMStudioConnector::GetModelsEndpoint(const FAIModelSettings& Settings)
{
	FString BaseURL = Settings.Endpoint;
	
	// Handle different endpoint formats
	if (BaseURL.EndsWith(TEXT("/v1/models")))
	{
		return BaseURL;
	}
	
	if (BaseURL.EndsWith(TEXT("/v1")))
	{
		return BaseURL + TEXT("/models");
	}
	
	// Extract base URL from chat completions endpoint
	if (BaseURL.EndsWith(TEXT("/v1/chat/completions")))
	{
		BaseURL = BaseURL.LeftChop(18); // Remove "/v1/chat/completions"
	}
	
	// Remove trailing slash if present
	if (BaseURL.EndsWith(TEXT("/")))
	{
		BaseURL = BaseURL.LeftChop(1);
	}
	
	return BaseURL + TEXT("/v1/models");
}

void FLMStudioConnector::RemoveCompletedRequest(FHttpRequestPtr Request)
{
	PendingRequests.RemoveAll([Request](const TSharedPtr<IHttpRequest, ESPMode::ThreadSafe>& PendingRequest)
	{
		return PendingRequest == Request;
	});
}

FAIModelResponse FLMStudioConnector::CreateErrorResponse(const FString& ErrorMessage)
{
	FAIModelResponse ErrorResponse;
	ErrorResponse.bSuccess = false;
	ErrorResponse.ErrorMessage = ErrorMessage;
	ErrorResponse.Response = TEXT("");
	ErrorResponse.TokensUsed = 0;
	ErrorResponse.ResponseTime = 0.0f;
	return ErrorResponse;
}

FString FLMStudioConnector::GetHttpErrorMessage(int32 ResponseCode)
{
	switch (ResponseCode)
	{
		case 400: return TEXT("Bad Request");
		case 401: return TEXT("Unauthorized");
		case 403: return TEXT("Forbidden");
		case 404: return TEXT("Not Found");
		case 429: return TEXT("Too Many Requests");
		case 500: return TEXT("Internal Server Error");
		case 502: return TEXT("Bad Gateway");
		case 503: return TEXT("Service Unavailable");
		case 504: return TEXT("Gateway Timeout");
		default: return FString::Printf(TEXT("HTTP Error %d"), ResponseCode);
	}
} 