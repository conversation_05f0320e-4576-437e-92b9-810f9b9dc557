#include "BlueprintGeneration/NodeFactory.h"
#include "Engine/Engine.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_CustomEvent.h"
#include "BlueprintGraph/Classes/K2Node_IfThenElse.h"
#include "BlueprintGraph/Classes/K2Node_ExecutionSequence.h"
#include "BlueprintGraph/Classes/K2Node_ForLoop.h"
#include "BlueprintGraph/Classes/K2Node_WhileLoop.h"
#include "BlueprintGraph/Classes/K2Node_Switch.h"
#include "BlueprintGraph/Classes/K2Node_Delay.h"
#include "BlueprintGraph/Classes/K2Node_Timeline.h"
#include "BlueprintGraph/Classes/K2Node_DynamicCast.h"
#include "BlueprintGraph/Classes/K2Node_ForEachElementInArray.h"
#include "BlueprintGraph/Classes/K2Node_Self.h"
#include "BlueprintGraph/Classes/K2Node_InputAction.h"
#include "BlueprintGraph/Classes/K2Node_MacroInstance.h"
#include "BlueprintGraph/Classes/K2Node_CommutativeAssociativeBinaryOperator.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "Engine/Blueprint.h"
#include "KismetCompiler.h"

DEFINE_LOG_CATEGORY(LogNodeFactory);

FNodeFactory::FNodeFactory()
    : NodePositionCounter(0)
    , DefaultNodeSpacing(400.0f)
    , bAutoRefreshNodes(true)
    , bVerboseLogging(false)
{
    // Initialize built-in node types
    InitializeBuiltInNodeTypes();

    UE_LOG(LogNodeFactory, Log, TEXT("NodeFactory initialized with %d registered node types"), RegisteredNodeTypes.Num());
}

FNodeFactory::~FNodeFactory()
{
    UE_LOG(LogNodeFactory, Log, TEXT("NodeFactory destroyed"));
}

FNODECREATIONRESULT FNodeFactory::CreateNode(UEdGraph* Graph, const FNODECREATIONINFO& CreationInfo)
{
    FNODECREATIONRESULT Result;

    // Validate creation request
    FString ErrorMessage;
    if (!ValidateNodeCreation(Graph, CreationInfo, ErrorMessage))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = ErrorMessage;
        LogNodeCreation(FString::Printf(TEXT("Node creation validation failed: %s"), *ErrorMessage), true);
        return Result;
    }

    // Create the node
    UK2Node* CreatedNode = CreateNodeOfType(Graph, CreationInfo.NodeTypeName, CreationInfo.Position);
    if (!CreatedNode)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Failed to create node of type: %s"), *CreationInfo.NodeTypeName);
        LogNodeCreation(Result.ErrorMessage, true);
        return Result;
    }

    // Configure the node
    if (!ConfigureNode(CreatedNode, CreationInfo.Properties))
    {
        Result.WarningMessages.Add(TEXT("Failed to configure some node properties"));
    }

    // Set comment if provided
    if (!CreationInfo.Comment.IsEmpty())
    {
        SetNodeComment(CreatedNode, CreationInfo.Comment);
    }

    // Set advanced display for pins if specified
    if (CreationInfo.AdvancedPins.Num() > 0)
    {
        SetNodeAdvancedDisplay(CreatedNode, CreationInfo.AdvancedPins);
    }

    // Refresh the node if auto-refresh is enabled
    if (bAutoRefreshNodes)
    {
        RefreshNode(CreatedNode);
    }

    // Set up successful result
    Result.bSuccess = true;
    Result.CreatedNode = CreatedNode;
    Result.NodeGUID = CreatedNode->NodeGuid.ToString();

    LogNodeCreation(FString::Printf(TEXT("Successfully created node: %s at position (%.1f, %.1f)"), 
        *CreationInfo.NodeTypeName, CreationInfo.Position.X, CreationInfo.Position.Y));

    return Result;
}

UK2Node* FNodeFactory::CreateNodeOfType(UEdGraph* Graph, const FString& NodeType, const FVector2D& Position)
{
    if (!Graph)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Cannot create node in null graph"));
        return nullptr;
    }

    // Check if node type is registered
    if (!RegisteredNodeTypes.Contains(NodeType))
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Unknown node type: %s"), *NodeType);
        return nullptr;
    }

    const FNODETYPEINFO& NodeTypeInfo = RegisteredNodeTypes[NodeType];
    
    // Create node of the specified class
    UK2Node* NewNode = CreateNodeOfClass(Graph, NodeTypeInfo.NodeClass, Position);
    
    if (NewNode)
    {
        // Special configuration for specific node types
        if (NodeType == TEXT("FunctionCall") && !NodeTypeInfo.FunctionName.IsEmpty())
        {
            UK2Node_CallFunction* FunctionCallNode = Cast<UK2Node_CallFunction>(NewNode);
            if (FunctionCallNode)
            {
                UFunction* Function = FindFunction(NodeTypeInfo.FunctionName, NodeTypeInfo.ClassName);
                if (Function)
                {
                    FunctionCallNode->SetFromFunction(Function);
                }
            }
        }
    }

    return NewNode;
}

UK2Node_CallFunction* FNodeFactory::CreateFunctionCallNode(UEdGraph* Graph, UFunction* Function, const FVector2D& Position)
{
    if (!Graph || !Function)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Cannot create function call node with null graph or function"));
        return nullptr;
    }

    UK2Node_CallFunction* NewNode = Cast<UK2Node_CallFunction>(CreateNodeOfClass(Graph, UK2Node_CallFunction::StaticClass(), Position));
    if (NewNode)
    {
        NewNode->SetFromFunction(Function);
        RefreshNode(NewNode);
    }

    return NewNode;
}

UK2Node_CallFunction* FNodeFactory::CreateFunctionCallNodeByName(UEdGraph* Graph, const FString& FunctionName, const FString& ClassName, const FVector2D& Position)
{
    UFunction* Function = FindFunction(FunctionName, ClassName);
    if (!Function)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Cannot find function: %s in class: %s"), *FunctionName, *ClassName);
        return nullptr;
    }

    return CreateFunctionCallNode(Graph, Function, Position);
}

UK2Node_VariableGet* FNodeFactory::CreateVariableGetNode(UEdGraph* Graph, FProperty* Property, const FVector2D& Position)
{
    if (!Graph || !Property)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Cannot create variable get node with null graph or property"));
        return nullptr;
    }

    UK2Node_VariableGet* NewNode = Cast<UK2Node_VariableGet>(CreateNodeOfClass(Graph, UK2Node_VariableGet::StaticClass(), Position));
    if (NewNode)
    {
        NewNode->VariableReference.SetSelfMember(Property->GetFName());
        RefreshNode(NewNode);
    }

    return NewNode;
}

UK2Node_VariableSet* FNodeFactory::CreateVariableSetNode(UEdGraph* Graph, FProperty* Property, const FVector2D& Position)
{
    if (!Graph || !Property)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Cannot create variable set node with null graph or property"));
        return nullptr;
    }

    UK2Node_VariableSet* NewNode = Cast<UK2Node_VariableSet>(CreateNodeOfClass(Graph, UK2Node_VariableSet::StaticClass(), Position));
    if (NewNode)
    {
        NewNode->VariableReference.SetSelfMember(Property->GetFName());
        RefreshNode(NewNode);
    }

    return NewNode;
}

UK2Node_Event* FNodeFactory::CreateEventNode(UEdGraph* Graph, const FString& EventName, const FVector2D& Position)
{
    if (!Graph)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Cannot create event node with null graph"));
        return nullptr;
    }

    UK2Node_Event* NewNode = Cast<UK2Node_Event>(CreateNodeOfClass(Graph, UK2Node_Event::StaticClass(), Position));
    if (NewNode)
    {
        // Set event function name
        NewNode->EventReference.SetExternalMember(FName(*EventName), UObject::StaticClass());
        RefreshNode(NewNode);
    }

    return NewNode;
}

UK2Node_CustomEvent* FNodeFactory::CreateCustomEventNode(UEdGraph* Graph, const FString& EventName, const FVector2D& Position)
{
    if (!Graph)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("Cannot create custom event node with null graph"));
        return nullptr;
    }

    UK2Node_CustomEvent* NewNode = Cast<UK2Node_CustomEvent>(CreateNodeOfClass(Graph, UK2Node_CustomEvent::StaticClass(), Position));
    if (NewNode)
    {
        NewNode->CustomFunctionName = FName(*EventName);
        RefreshNode(NewNode);
    }

    return NewNode;
}

UK2Node_IfThenElse* FNodeFactory::CreateBranchNode(UEdGraph* Graph, const FVector2D& Position)
{
    return Cast<UK2Node_IfThenElse>(CreateNodeOfClass(Graph, UK2Node_IfThenElse::StaticClass(), Position));
}

UK2Node_ExecutionSequence* FNodeFactory::CreateSequenceNode(UEdGraph* Graph, const FVector2D& Position, int32 OutputCount)
{
    UK2Node_ExecutionSequence* NewNode = Cast<UK2Node_ExecutionSequence>(CreateNodeOfClass(Graph, UK2Node_ExecutionSequence::StaticClass(), Position));
    if (NewNode && OutputCount > 2)
    {
        // Add additional output pins if requested
        for (int32 i = 2; i < OutputCount; i++)
        {
            NewNode->AddInputPin();
        }
        RefreshNode(NewNode);
    }

    return NewNode;
}

UK2Node_ForLoop* FNodeFactory::CreateForLoopNode(UEdGraph* Graph, const FVector2D& Position)
{
    return Cast<UK2Node_ForLoop>(CreateNodeOfClass(Graph, UK2Node_ForLoop::StaticClass(), Position));
}

UK2Node_WhileLoop* FNodeFactory::CreateWhileLoopNode(UEdGraph* Graph, const FVector2D& Position)
{
    return Cast<UK2Node_WhileLoop>(CreateNodeOfClass(Graph, UK2Node_WhileLoop::StaticClass(), Position));
}

UK2Node_Switch* FNodeFactory::CreateSwitchNode(UEdGraph* Graph, const FVector2D& Position, const FString& SwitchType)
{
    UK2Node_Switch* NewNode = Cast<UK2Node_Switch>(CreateNodeOfClass(Graph, UK2Node_Switch::StaticClass(), Position));
    if (NewNode)
    {
        // Configure switch type based on string parameter
        if (SwitchType == TEXT("string"))
        {
            // Set up for string switch
        }
        else if (SwitchType == TEXT("enum"))
        {
            // Set up for enum switch
        }
        // Default is int switch
        
        RefreshNode(NewNode);
    }

    return NewNode;
}

bool FNodeFactory::ConfigureNode(UK2Node* Node, const TMap<FString, FString>& Properties)
{
    if (!Node)
    {
        return false;
    }

    bool bAllPropertiesSet = true;

    for (const auto& PropertyPair : Properties)
    {
        if (!SetNodeProperty(Node, PropertyPair.Key, PropertyPair.Value))
        {
            bAllPropertiesSet = false;
            UE_LOG(LogNodeFactory, Warning, TEXT("Failed to set property %s on node %s"), 
                *PropertyPair.Key, *Node->GetClass()->GetName());
        }
    }

    return bAllPropertiesSet;
}

bool FNodeFactory::SetNodeComment(UK2Node* Node, const FString& Comment)
{
    if (!Node)
    {
        return false;
    }

    Node->NodeComment = Comment;
    Node->bCommentBubbleVisible = !Comment.IsEmpty();
    return true;
}

bool FNodeFactory::SetNodeAdvancedDisplay(UK2Node* Node, const TArray<FString>& PinNames)
{
    if (!Node)
    {
        return false;
    }

    bool bAllPinsSet = true;

    for (const FString& PinName : PinNames)
    {
        UEdGraphPin* Pin = Node->FindPin(FName(*PinName));
        if (Pin)
        {
            Pin->bAdvancedView = true;
        }
        else
        {
            bAllPinsSet = false;
            UE_LOG(LogNodeFactory, Warning, TEXT("Could not find pin %s on node %s"), 
                *PinName, *Node->GetClass()->GetName());
        }
    }

    return bAllPinsSet;
}

bool FNodeFactory::SetNodeProperty(UK2Node* Node, const FString& PropertyName, const FString& PropertyValue)
{
    if (!Node)
    {
        return false;
    }

    // Find the property on the node
    FProperty* Property = Node->GetClass()->FindPropertyByName(FName(*PropertyName));
    if (!Property)
    {
        return false;
    }

    // Set the property value based on its type
    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
    {
        bool bValue = PropertyValue.ToBool();
        BoolProp->SetPropertyValue_InContainer(Node, bValue);
        return true;
    }
    else if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
    {
        int32 IntValue = FCString::Atoi(*PropertyValue);
        IntProp->SetPropertyValue_InContainer(Node, IntValue);
        return true;
    }
    else if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
    {
        float FloatValue = FCString::Atof(*PropertyValue);
        FloatProp->SetPropertyValue_InContainer(Node, FloatValue);
        return true;
    }
    else if (FStrProperty* StringProp = CastField<FStrProperty>(Property))
    {
        StringProp->SetPropertyValue_InContainer(Node, PropertyValue);
        return true;
    }

    return false;
}

bool FNodeFactory::IsNodeTypeSupported(const FString& NodeType)
{
    return RegisteredNodeTypes.Contains(NodeType);
}

bool FNodeFactory::CanCreateNodeInGraph(const FString& NodeType, UEdGraph* Graph)
{
    if (!Graph || !IsNodeTypeSupported(NodeType))
    {
        return false;
    }

    const FNODETYPEINFO& NodeTypeInfo = RegisteredNodeTypes[NodeType];
    
    // Check if the graph type is supported
    if (NodeTypeInfo.SupportedGraphTypes.Num() > 0)
    {
        FString GraphTypeName = Graph->GetClass()->GetName();
        return NodeTypeInfo.SupportedGraphTypes.Contains(GraphTypeName);
    }

    return true; // If no specific graph types are specified, assume it's supported
}

bool FNodeFactory::ValidateNodeCreation(UEdGraph* Graph, const FNODECREATIONINFO& CreationInfo, FString& OutErrorMessage)
{
    if (!Graph)
    {
        OutErrorMessage = TEXT("Graph is null");
        return false;
    }

    if (CreationInfo.NodeTypeName.IsEmpty())
    {
        OutErrorMessage = TEXT("Node type name is empty");
        return false;
    }

    if (!IsNodeTypeSupported(CreationInfo.NodeTypeName))
    {
        OutErrorMessage = FString::Printf(TEXT("Node type '%s' is not supported"), *CreationInfo.NodeTypeName);
        return false;
    }

    if (!CanCreateNodeInGraph(CreationInfo.NodeTypeName, Graph))
    {
        OutErrorMessage = FString::Printf(TEXT("Node type '%s' cannot be created in this graph type"), *CreationInfo.NodeTypeName);
        return false;
    }

    return true;
}

void FNodeFactory::RegisterNodeType(const FNODETYPEINFO& NodeTypeInfo)
{
    if (NodeTypeInfo.NodeTypeName.IsEmpty() || !NodeTypeInfo.NodeClass)
    {
        UE_LOG(LogNodeFactory, Warning, TEXT("Cannot register node type with empty name or null class"));
        return;
    }

    RegisteredNodeTypes.Add(NodeTypeInfo.NodeTypeName, NodeTypeInfo);
    
    LogNodeCreation(FString::Printf(TEXT("Registered node type: %s"), *NodeTypeInfo.NodeTypeName));
}

void FNodeFactory::UnregisterNodeType(const FString& NodeTypeName)
{
    if (RegisteredNodeTypes.Remove(NodeTypeName) > 0)
    {
        LogNodeCreation(FString::Printf(TEXT("Unregistered node type: %s"), *NodeTypeName));
    }
}

FNODETYPEINFO FNodeFactory::GetNodeTypeInfo(const FString& NodeTypeName)
{
    if (RegisteredNodeTypes.Contains(NodeTypeName))
    {
        return RegisteredNodeTypes[NodeTypeName];
    }
    
    return FNODETYPEINFO(); // Return empty struct if not found
}

TArray<FString> FNodeFactory::GetRegisteredNodeTypes()
{
    TArray<FString> NodeTypes;
    RegisteredNodeTypes.GetKeys(NodeTypes);
    return NodeTypes;
}

TArray<FString> FNodeFactory::GetNodeTypesByCategory(ENodeCategory Category)
{
    TArray<FString> NodeTypes;
    
    for (const auto& Pair : RegisteredNodeTypes)
    {
        if (Pair.Value.Category == Category)
        {
            NodeTypes.Add(Pair.Key);
        }
    }
    
    return NodeTypes;
}

UClass* FNodeFactory::GetNodeClass(const FString& NodeTypeName)
{
    if (RegisteredNodeTypes.Contains(NodeTypeName))
    {
        return RegisteredNodeTypes[NodeTypeName].NodeClass;
    }
    
    return nullptr;
}

FString FNodeFactory::NodeCategoryToString(ENodeCategory Category)
{
    switch (Category)
    {
        case ENodeCategory::FlowControl: return TEXT("Flow Control");
        case ENodeCategory::Functions: return TEXT("Functions");
        case ENodeCategory::Variables: return TEXT("Variables");
        case ENodeCategory::Events: return TEXT("Events");
        case ENodeCategory::Timers: return TEXT("Timers");
        case ENodeCategory::Math: return TEXT("Math");
        case ENodeCategory::Casting: return TEXT("Casting");
        case ENodeCategory::Arrays: return TEXT("Arrays");
        case ENodeCategory::Strings: return TEXT("Strings");
        case ENodeCategory::Debug: return TEXT("Debug");
        case ENodeCategory::Input: return TEXT("Input");
        case ENodeCategory::Custom: return TEXT("Custom");
        default: return TEXT("Unknown");
    }
}

ENodeCategory FNodeFactory::StringToNodeCategory(const FString& CategoryString)
{
    if (CategoryString == TEXT("Flow Control")) return ENodeCategory::FlowControl;
    if (CategoryString == TEXT("Functions")) return ENodeCategory::Functions;
    if (CategoryString == TEXT("Variables")) return ENodeCategory::Variables;
    if (CategoryString == TEXT("Events")) return ENodeCategory::Events;
    if (CategoryString == TEXT("Timers")) return ENodeCategory::Timers;
    if (CategoryString == TEXT("Math")) return ENodeCategory::Math;
    if (CategoryString == TEXT("Casting")) return ENodeCategory::Casting;
    if (CategoryString == TEXT("Arrays")) return ENodeCategory::Arrays;
    if (CategoryString == TEXT("Strings")) return ENodeCategory::Strings;
    if (CategoryString == TEXT("Debug")) return ENodeCategory::Debug;
    if (CategoryString == TEXT("Input")) return ENodeCategory::Input;
    
    return ENodeCategory::Custom;
}

FVector2D FNodeFactory::GetDefaultNodePosition(UEdGraph* Graph)
{
    // Simple positioning algorithm - could be enhanced
    FVector2D Position(NodePositionCounter * DefaultNodeSpacing, 0.0f);
    NodePositionCounter++;
    return Position;
}

void FNodeFactory::RefreshNode(UK2Node* Node)
{
    if (!Node)
    {
        return;
    }

    Node->ReconstructNode();
    Node->GetGraph()->NotifyGraphChanged();
}

UK2Node* FNodeFactory::CreateNodeOfClass(UEdGraph* Graph, UClass* NodeClass, const FVector2D& Position)
{
    if (!Graph || !NodeClass)
    {
        return nullptr;
    }

    UK2Node* NewNode = NewObject<UK2Node>(Graph, NodeClass);
    if (NewNode)
    {
        Graph->AddNode(NewNode, true);
        NewNode->NodePosX = Position.X;
        NewNode->NodePosY = Position.Y;
        NewNode->AllocateDefaultPins();
    }

    return NewNode;
}

UFunction* FNodeFactory::FindFunction(const FString& FunctionName, const FString& ClassName)
{
    if (ClassName.IsEmpty())
    {
        return nullptr;
    }

    UClass* Class = FindObject<UClass>(ANY_PACKAGE, *ClassName);
    if (!Class)
    {
        return nullptr;
    }

    return Class->FindFunctionByName(FName(*FunctionName));
}

FProperty* FNodeFactory::FindProperty(const FString& PropertyName, UBlueprint* Blueprint)
{
    if (!Blueprint || PropertyName.IsEmpty())
    {
        return nullptr;
    }

    return Blueprint->GeneratedClass->FindPropertyByName(FName(*PropertyName));
}

UBlueprint* FNodeFactory::GetBlueprintFromGraph(UEdGraph* Graph)
{
    if (!Graph)
    {
        return nullptr;
    }

    return FBlueprintEditorUtils::FindBlueprintForGraph(Graph);
}

void FNodeFactory::InitializeBuiltInNodeTypes()
{
    // Flow Control nodes
    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("Branch");
        NodeInfo.NodeClass = UK2Node_IfThenElse::StaticClass();
        NodeInfo.Category = ENodeCategory::FlowControl;
        NodeInfo.Description = TEXT("Conditional execution based on boolean input");
        RegisterNodeType(NodeInfo);
    }

    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("Sequence");
        NodeInfo.NodeClass = UK2Node_ExecutionSequence::StaticClass();
        NodeInfo.Category = ENodeCategory::FlowControl;
        NodeInfo.Description = TEXT("Execute multiple outputs in sequence");
        RegisterNodeType(NodeInfo);
    }

    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("ForLoop");
        NodeInfo.NodeClass = UK2Node_ForLoop::StaticClass();
        NodeInfo.Category = ENodeCategory::FlowControl;
        NodeInfo.Description = TEXT("Loop a specified number of times");
        RegisterNodeType(NodeInfo);
    }

    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("WhileLoop");
        NodeInfo.NodeClass = UK2Node_WhileLoop::StaticClass();
        NodeInfo.Category = ENodeCategory::FlowControl;
        NodeInfo.Description = TEXT("Loop while condition is true");
        RegisterNodeType(NodeInfo);
    }

    // Variable nodes
    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("VariableGet");
        NodeInfo.NodeClass = UK2Node_VariableGet::StaticClass();
        NodeInfo.Category = ENodeCategory::Variables;
        NodeInfo.Description = TEXT("Get the value of a variable");
        RegisterNodeType(NodeInfo);
    }

    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("VariableSet");
        NodeInfo.NodeClass = UK2Node_VariableSet::StaticClass();
        NodeInfo.Category = ENodeCategory::Variables;
        NodeInfo.Description = TEXT("Set the value of a variable");
        RegisterNodeType(NodeInfo);
    }

    // Event nodes
    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("Event");
        NodeInfo.NodeClass = UK2Node_Event::StaticClass();
        NodeInfo.Category = ENodeCategory::Events;
        NodeInfo.Description = TEXT("Built-in event node");
        RegisterNodeType(NodeInfo);
    }

    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("CustomEvent");
        NodeInfo.NodeClass = UK2Node_CustomEvent::StaticClass();
        NodeInfo.Category = ENodeCategory::Events;
        NodeInfo.Description = TEXT("Custom event that can be called");
        RegisterNodeType(NodeInfo);
    }

    // Function nodes
    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("FunctionCall");
        NodeInfo.NodeClass = UK2Node_CallFunction::StaticClass();
        NodeInfo.Category = ENodeCategory::Functions;
        NodeInfo.Description = TEXT("Call a function");
        NodeInfo.bRequiresSpecialConfiguration = true;
        RegisterNodeType(NodeInfo);
    }

    // Timer nodes
    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("Delay");
        NodeInfo.NodeClass = UK2Node_Delay::StaticClass();
        NodeInfo.Category = ENodeCategory::Timers;
        NodeInfo.Description = TEXT("Delay execution for specified time");
        RegisterNodeType(NodeInfo);
    }

    // Casting nodes
    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("Cast");
        NodeInfo.NodeClass = UK2Node_DynamicCast::StaticClass();
        NodeInfo.Category = ENodeCategory::Casting;
        NodeInfo.Description = TEXT("Cast object to specified type");
        RegisterNodeType(NodeInfo);
    }

    // Array nodes
    {
        FNODETYPEINFO NodeInfo;
        NodeInfo.NodeTypeName = TEXT("ForEachLoop");
        NodeInfo.NodeClass = UK2Node_ForEachElementInArray::StaticClass();
        NodeInfo.Category = ENodeCategory::Arrays;
        NodeInfo.Description = TEXT("Loop through each element in an array");
        RegisterNodeType(NodeInfo);
    }

    UE_LOG(LogNodeFactory, Log, TEXT("Initialized %d built-in node types"), RegisteredNodeTypes.Num());
}

void FNodeFactory::LogNodeCreation(const FString& Message, bool bIsError)
{
    if (bIsError)
    {
        UE_LOG(LogNodeFactory, Error, TEXT("%s"), *Message);
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogNodeFactory, Log, TEXT("%s"), *Message);
    }
} 