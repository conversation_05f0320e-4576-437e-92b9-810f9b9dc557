#include "AI/OllamaConnector.h"
#include "UE5BlueprintGeneratorModule.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Dom/JsonObject.h"
#include "Misc/DateTime.h"

DEFINE_LOG_CATEGORY_EXTERN(LogOllamaConnector, Log, All);
DEFINE_LOG_CATEGORY(LogOllamaConnector);

FOllamaConnector::FOllamaConnector()
{
	UE_LOG(LogOllamaConnector, Log, TEXT("Ollama connector initialized"));
}

FOllamaConnector::~FOllamaConnector()
{
	CancelPendingRequests();
	UE_LOG(LogOllamaConnector, Log, TEXT("Ollama connector destroyed"));
}

bool FOllamaConnector::SendRequest(const FAIModelRequest& Request, TFunction<void(const FAIModelResponse&)> OnResponse)
{
	if (Request.Prompt.IsEmpty())
	{
		UE_LOG(LogOllamaConnector, Warning, TEXT("Cannot send request with empty prompt"));
		FAIModelResponse ErrorResponse = CreateErrorResponse(TEXT("Empty prompt provided"));
		OnResponse(ErrorResponse);
		return false;
	}

	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = CreateGenerateRequest(Request);
	if (!HttpRequest.IsValid())
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Failed to create HTTP request"));
		FAIModelResponse ErrorResponse = CreateErrorResponse(TEXT("Failed to create HTTP request"));
		OnResponse(ErrorResponse);
		return false;
	}

	// Bind response handler
	HttpRequest->OnProcessRequestComplete().BindLambda([this, OnResponse](FHttpRequestPtr RequestPtr, FHttpResponsePtr Response, bool bWasSuccessful)
	{
		HandleGenerateResponse(RequestPtr, Response, bWasSuccessful, OnResponse);
	});

	// Send the request
	bool bRequestSent = HttpRequest->ProcessRequest();
	if (bRequestSent)
	{
		PendingRequests.Add(HttpRequest);
		UE_LOG(LogOllamaConnector, Log, TEXT("Ollama generation request sent"));
	}
	else
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Failed to send Ollama request"));
		FAIModelResponse ErrorResponse = CreateErrorResponse(TEXT("Failed to send HTTP request"));
		OnResponse(ErrorResponse);
	}

	return bRequestSent;
}

bool FOllamaConnector::TestConnection(const FAIModelSettings& Settings, TFunction<void(bool, const FString&)> OnResponse)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = CreateTestRequest(Settings);
	if (!HttpRequest.IsValid())
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Failed to create test HTTP request"));
		OnResponse(false, TEXT("Failed to create HTTP request"));
		return false;
	}

	// Bind response handler
	HttpRequest->OnProcessRequestComplete().BindLambda([this, OnResponse](FHttpRequestPtr RequestPtr, FHttpResponsePtr Response, bool bWasSuccessful)
	{
		HandleTestResponse(RequestPtr, Response, bWasSuccessful, OnResponse);
	});

	// Send the request
	bool bRequestSent = HttpRequest->ProcessRequest();
	if (bRequestSent)
	{
		PendingRequests.Add(HttpRequest);
		UE_LOG(LogOllamaConnector, Log, TEXT("Ollama connection test sent"));
	}
	else
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Failed to send Ollama test request"));
		OnResponse(false, TEXT("Failed to send HTTP request"));
	}

	return bRequestSent;
}

bool FOllamaConnector::GetAvailableModels(const FAIModelSettings& Settings, TFunction<void(const TArray<FString>&)> OnResponse)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = CreateModelsRequest(Settings);
	if (!HttpRequest.IsValid())
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Failed to create models HTTP request"));
		TArray<FString> EmptyArray;
		OnResponse(EmptyArray);
		return false;
	}

	// Bind response handler
	HttpRequest->OnProcessRequestComplete().BindLambda([this, OnResponse](FHttpRequestPtr RequestPtr, FHttpResponsePtr Response, bool bWasSuccessful)
	{
		HandleModelsResponse(RequestPtr, Response, bWasSuccessful, OnResponse);
	});

	// Send the request
	bool bRequestSent = HttpRequest->ProcessRequest();
	if (bRequestSent)
	{
		PendingRequests.Add(HttpRequest);
		UE_LOG(LogOllamaConnector, Log, TEXT("Ollama models request sent"));
	}
	else
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Failed to send Ollama models request"));
		TArray<FString> EmptyArray;
		OnResponse(EmptyArray);
	}

	return bRequestSent;
}

void FOllamaConnector::CancelPendingRequests()
{
	for (auto& Request : PendingRequests)
	{
		if (Request.IsValid())
		{
			Request->CancelRequest();
		}
	}
	PendingRequests.Empty();
	UE_LOG(LogOllamaConnector, Log, TEXT("Cancelled %d pending Ollama requests"), PendingRequests.Num());
}

TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> FOllamaConnector::CreateGenerateRequest(const FAIModelRequest& Request)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	HttpRequest->SetURL(GetGenerateEndpoint(Request.Settings));
	HttpRequest->SetVerb(TEXT("POST"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequest->SetTimeout(Request.Settings.TimeoutSeconds);

	// Create JSON payload
	TSharedPtr<FJsonObject> JsonPayload = CreateGenerateRequestJson(Request);
	FString JsonString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
	FJsonSerializer::Serialize(JsonPayload.ToSharedRef(), Writer);

	HttpRequest->SetContentAsString(JsonString);

	UE_LOG(LogOllamaConnector, VeryVerbose, TEXT("Ollama request JSON: %s"), *JsonString);

	return HttpRequest;
}

TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> FOllamaConnector::CreateTestRequest(const FAIModelSettings& Settings)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	HttpRequest->SetURL(GetTagsEndpoint(Settings));
	HttpRequest->SetVerb(TEXT("GET"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequest->SetTimeout(10); // Short timeout for connection test

	return HttpRequest;
}

TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> FOllamaConnector::CreateModelsRequest(const FAIModelSettings& Settings)
{
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	HttpRequest->SetURL(GetTagsEndpoint(Settings));
	HttpRequest->SetVerb(TEXT("GET"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequest->SetTimeout(Settings.TimeoutSeconds);

	return HttpRequest;
}

void FOllamaConnector::HandleGenerateResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const FAIModelResponse&)> OnResponse)
{
	RemoveCompletedRequest(Request);

	FAIModelResponse ModelResponse;
	
	if (!bWasSuccessful || !Response.IsValid())
	{
		ModelResponse = CreateErrorResponse(TEXT("HTTP request failed"));
		UE_LOG(LogOllamaConnector, Error, TEXT("Ollama generation request failed"));
		OnResponse(ModelResponse);
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	if (ResponseCode != 200)
	{
		FString ErrorMsg = FString::Printf(TEXT("HTTP %d: %s"), ResponseCode, *GetHttpErrorMessage(ResponseCode));
		ModelResponse = CreateErrorResponse(ErrorMsg);
		UE_LOG(LogOllamaConnector, Error, TEXT("Ollama returned error: %s"), *ErrorMsg);
		OnResponse(ModelResponse);
		return;
	}

	FString ResponseContent = Response->GetContentAsString();
	UE_LOG(LogOllamaConnector, VeryVerbose, TEXT("Ollama response: %s"), *ResponseContent);

	ModelResponse = ParseGenerateResponse(ResponseContent);
	OnResponse(ModelResponse);
}

void FOllamaConnector::HandleTestResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(bool, const FString&)> OnResponse)
{
	RemoveCompletedRequest(Request);

	if (!bWasSuccessful || !Response.IsValid())
	{
		UE_LOG(LogOllamaConnector, Warning, TEXT("Ollama connection test failed"));
		OnResponse(false, TEXT("Connection failed"));
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	if (ResponseCode == 200)
	{
		UE_LOG(LogOllamaConnector, Log, TEXT("Ollama connection test successful"));
		OnResponse(true, TEXT("Connection successful"));
	}
	else
	{
		FString ErrorMsg = FString::Printf(TEXT("HTTP %d: %s"), ResponseCode, *GetHttpErrorMessage(ResponseCode));
		UE_LOG(LogOllamaConnector, Warning, TEXT("Ollama connection test failed: %s"), *ErrorMsg);
		OnResponse(false, ErrorMsg);
	}
}

void FOllamaConnector::HandleModelsResponse(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, TFunction<void(const TArray<FString>&)> OnResponse)
{
	RemoveCompletedRequest(Request);

	TArray<FString> Models;

	if (!bWasSuccessful || !Response.IsValid())
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Ollama models request failed"));
		OnResponse(Models);
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	if (ResponseCode != 200)
	{
		FString ErrorMsg = FString::Printf(TEXT("HTTP %d: %s"), ResponseCode, *GetHttpErrorMessage(ResponseCode));
		UE_LOG(LogOllamaConnector, Error, TEXT("Ollama models request failed: %s"), *ErrorMsg);
		OnResponse(Models);
		return;
	}

	FString ResponseContent = Response->GetContentAsString();
	Models = ParseModelsResponse(ResponseContent);
	
	UE_LOG(LogOllamaConnector, Log, TEXT("Retrieved %d models from Ollama"), Models.Num());
	OnResponse(Models);
}

TSharedPtr<FJsonObject> FOllamaConnector::CreateGenerateRequestJson(const FAIModelRequest& Request)
{
	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

	JsonObject->SetStringField(TEXT("model"), Request.Settings.ModelName);
	JsonObject->SetStringField(TEXT("prompt"), Request.Prompt);
	JsonObject->SetBoolField(TEXT("stream"), Request.Settings.bUseStreaming);

	// Options object
	TSharedPtr<FJsonObject> OptionsObject = MakeShareable(new FJsonObject);
	OptionsObject->SetNumberField(TEXT("temperature"), Request.Settings.Temperature);
	OptionsObject->SetNumberField(TEXT("top_p"), Request.Settings.TopP);
	OptionsObject->SetNumberField(TEXT("num_predict"), Request.Settings.MaxTokens);
	
	JsonObject->SetObjectField(TEXT("options"), OptionsObject);

	// System prompt if provided
	if (!Request.Settings.SystemPrompt.IsEmpty())
	{
		JsonObject->SetStringField(TEXT("system"), Request.Settings.SystemPrompt);
	}

	return JsonObject;
}

FAIModelResponse FOllamaConnector::ParseGenerateResponse(const FString& ResponseContent)
{
	FAIModelResponse ModelResponse;
	
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ResponseContent);
	
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		ModelResponse = CreateErrorResponse(TEXT("Failed to parse JSON response"));
		return ModelResponse;
	}

	// Check for error in response
	if (JsonObject->HasField(TEXT("error")))
	{
		FString ErrorMessage = JsonObject->GetStringField(TEXT("error"));
		ModelResponse = CreateErrorResponse(ErrorMessage);
		return ModelResponse;
	}

	// Extract response text
	if (JsonObject->HasField(TEXT("response")))
	{
		ModelResponse.Response = JsonObject->GetStringField(TEXT("response"));
		ModelResponse.bSuccess = true;
	}
	else
	{
		ModelResponse = CreateErrorResponse(TEXT("No response field in JSON"));
		return ModelResponse;
	}

	// Extract additional metadata if available
	if (JsonObject->HasField(TEXT("eval_count")))
	{
		ModelResponse.TokensUsed = JsonObject->GetIntegerField(TEXT("eval_count"));
	}

	if (JsonObject->HasField(TEXT("total_duration")))
	{
		int64 DurationNs = JsonObject->GetIntegerField(TEXT("total_duration"));
		ModelResponse.ResponseTime = DurationNs / 1000000000.0f; // Convert nanoseconds to seconds
	}

	return ModelResponse;
}

TArray<FString> FOllamaConnector::ParseModelsResponse(const FString& ResponseContent)
{
	TArray<FString> Models;
	
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ResponseContent);
	
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogOllamaConnector, Error, TEXT("Failed to parse models JSON response"));
		return Models;
	}

	const TArray<TSharedPtr<FJsonValue>>* ModelsArray;
	if (JsonObject->TryGetArrayField(TEXT("models"), ModelsArray))
	{
		for (const auto& ModelValue : *ModelsArray)
		{
			TSharedPtr<FJsonObject> ModelObject = ModelValue->AsObject();
			if (ModelObject.IsValid())
			{
				FString ModelName = ModelObject->GetStringField(TEXT("name"));
				if (!ModelName.IsEmpty())
				{
					Models.Add(ModelName);
				}
			}
		}
	}

	return Models;
}

FString FOllamaConnector::GetGenerateEndpoint(const FAIModelSettings& Settings)
{
	FString BaseURL = Settings.Endpoint;
	if (BaseURL.EndsWith(TEXT("/api/generate")))
	{
		return BaseURL;
	}
	
	// Remove trailing slash if present
	if (BaseURL.EndsWith(TEXT("/")))
	{
		BaseURL = BaseURL.LeftChop(1);
	}
	
	return BaseURL + TEXT("/api/generate");
}

FString FOllamaConnector::GetModelsEndpoint(const FAIModelSettings& Settings)
{
	return GetTagsEndpoint(Settings);
}

FString FOllamaConnector::GetTagsEndpoint(const FAIModelSettings& Settings)
{
	FString BaseURL = Settings.Endpoint;
	
	// Extract base URL from generate endpoint
	if (BaseURL.EndsWith(TEXT("/api/generate")))
	{
		BaseURL = BaseURL.LeftChop(13); // Remove "/api/generate"
	}
	
	// Remove trailing slash if present
	if (BaseURL.EndsWith(TEXT("/")))
	{
		BaseURL = BaseURL.LeftChop(1);
	}
	
	return BaseURL + TEXT("/api/tags");
}

void FOllamaConnector::RemoveCompletedRequest(FHttpRequestPtr Request)
{
	PendingRequests.RemoveAll([Request](const TSharedPtr<IHttpRequest, ESPMode::ThreadSafe>& PendingRequest)
	{
		return PendingRequest == Request;
	});
}

FAIModelResponse FOllamaConnector::CreateErrorResponse(const FString& ErrorMessage)
{
	FAIModelResponse ErrorResponse;
	ErrorResponse.bSuccess = false;
	ErrorResponse.ErrorMessage = ErrorMessage;
	ErrorResponse.Response = TEXT("");
	ErrorResponse.TokensUsed = 0;
	ErrorResponse.ResponseTime = 0.0f;
	return ErrorResponse;
}

FString FOllamaConnector::GetHttpErrorMessage(int32 ResponseCode)
{
	switch (ResponseCode)
	{
		case 400: return TEXT("Bad Request");
		case 401: return TEXT("Unauthorized");
		case 403: return TEXT("Forbidden");
		case 404: return TEXT("Not Found");
		case 500: return TEXT("Internal Server Error");
		case 502: return TEXT("Bad Gateway");
		case 503: return TEXT("Service Unavailable");
		case 504: return TEXT("Gateway Timeout");
		default: return FString::Printf(TEXT("HTTP Error %d"), ResponseCode);
	}
} 