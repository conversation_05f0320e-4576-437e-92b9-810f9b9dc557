#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "BlueprintGeneration/NodeAssemblyEngine.h"
#include "BlueprintGeneration/NodePropertyManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogPropertyAssemblyManager, Log, All);

/**
 * Property assembly mode enumeration
 */
UENUM(BlueprintType)
enum class EPropertyAssemblyMode : uint8
{
    Basic,              // Basic property assignment
    Validated,          // Property assignment with validation
    TypeConverted,      // Property assignment with type conversion
    DefaultManaged,     // Property assignment with default value management
    FullAssembly       // Complete property assembly with all features
};

/**
 * Property validation level enumeration
 */
UENUM(BlueprintType)
enum class EBPGenPropertyValidationLevel : uint8
{
    None,              // No validation
    Basic,             // Basic type and range validation
    Strict,            // Strict validation with constraints
    Advanced,          // Advanced validation with custom rules
    Complete          // Complete validation with all checks
};

/**
 * Property conversion strategy enumeration
 */
UENUM(BlueprintType)
enum class EPropertyConversionStrategy : uint8
{
    None,              // No conversion
    Implicit,          // Implicit type conversion
    Explicit,          // Explicit type conversion
    Safe,              // Safe conversion with fallbacks
    Aggressive        // Aggressive conversion with best effort
};

/**
 * Property assembly instruction structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYASSEMBLYINSTRUCTION
{
    GENERATED_BODY()

    // Instruction identifier
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString InstructionId;

    // Target node identifier
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString NodeId;

    // Property name
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString PropertyName;

    // Property value
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString PropertyValue;

    // Expected property type
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString PropertyType;

    // Assembly mode
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    EPropertyAssemblyMode AssemblyMode = EPropertyAssemblyMode::Basic;

    // Validation level
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    EBPGenPropertyValidationLevel ValidationLevel = EBPGenPropertyValidationLevel::Basic;

    // Conversion strategy
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    EPropertyConversionStrategy ConversionStrategy = EPropertyConversionStrategy::Safe;

    // Priority level
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    ENodeAssemblyPriority Priority = ENodeAssemblyPriority::Normal;

    // Dependencies (other instruction IDs)
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TArray<FString> Dependencies;

    // Custom validation rules
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TMap<FString, FString> ValidationRules;

    // Default value fallback
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString DefaultValue;

    // Allow type conversion
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bAllowTypeConversion = true;

    // Required property (must be set)
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bIsRequired = false;

    // Execution status
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bIsExecuted = false;

    // Error status
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bHasError = false;

    // Error message
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString ErrorMessage;

    FPROPERTYASSEMBLYINSTRUCTION()
    {
        AssemblyMode = EPropertyAssemblyMode::Basic;
        ValidationLevel = EBPGenPropertyValidationLevel::Basic;
        ConversionStrategy = EPropertyConversionStrategy::Safe;
        Priority = ENodeAssemblyPriority::Normal;
        bAllowTypeConversion = true;
        bIsRequired = false;
        bIsExecuted = false;
        bHasError = false;
    }
};

/**
 * Property assembly context structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYASSEMBLYCONTEXT
{
    GENERATED_BODY()

    // Target blueprint
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UBlueprint> Blueprint = nullptr;

    // Target graph
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UEdGraph> Graph = nullptr;

    // Available nodes
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, TObjectPtr<UK2Node>> AvailableNodes;

    // Assembly configuration
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FNodeAssemblyConfig Config;

    // Property assembly start time
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FDateTime AssemblyStartTime;

    // Assembled properties
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, FString> AssembledProperties;

    // Property assembly order
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TArray<FString> AssemblyOrder;

    FPROPERTYASSEMBLYCONTEXT()
    {
        Blueprint = nullptr;
        Graph = nullptr;
        AssemblyStartTime = FDateTime::Now();
    }
};

/**
 * Property assembly result structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYASSEMBLYRESULT
{
    GENERATED_BODY()

    // Assembly success status
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // Instruction identifier
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString InstructionId;

    // Node identifier
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString NodeId;

    // Property name
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString PropertyName;

    // Final property value
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString FinalValue;

    // Original property value
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString OriginalValue;

    // Property type
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString PropertyType;

    // Was type conversion performed
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bTypeConverted = false;

    // Conversion details
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ConversionDetails;

    // Validation performed
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bValidationPerformed = false;

    // Validation passed
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bValidationPassed = false;

    // Validation messages
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> ValidationMessages;

    // Assembly time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float AssemblyTime = 0.0f;

    // Error message
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    // Warnings
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Warnings;

    FPROPERTYASSEMBLYRESULT()
    {
        bSuccess = false;
        bTypeConverted = false;
        bValidationPerformed = false;
        bValidationPassed = false;
        AssemblyTime = 0.0f;
    }
};

/**
 * Property assembly configuration structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYASSEMBLYCONFIG
{
    GENERATED_BODY()

    // Enable property validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableValidation = true;

    // Enable type conversion
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableTypeConversion = true;

    // Enable default value management
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableDefaultValueManagement = true;

    // Enable property caching
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnablePropertyCaching = true;

    // Enable batch processing
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableBatchProcessing = true;

    // Enable error recovery
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableErrorRecovery = true;

    // Enable performance monitoring
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnablePerformanceMonitoring = true;

    // Enable statistics tracking
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    bool bEnableStatistics = true;

    // Default assembly mode
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    EPropertyAssemblyMode DefaultAssemblyMode = EPropertyAssemblyMode::Validated;

    // Default validation level
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    EBPGenPropertyValidationLevel DefaultValidationLevel = EBPGenPropertyValidationLevel::Basic;

    // Default conversion strategy
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assembly")
    EPropertyConversionStrategy DefaultConversionStrategy = EPropertyConversionStrategy::Safe;

    // Assembly timeout (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "300"))
    float AssemblyTimeout = 30.0f;

    // Maximum retry attempts
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Recovery", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MaxRetryAttempts = 3;

    // Retry delay (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Recovery", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float RetryDelay = 1.0f;

    // Maximum batch size
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Processing", meta = (ClampMin = "1", ClampMax = "1000"))
    int32 MaxBatchSize = 100;

    FPROPERTYASSEMBLYCONFIG()
    {
        bEnableValidation = true;
        bEnableTypeConversion = true;
        bEnableDefaultValueManagement = true;
        bEnablePropertyCaching = true;
        bEnableBatchProcessing = true;
        bEnableErrorRecovery = true;
        bEnablePerformanceMonitoring = true;
        bEnableStatistics = true;
        DefaultAssemblyMode = EPropertyAssemblyMode::Validated;
        DefaultValidationLevel = EBPGenPropertyValidationLevel::Basic;
        DefaultConversionStrategy = EPropertyConversionStrategy::Safe;
        AssemblyTimeout = 30.0f;
        MaxRetryAttempts = 3;
        RetryDelay = 1.0f;
        MaxBatchSize = 100;
    }
};

/**
 * Property assembly statistics structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYASSEMBLYSTATISTICS
{
    GENERATED_BODY()

    // Total property assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalAssemblies = 0;

    // Successful assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulAssemblies = 0;

    // Failed assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedAssemblies = 0;

    // Type conversions performed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TypeConversions = 0;

    // Validations performed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 ValidationsPerformed = 0;

    // Validation failures
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 ValidationFailures = 0;

    // Total assembly time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float TotalAssemblyTime = 0.0f;

    // Average assembly time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageAssemblyTime = 0.0f;

    // Assembly mode usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EPropertyAssemblyMode, int32> AssemblyModeUsage;

    // Property type frequency
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<FString, int32> PropertyTypeFrequency;

    // Conversion strategy usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EPropertyConversionStrategy, int32> ConversionStrategyUsage;

    FPROPERTYASSEMBLYSTATISTICS()
    {
        TotalAssemblies = 0;
        SuccessfulAssemblies = 0;
        FailedAssemblies = 0;
        TypeConversions = 0;
        ValidationsPerformed = 0;
        ValidationFailures = 0;
        TotalAssemblyTime = 0.0f;
        AverageAssemblyTime = 0.0f;
    }
};

/**
 * Property type converter structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYTYPECONVERTER
{
    GENERATED_BODY()

    // Source type
    UPROPERTY(BlueprintReadOnly, Category = "Converter")
    FString SourceType;

    // Target type
    UPROPERTY(BlueprintReadOnly, Category = "Converter")
    FString TargetType;

    // Conversion function name
    UPROPERTY(BlueprintReadOnly, Category = "Converter")
    FString ConversionFunction;

    // Conversion priority
    UPROPERTY(BlueprintReadOnly, Category = "Converter")
    int32 Priority = 0;

    // Is lossy conversion
    UPROPERTY(BlueprintReadOnly, Category = "Converter")
    bool bIsLossyConversion = false;

    // Conversion description
    UPROPERTY(BlueprintReadOnly, Category = "Converter")
    FString Description;

    FPROPERTYTYPECONVERTER()
    {
        Priority = 0;
        bIsLossyConversion = false;
    }
};

/**
 * Property validation rule structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYVALIDATIONRULE
{
    GENERATED_BODY()

    // Rule name
    UPROPERTY(BlueprintReadOnly, Category = "Rule")
    FString RuleName;

    // Property type this rule applies to
    UPROPERTY(BlueprintReadOnly, Category = "Rule")
    FString PropertyType;

    // Validation function name
    UPROPERTY(BlueprintReadOnly, Category = "Rule")
    FString ValidationFunction;

    // Rule parameters
    UPROPERTY(BlueprintReadOnly, Category = "Rule")
    TMap<FString, FString> Parameters;

    // Error message template
    UPROPERTY(BlueprintReadOnly, Category = "Rule")
    FString ErrorMessageTemplate;

    // Rule priority
    UPROPERTY(BlueprintReadOnly, Category = "Rule")
    int32 Priority = 0;

    // Is rule enabled
    UPROPERTY(BlueprintReadOnly, Category = "Rule")
    bool bIsEnabled = true;

    FPROPERTYVALIDATIONRULE()
    {
        Priority = 0;
        bIsEnabled = true;
    }
};

/**
 * Delegate declarations for property assembly events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPropertyAssemblyStarted, const FString&, InstructionId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPropertyAssemblyProgress, const FString&, InstructionId, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPropertyAssemblyCompleted, const FPROPERTYASSEMBLYRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPropertyAssemblyFailed, const FString&, InstructionId, const FString&, ErrorMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPropertyValidationFailed, const FString&, PropertyName, const FString&, ValidationError);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnPropertyTypeConverted, const FString&, PropertyName, const FString&, FromType, const FString&, ToType);

/**
 * Property Assembly Manager - Specialized property assembly and configuration system
 * 
 * This class provides comprehensive property assembly capabilities for blueprint nodes,
 * including property validation, type conversion, default value management, and batch processing.
 * It ensures that node properties are correctly configured according to their types and constraints.
 */
class UE5BLUEPRINTGENERATOR_API FPropertyAssemblyManager
{
public:
    FPropertyAssemblyManager();
    virtual ~FPropertyAssemblyManager();

    // Core assembly operations
    bool AssembleProperty(const FPROPERTYASSEMBLYINSTRUCTION& Instruction, FPROPERTYASSEMBLYCONTEXT& Context, FPROPERTYASSEMBLYRESULT& OutResult);
    bool AssembleProperties(const TArray<FPROPERTYASSEMBLYINSTRUCTION>& Instructions, FPROPERTYASSEMBLYCONTEXT& Context, TArray<FPROPERTYASSEMBLYRESULT>& OutResults);
    bool AssembleNodeProperties(const FString& NodeId, const TMap<FString, FString>& Properties, FPROPERTYASSEMBLYCONTEXT& Context, TArray<FPROPERTYASSEMBLYRESULT>& OutResults);

    // Batch assembly operations
    bool AssembleBatch(const TArray<FPROPERTYASSEMBLYINSTRUCTION>& Instructions, FPROPERTYASSEMBLYCONTEXT& Context, TArray<FPROPERTYASSEMBLYRESULT>& OutResults);
    bool AssembleMultipleBatches(const TArray<TArray<FPROPERTYASSEMBLYINSTRUCTION>>& Batches, FPROPERTYASSEMBLYCONTEXT& Context, TArray<FPROPERTYASSEMBLYRESULT>& OutResults);

    // Property validation
    bool ValidateProperty(const FString& PropertyName, const FString& PropertyValue, const FString& PropertyType, TArray<FString>& OutValidationMessages);
    bool ValidatePropertyWithRules(const FString& PropertyName, const FString& PropertyValue, const FString& PropertyType, const TArray<FPropertyValidationRule>& Rules, TArray<FString>& OutValidationMessages);
    bool ValidatePropertyBatch(const TArray<FPROPERTYASSEMBLYINSTRUCTION>& Instructions, TArray<FString>& OutValidationMessages);

    // Type conversion
    bool ConvertPropertyType(const FString& PropertyValue, const FString& SourceType, const FString& TargetType, FString& OutConvertedValue, FString& OutConversionDetails);
    bool CanConvertType(const FString& SourceType, const FString& TargetType);
    TArray<FPropertyTypeConverter> GetAvailableConverters(const FString& SourceType, const FString& TargetType);

    // Default value management
    bool ApplyDefaultValue(const FString& PropertyName, const FString& PropertyType, UK2Node* Node);
    bool GetDefaultValueForProperty(const FString& PropertyName, const FString& PropertyType, FString& OutDefaultValue);
    bool SetDefaultValueForProperty(const FString& PropertyName, const FString& PropertyType, const FString& DefaultValue);

    // Configuration management
    void SetAssemblyConfig(const FPROPERTYASSEMBLYCONFIG& Config);
    FPROPERTYASSEMBLYCONFIG GetAssemblyConfig() const;

    // Context management
    FPROPERTYASSEMBLYCONTEXT CreateAssemblyContext(UBlueprint* Blueprint, UEdGraph* Graph, const TMap<FString, TObjectPtr<UK2Node>>& AvailableNodes);
    bool ValidateAssemblyContext(const FPROPERTYASSEMBLYCONTEXT& Context);

    // Statistics and monitoring
    FPROPERTYASSEMBLYSTATISTICS GetStatistics() const;
    void ResetStatistics();

    // Type converter management
    void RegisterTypeConverter(const FPropertyTypeConverter& Converter);
    void UnregisterTypeConverter(const FString& SourceType, const FString& TargetType);
    TArray<FPropertyTypeConverter> GetRegisteredConverters() const;

    // Validation rule management
    void RegisterValidationRule(const FPropertyValidationRule& Rule);
    void UnregisterValidationRule(const FString& RuleName);
    TArray<FPropertyValidationRule> GetValidationRules(const FString& PropertyType) const;

    // Property caching
    void EnablePropertyCaching(bool bEnable);
    bool IsPropertyCached(const FString& PropertyKey);
    void CacheProperty(const FString& PropertyKey, const FString& PropertyValue);
    bool GetCachedProperty(const FString& PropertyKey, FString& OutPropertyValue);
    void ClearPropertyCache();

    // Event delegates
    FOnPropertyAssemblyStarted OnPropertyAssemblyStarted;
    FOnPropertyAssemblyProgress OnPropertyAssemblyProgress;
    FOnPropertyAssemblyCompleted OnPropertyAssemblyCompleted;
    FOnPropertyAssemblyFailed OnPropertyAssemblyFailed;
    FOnPropertyValidationFailed OnPropertyValidationFailed;
    FOnPropertyTypeConverted OnPropertyTypeConverted;

private:
    // Component references
    TSharedPtr<FNodePropertyManager> PropertyManager;

    // Assembly configuration
    FPROPERTYASSEMBLYCONFIG Config;

    // Assembly statistics
    FPROPERTYASSEMBLYSTATISTICS Statistics;

    // Type converters
    TArray<FPropertyTypeConverter> TypeConverters;

    // Validation rules
    TMap<FString, TArray<FPropertyValidationRule>> ValidationRules;

    // Property cache
    TMap<FString, FString> PropertyCache;
    bool bPropertyCachingEnabled;

    // Performance monitoring
    TMap<FString, FDateTime> AssemblyTimers;

    // Core assembly methods
    bool ExecutePropertyAssembly(const FPROPERTYASSEMBLYINSTRUCTION& Instruction, FPROPERTYASSEMBLYCONTEXT& Context, FPROPERTYASSEMBLYRESULT& OutResult);
    bool ApplyPropertyValue(UK2Node* Node, const FString& PropertyName, const FString& PropertyValue, const FString& PropertyType);

    // Validation methods
    bool ValidatePropertyBasic(const FString& PropertyValue, const FString& PropertyType, TArray<FString>& OutMessages);
    bool ValidatePropertyStrict(const FString& PropertyValue, const FString& PropertyType, TArray<FString>& OutMessages);
    bool ValidatePropertyAdvanced(const FString& PropertyValue, const FString& PropertyType, const TArray<FPropertyValidationRule>& Rules, TArray<FString>& OutMessages);

    // Type conversion methods
    bool ConvertImplicit(const FString& PropertyValue, const FString& SourceType, const FString& TargetType, FString& OutConvertedValue);
    bool ConvertExplicit(const FString& PropertyValue, const FString& SourceType, const FString& TargetType, FString& OutConvertedValue);
    bool ConvertSafe(const FString& PropertyValue, const FString& SourceType, const FString& TargetType, FString& OutConvertedValue);
    bool ConvertAggressive(const FString& PropertyValue, const FString& SourceType, const FString& TargetType, FString& OutConvertedValue);

    // Default value methods
    void InitializeDefaultValues();
    FString GetBuiltInDefaultValue(const FString& PropertyType);

    // Error handling
    bool HandleAssemblyError(const FString& InstructionId, const FString& ErrorMessage, FPROPERTYASSEMBLYCONTEXT& Context);
    bool RetryPropertyAssembly(const FPROPERTYASSEMBLYINSTRUCTION& Instruction, FPROPERTYASSEMBLYCONTEXT& Context, FPROPERTYASSEMBLYRESULT& OutResult);

    // Performance monitoring
    void StartAssemblyTimer(const FString& InstructionId);
    void StopAssemblyTimer(const FString& InstructionId, FPROPERTYASSEMBLYRESULT& Result);
    void UpdateStatistics(const FPROPERTYASSEMBLYRESULT& Result);

    // Event broadcasting
    void BroadcastAssemblyStarted(const FString& InstructionId);
    void BroadcastAssemblyProgress(const FString& InstructionId, float Progress);
    void BroadcastAssemblyCompleted(const FPROPERTYASSEMBLYRESULT& Result);
    void BroadcastAssemblyFailed(const FString& InstructionId, const FString& ErrorMessage);
    void BroadcastValidationFailed(const FString& PropertyName, const FString& ValidationError);
    void BroadcastTypeConverted(const FString& PropertyName, const FString& FromType, const FString& ToType);

    // Utility methods
    FString GenerateInstructionId();
    FString GeneratePropertyKey(const FString& NodeId, const FString& PropertyName);
    bool IsValidPropertyValue(const FString& PropertyValue, const FString& PropertyType);
    FString SanitizePropertyValue(const FString& PropertyValue, const FString& PropertyType);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();
    void InitializeBuiltInConverters();
    void InitializeBuiltInValidationRules();
};

#include "PropertyAssemblyManager.generated.h"