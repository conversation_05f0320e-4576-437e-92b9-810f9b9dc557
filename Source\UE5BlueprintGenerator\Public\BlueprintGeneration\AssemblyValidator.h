#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGeneration/AssemblyCoordinator.h"
#include "BlueprintGeneration/BlueprintAssembler.h"
#include "BlueprintGeneration/GraphValidator.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAssemblyValidator, Log, All);

/**
 * Assembly validation severity enumeration
 */
UENUM(BlueprintType)
enum class EAssemblyValidationSeverity : uint8
{
    Info,           // Informational message
    Warning,        // Warning that doesn't prevent assembly
    Error,          // Error that may cause assembly failure
    Critical,       // Critical error that will cause assembly failure
    Fatal          // Fatal error that prevents any assembly
};

/**
 * Assembly validation category enumeration
 */
UENUM(BlueprintType)
enum class EAssemblyValidationCategory : uint8
{
    General,        // General assembly validation
    Structure,      // Blueprint structure validation
    Dependencies,   // Dependency validation
    Instructions,   // Assembly instruction validation
    Performance,    // Performance-related validation
    Compliance,     // UE5 compliance validation
    Quality,        // Quality assurance validation
    Security       // Security-related validation
};

/**
 * Assembly validation rule enumeration
 */
UENUM(BlueprintType)
enum class EAssemblyValidationRule : uint8
{
    // General rules
    ValidRequest,
    ValidStructure,
    ValidDependencies,
    
    // Instruction rules
    ValidInstructions,
    ValidExecutionOrder,
    ValidConnections,
    
    // Performance rules
    ComplexityLimit,
    InstructionLimit,
    ConnectionLimit,
    
    // Quality rules
    NamingConventions,
    DocumentationPresence,
    ErrorHandling,
    
    // Compliance rules
    UE5Standards,
    BlueprintBestPractices,
    PerformanceGuidelines,
    
    // Security rules
    SafeOperations,
    ValidatedInputs,
    SecureConnections
};

/**
 * Assembly validation issue structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYVALIDATIONISSUE
{
    GENERATED_BODY()

    // Issue identifier
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString IssueId;

    // Validation rule that triggered this issue
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    EAssemblyValidationRule Rule = EAssemblyValidationRule::ValidRequest;

    // Issue severity
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    EAssemblyValidationSeverity Severity = EAssemblyValidationSeverity::Info;

    // Issue category
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    EAssemblyValidationCategory Category = EAssemblyValidationCategory::General;

    // Issue message
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString Message;

    // Detailed description
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString Description;

    // Context information
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString Context;

    // Suggested fix
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString SuggestedFix;

    // Related instruction ID (if applicable)
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString RelatedInstructionId;

    // Issue location (line number, node ID, etc.)
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString Location;

    // Can this issue be auto-fixed?
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    bool bCanAutoFix = false;

    // Issue detection time
    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FDateTime DetectionTime;

    FASSEMBLYVALIDATIONISSUE()
    {
        Rule = EAssemblyValidationRule::ValidRequest;
        Severity = EAssemblyValidationSeverity::Info;
        Category = EAssemblyValidationCategory::General;
        bCanAutoFix = false;
        DetectionTime = FDateTime::Now();
    }
};

/**
 * Assembly validation configuration structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYVALIDATIONCONFIG
{
    GENERATED_BODY()

    // Enable validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bEnableValidation = true;

    // Enable auto-fix for fixable issues
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bEnableAutoFix = true;

    // Stop validation on first critical error
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bStopOnCriticalError = false;

    // Maximum number of issues to report
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation", meta = (ClampMin = "1", ClampMax = "1000"))
    int32 MaxIssues = 100;

    // Minimum severity level to report
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    EAssemblyValidationSeverity MinSeverityLevel = EAssemblyValidationSeverity::Info;

    // Enabled validation categories
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    TArray<EAssemblyValidationCategory> EnabledCategories;

    // Enabled validation rules
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    TArray<EAssemblyValidationRule> EnabledRules;

    // Performance limits
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxInstructions = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConnections = 500;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxComplexityScore = 10.0f;

    FASSEMBLYVALIDATIONCONFIG()
    {
        bEnableValidation = true;
        bEnableAutoFix = true;
        bStopOnCriticalError = false;
        MaxIssues = 100;
        MinSeverityLevel = EAssemblyValidationSeverity::Info;
        MaxInstructions = 1000;
        MaxConnections = 500;
        MaxComplexityScore = 10.0f;

        // Enable all categories by default
        EnabledCategories = {
            EAssemblyValidationCategory::General,
            EAssemblyValidationCategory::Structure,
            EAssemblyValidationCategory::Dependencies,
            EAssemblyValidationCategory::Instructions,
            EAssemblyValidationCategory::Performance,
            EAssemblyValidationCategory::Compliance,
            EAssemblyValidationCategory::Quality
        };

        // Enable all rules by default
        EnabledRules = {
            EAssemblyValidationRule::ValidRequest,
            EAssemblyValidationRule::ValidStructure,
            EAssemblyValidationRule::ValidDependencies,
            EAssemblyValidationRule::ValidInstructions,
            EAssemblyValidationRule::ValidExecutionOrder,
            EAssemblyValidationRule::ValidConnections,
            EAssemblyValidationRule::ComplexityLimit,
            EAssemblyValidationRule::InstructionLimit,
            EAssemblyValidationRule::ConnectionLimit,
            EAssemblyValidationRule::NamingConventions,
            EAssemblyValidationRule::UE5Standards,
            EAssemblyValidationRule::BlueprintBestPractices
        };
    }
};

/**
 * Assembly validation result structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYVALIDATIONRESULT
{
    GENERATED_BODY()

    // Validation success status
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bValidationPassed = false;

    // Overall validation score (0.0 - 1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ValidationScore = 0.0f;

    // Total number of issues found
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 TotalIssues = 0;

    // Issues by severity
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<EAssemblyValidationSeverity, int32> IssuesBySeverity;

    // Issues by category
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<EAssemblyValidationCategory, int32> IssuesByCategory;

    // All validation issues
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FASSEMBLYVALIDATIONISSUE> Issues;

    // Auto-fixed issues
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FASSEMBLYVALIDATIONISSUE> AutoFixedIssues;

    // Validation time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ValidationTime = 0.0f;

    // Validation recommendations
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Recommendations;

    // Quality metrics
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, float> QualityMetrics;

    FASSEMBLYVALIDATIONRESULT()
    {
        bValidationPassed = false;
        ValidationScore = 0.0f;
        TotalIssues = 0;
        ValidationTime = 0.0f;
    }
};

/**
 * Assembly validation statistics structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYVALIDATIONSTATISTICS
{
    GENERATED_BODY()

    // Total validations performed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalValidations = 0;

    // Successful validations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulValidations = 0;

    // Failed validations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedValidations = 0;

    // Average validation time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageValidationTime = 0.0f;

    // Average validation score
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageValidationScore = 0.0f;

    // Most common issues
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EAssemblyValidationRule, int32> CommonIssues;

    // Auto-fix success rate
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AutoFixSuccessRate = 0.0f;

    FASSEMBLYVALIDATIONSTATISTICS()
    {
        TotalValidations = 0;
        SuccessfulValidations = 0;
        FailedValidations = 0;
        AverageValidationTime = 0.0f;
        AverageValidationScore = 0.0f;
        AutoFixSuccessRate = 0.0f;
    }
};

/**
 * Delegate declarations for validation events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyValidationStarted, const FString&, ValidationId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssemblyValidationIssueFound, const FASSEMBLYVALIDATIONISSUE&, Issue, const FString&, ValidationId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssemblyValidationIssueFixed, const FASSEMBLYVALIDATIONISSUE&, Issue, const FString&, ValidationId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyValidationCompleted, const FASSEMBLYVALIDATIONRESULT&, Result);

/**
 * Assembly Validator - Assembly-specific validation system for quality assurance
 * 
 * This class provides comprehensive validation for blueprint assembly processes,
 * ensuring assembly integrity, performance compliance, and quality standards.
 * It validates assembly requests, plans, execution contexts, and results.
 */
class UE5BLUEPRINTGENERATOR_API FAssemblyValidator
{
public:
    FAssemblyValidator();
    virtual ~FAssemblyValidator();

    // Core validation operations
    bool ValidateAssemblyRequest(const FASSEMBLYREQUEST& Request, FASSEMBLYVALIDATIONRESULT& OutResult);
    bool ValidateAssemblyPlan(const FASSEMBLYEXECUTIONPLAN& Plan, FASSEMBLYVALIDATIONRESULT& OutResult);
    bool ValidateAssemblyContext(const FASSEMBLYEXECUTIONCONTEXT& Context, FASSEMBLYVALIDATIONRESULT& OutResult);
    bool ValidateAssemblyResult(const FASSEMBLYRESULT& Result, FASSEMBLYVALIDATIONRESULT& OutResult);

    // Comprehensive validation
    bool ValidateCompleteAssembly(const FASSEMBLYREQUEST& Request, const FASSEMBLYEXECUTIONPLAN& Plan,
                                  const FASSEMBLYEXECUTIONCONTEXT& Context, const FASSEMBLYRESULT& Result,
                                  FASSEMBLYVALIDATIONRESULT& OutResult);

    // Issue management
    bool AutoFixIssues(FASSEMBLYVALIDATIONRESULT& ValidationResult);
    bool CanAutoFixIssue(const FASSEMBLYVALIDATIONISSUE& Issue);
    bool ApplyAutoFix(const FASSEMBLYVALIDATIONISSUE& Issue, FASSEMBLYREQUEST& Request);

    // Configuration
    void SetValidationConfig(const FASSEMBLYVALIDATIONCONFIG& Config);
    FASSEMBLYVALIDATIONCONFIG GetValidationConfig() const;

    // Statistics
    FASSEMBLYVALIDATIONSTATISTICS GetStatistics() const;
    void ResetStatistics();

    // Rule management
    void EnableValidationRule(EAssemblyValidationRule Rule);
    void DisableValidationRule(EAssemblyValidationRule Rule);
    bool IsValidationRuleEnabled(EAssemblyValidationRule Rule) const;

    // Category management
    void EnableValidationCategory(EAssemblyValidationCategory Category);
    void DisableValidationCategory(EAssemblyValidationCategory Category);
    bool IsValidationCategoryEnabled(EAssemblyValidationCategory Category) const;

    // Event delegates
    FOnAssemblyValidationStarted OnAssemblyValidationStarted;
    FOnAssemblyValidationIssueFound OnAssemblyValidationIssueFound;
    FOnAssemblyValidationIssueFixed OnAssemblyValidationIssueFixed;
    FOnAssemblyValidationCompleted OnAssemblyValidationCompleted;

private:
    // Component references
    TSharedPtr<FGraphValidator> GraphValidator;

    // Validation configuration
    FASSEMBLYVALIDATIONCONFIG Config;

    // Validation statistics
    FASSEMBLYVALIDATIONSTATISTICS Statistics;

    // Current validation state
    FString CurrentValidationId;
    FDateTime ValidationStartTime;

    // Request validation methods
    bool ValidateRequestStructure(const FASSEMBLYREQUEST& Request, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateRequestDependencies(const FASSEMBLYREQUEST& Request, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateRequestComplexity(const FASSEMBLYREQUEST& Request, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);

    // Plan validation methods
    bool ValidatePlanInstructions(const FASSEMBLYEXECUTIONPLAN& Plan, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidatePlanExecutionOrder(const FASSEMBLYEXECUTIONPLAN& Plan, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidatePlanConnections(const FASSEMBLYEXECUTIONPLAN& Plan, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidatePlanPerformance(const FASSEMBLYEXECUTIONPLAN& Plan, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);

    // Context validation methods
    bool ValidateContextBlueprint(const FASSEMBLYEXECUTIONCONTEXT& Context, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateContextNodes(const FASSEMBLYEXECUTIONCONTEXT& Context, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateContextConfiguration(const FASSEMBLYEXECUTIONCONTEXT& Context, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);

    // Result validation methods
    bool ValidateResultIntegrity(const FASSEMBLYRESULT& Result, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateResultQuality(const FASSEMBLYRESULT& Result, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateResultCompliance(const FASSEMBLYRESULT& Result, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);

    // Specific validation rules
    bool ValidateNamingConventions(const FASSEMBLYREQUEST& Request, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateUE5Standards(const FASSEMBLYREQUEST& Request, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidateBlueprintBestPractices(const FASSEMBLYREQUEST& Request, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);
    bool ValidatePerformanceGuidelines(const FASSEMBLYEXECUTIONPLAN& Plan, TArray<FASSEMBLYVALIDATIONISSUE>& OutIssues);

    // Auto-fix implementations
    bool AutoFixNamingConventions(const FASSEMBLYVALIDATIONISSUE& Issue, FASSEMBLYREQUEST& Request);
    bool AutoFixInstructionOrder(const FASSEMBLYVALIDATIONISSUE& Issue, FASSEMBLYEXECUTIONPLAN& Plan);
    bool AutoFixConnections(const FASSEMBLYVALIDATIONISSUE& Issue, FASSEMBLYEXECUTIONPLAN& Plan);

    // Issue management
    FASSEMBLYVALIDATIONISSUE CreateIssue(EAssemblyValidationRule Rule, EAssemblyValidationSeverity Severity,
                                         EAssemblyValidationCategory Category, const FString& Message,
                                         const FString& Context = TEXT(""), const FString& SuggestedFix = TEXT(""));
    void AddIssue(const FASSEMBLYVALIDATIONISSUE& Issue, TArray<FASSEMBLYVALIDATIONISSUE>& Issues);
    bool ShouldReportIssue(const FASSEMBLYVALIDATIONISSUE& Issue) const;

    // Validation scoring
    float CalculateValidationScore(const TArray<FASSEMBLYVALIDATIONISSUE>& Issues);
    void CalculateQualityMetrics(const FASSEMBLYVALIDATIONRESULT& Result, TMap<FString, float>& OutMetrics);

    // Statistics management
    void UpdateStatistics(const FASSEMBLYVALIDATIONRESULT& Result);

    // Utility methods
    FString GenerateValidationId();
    void StartValidationTimer();
    void StopValidationTimer(FASSEMBLYVALIDATIONRESULT& Result);
    void BroadcastValidationStarted(const FString& ValidationId);
    void BroadcastIssueFound(const FASSEMBLYVALIDATIONISSUE& Issue, const FString& ValidationId);
    void BroadcastIssueFixed(const FASSEMBLYVALIDATIONISSUE& Issue, const FString& ValidationId);
    void BroadcastValidationCompleted(const FASSEMBLYVALIDATIONRESULT& Result);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();
}; 